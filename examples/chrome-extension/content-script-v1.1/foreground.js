// This script gets injected into any opened page
// whose URL matches the pattern defined in the manifest
// (see "content_script" key).
// Several foreground scripts can be declared
// and injected into the same or different pages.
(() => {
    //======================== npm package code ==========================================
    function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function t(t){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?e(Object(r),!0).forEach((function(e){a(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function n(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}function s(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,s,a=[],u=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(a.push(r.value),a.length!==t);u=!0);}catch(e){c=!0,i=e}finally{try{if(!u&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw i}}return a}}(e,t)||f(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e){return function(e){if(Array.isArray(e))return h(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||f(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){if(e){if("string"==typeof e)return h(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function d(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=f(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}var p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function g(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function y(e){return e&&Object.prototype.hasOwnProperty.call(e,"default")&&1===Object.keys(e).length?e.default:e}var v={exports:{}};!function(e){function t(e){if(e)return function(e){return u(e,t.prototype),e._callbacks=new Map,e}(e);this._callbacks=new Map}t.prototype.on=function(e,t){var n,r=null!==(n=this._callbacks.get(e))&&void 0!==n?n:[];return r.push(t),this._callbacks.set(e,r),this},t.prototype.once=function(e,t){var n=this,r=function r(){n.off(e,r);for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];t.apply(n,o)};return r.fn=t,this.on(e,r),this},t.prototype.off=function(e,t){if(void 0===e&&void 0===t)return this._callbacks.clear(),this;if(void 0===t)return this._callbacks.delete(e),this;var n=this._callbacks.get(e);if(n){var r,i=d(n.entries());try{for(i.s();!(r=i.n()).done;){var o=c(r.value,2),s=o[0],a=o[1];if(a===t||a.fn===t){n.splice(s,1);break}}}catch(e){i.e(e)}finally{i.f()}0===n.length?this._callbacks.delete(e):this._callbacks.set(e,n)}return this},t.prototype.emit=function(e){var t=this._callbacks.get(e);if(t){for(var n=l(t),r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];var s,a=d(n);try{for(a.s();!(s=a.n()).done;){s.value.apply(this,i)}}catch(e){a.e(e)}finally{a.f()}}return this},t.prototype.listeners=function(e){var t;return null!==(t=this._callbacks.get(e))&&void 0!==t?t:[]},t.prototype.listenerCount=function(e){if(e)return this.listeners(e).length;var t,n=0,r=d(this._callbacks.values());try{for(r.s();!(t=r.n()).done;){n+=t.value.length}}catch(e){r.e(e)}finally{r.f()}return n},t.prototype.hasListeners=function(e){return this.listenerCount(e)>0},t.prototype.addEventListener=t.prototype.on,t.prototype.removeListener=t.prototype.off,t.prototype.removeEventListener=t.prototype.off,t.prototype.removeAllListeners=t.prototype.off,e.exports=t}(v);var m=g(v.exports);function I(e){return null!=e&&"object"===r(e)&&!0===e["@@functional/placeholder"]}function b(e){return function t(n){return 0===arguments.length||I(n)?t:e.apply(this,arguments)}}function A(e){return function t(n,r){switch(arguments.length){case 0:return t;case 1:return I(n)?t:b((function(t){return e(n,t)}));default:return I(n)&&I(r)?t:I(n)?b((function(t){return e(t,r)})):I(r)?b((function(t){return e(n,t)})):e(n,r)}}}function k(e){return function t(n,r,i){switch(arguments.length){case 0:return t;case 1:return I(n)?t:A((function(t,r){return e(n,t,r)}));case 2:return I(n)&&I(r)?t:I(n)?A((function(t,n){return e(t,r,n)})):I(r)?A((function(t,r){return e(n,t,r)})):b((function(t){return e(n,r,t)}));default:return I(n)&&I(r)&&I(i)?t:I(n)&&I(r)?A((function(t,n){return e(t,n,i)})):I(n)&&I(i)?A((function(t,n){return e(t,r,n)})):I(r)&&I(i)?A((function(t,r){return e(n,t,r)})):I(n)?b((function(t){return e(t,r,i)})):I(r)?b((function(t){return e(n,t,i)})):I(i)?b((function(t){return e(n,r,t)})):e(n,r,i)}}}function E(e,t){return Object.prototype.hasOwnProperty.call(t,e)}var C=b((function(e){return null===e?"Null":void 0===e?"Undefined":Object.prototype.toString.call(e).slice(8,-1)}));function S(e){return"[object Object]"===Object.prototype.toString.call(e)}function O(e,t,n){if(n||(n=new w),function(e){var t=r(e);return null==e||"object"!=t&&"function"!=t}(e))return e;var i,o=function(r){var i=n.get(e);if(i)return i;for(var o in n.set(e,r),e)Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=t?O(e[o],!0,n):e[o]);return r};switch(C(e)){case"Object":return o(Object.create(Object.getPrototypeOf(e)));case"Array":return o([]);case"Date":return new Date(e.valueOf());case"RegExp":return i=e,new RegExp(i.source,i.flags?i.flags:(i.global?"g":"")+(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.sticky?"y":"")+(i.unicode?"u":"")+(i.dotAll?"s":""));case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":return e.slice();default:return e}}var w=function(){function e(){this.map={},this.length=0}return e.prototype.set=function(e,t){var n=this.hash(e),r=this.map[n];r||(this.map[n]=r=[]),r.push([e,t]),this.length+=1},e.prototype.hash=function(e){var t=[];for(var n in e)t.push(Object.prototype.toString.call(e[n]));return t.join()},e.prototype.get=function(e){if(this.length<=180)for(var t in this.map)for(var n=this.map[t],r=0;r<n.length;r+=1){var i=n[r];if(i[0]===e)return i[1]}else{var o=this.hash(e),s=this.map[o];if(s)for(var a=0;a<s.length;a+=1){var u=s[a];if(u[0]===e)return u[1]}}},e}(),T=b((function(e){return null!=e&&"function"==typeof e.clone?e.clone():O(e,!0)})),_=k((function(e,t,n){var r,i={};for(r in n=n||{},t=t||{})E(r,t)&&(i[r]=E(r,n)?e(r,t[r],n[r]):t[r]);for(r in n)E(r,n)&&!E(r,i)&&(i[r]=n[r]);return i})),R=k((function e(t,n,r){return _((function(n,r,i){return S(r)&&S(i)?e(t,r,i):t(n,r,i)}),n,r)})),P=k((function(e,t,n){return R((function(t,n,r){return e(n,r)}),t,n)})),x={HS:"HubSpot",GA:"GA",HOTJAR:"Hotjar",GOOGLEADS:"GoogleAds",VWO:"VWO",GTM:"GoogleTagManager",BRAZE:"Braze",INTERCOM:"INTERCOM",KEEN:"Keen",KISSMETRICS:"Kissmetrics",CUSTOMERIO:"CustomerIO",CHARTBEAT:"Chartbeat",COMSCORE:"Comscore",FACEBOOK_PIXEL:"FacebookPixel",LOTAME:"Lotame",OPTIMIZELY:"Optimizely",BUGSNAG:"Bugsnag",FULLSTORY:"Fullstory",TVSQUARED:"TVSquared",GA4:"GA4",MOENGAGE:"MoEngage",AM:"Amplitude",PENDO:"Pendo",LYTICS:"Lytics",APPCUES:"Appcues",POSTHOG:"Posthog",KLAVIYO:"Klaviyo",CLEVERTAP:"Clevertap",BINGADS:"BingAds",PINTEREST_TAG:"PinterestTag",ADOBE_ANALYTICS:"AdobeAnalytics",LINKEDIN_INSIGHT_TAG:"LinkedInInsightTag",REDDIT_PIXEL:"RedditPixel",DRIP:"Drip",HEAP:"Heap",CRITEO:"Criteo",MP:"Mixpanel",QUALTRICS:"Qualtrics",PROFITWELL:"ProfitWell",SENTRY:"Sentry",QUANTUMMETRIC:"QuantumMetric",SNAP_PIXEL:"SnapPixel",POST_AFFILIATE_PRO:"PostAffiliatePro",GOOGLE_OPTIMIZE:"GoogleOptimize",LAUNCHDARKLY:"LaunchDarkly",GA360:"GA360",ADROLL:"Adroll",DCM_FLOODLIGHT:"DCMFloodlight",MATOMO:"Matomo",VERO:"Vero",MOUSEFLOW:"Mouseflow",ROCKERBOX:"Rockerbox",CONVERTFLOW:"ConvertFlow",SNAPENGAGE:"SnapEngage",LIVECHAT:"LiveChat",SHYNET:"Shynet",WOOPRA:"Woopra",ROLLBAR:"RollBar",QUORA_PIXEL:"QuoraPixel",JUNE:"June",ENGAGE:"Engage",ITERABLE:"Iterable",YANDEX_METRICA:"YandexMetrica",REFINER:"Refiner",QUALAROO:"Qualaroo",PODSIGHTS:"Podsights",AXEPTIO:"Axeptio",SATISMETER:"Satismeter",MICROSOFT_CLARITY:"MicrosoftClarity",SENDINBLUE:"Sendinblue",OLARK:"Olark",LEMNISK:"Lemnisk",TIKTOK_ADS:"TiktokAds",ACTIVE_CAMPAIGN:"ActiveCampaign",SPRIG:"Sprig",SPOTIFYPIXEL:"SpotifyPixel",COMMANDBAR:"CommandBar",NINETAILED:"Ninetailed"},D="ADOBE_ANALYTICS";a({},"Adobe Analytics","AdobeAnalytics");var L=a(a(a(a(a(a({"Adobe Analytics":D,ADOBEANALYTICS:D,"ADOBE ANALYTICS":D},D,D),"AdobeAnalytics",D),"adobeanalytics",D),"adobe analytics",D),"Adobe analytics",D),"adobe Analytics",D),M="AM";a({},"Amplitude","Amplitude");var B=a(a(a(a({},M,M),"AMPLITUDE",M),"Amplitude",M),"am",M),N="APPCUES";a({},"Appcues","Appcues");var G=a(a(a(a({},N,N),"Appcues",N),"App Cues",N),"appcues",N),F="BINGADS";a({},"Bing Ads","BingAds");var j=a(a(a(a(a(a(a({},F,F),"BingAds",F),"bingads",F),"Bing Ads",F),"Bing ads",F),"bing Ads",F),"bing ads",F),U="BRAZE";a({},"Braze","Braze");var K=a(a(a({},U,U),"Braze",U),"braze",U),Q="BUGSNAG";a({},"Bugsnag","Bugsnag");var z=a(a(a({},Q,Q),"bugsnag",Q),"Bugsnag",Q),H="CHARTBEAT";a({},"Chartbeat","Chartbeat");var q=a(a(a(a(a({},H,H),"Chartbeat",H),"chartbeat",H),"Chart Beat",H),"chart beat",H),V="CLEVERTAP";a({},"CleverTap","Clevertap");var W=a(a(a({},V,V),"Clevertap",V),"clevertap",V),Y="COMSCORE";a({},"Comscore","Comscore");var X=a(a(a(a(a(a({},Y,Y),"Comscore",Y),"Com Score",Y),"com Score",Y),"com score",Y),"Com score",Y),J="CRITEO";a({},"Criteo","Criteo");var $=a(a(a({},J,J),"Criteo",J),"criteo",J),Z="CUSTOMERIO";a({},"Customer IO","CustomerIO");var ee=a(a(a(a(a({},Z,Z),"Customerio",Z),"Customer.io",Z),"CUSTOMER.IO",Z),"customer.io",Z),te="DRIP";a({},"Drip","Drip");var ne=a(a(a({},te,te),"Drip",te),"drip",te),re="FACEBOOK_PIXEL";a({},"Facebook Pixel","FacebookPixel");var ie=a(a(a(a(a(a(a({},re,re),"FB Pixel",re),"Facebook Pixel",re),"facebook pixel",re),"fbpixel",re),"FBPIXEL",re),"FB_PIXEL",re),oe="FULLSTORY";a({},"Fullstory","Fullstory");var se=a(a(a(a(a(a(a(a({},oe,oe),"Fullstory",oe),"FullStory",oe),"full Story",oe),"Full Story",oe),"Full story",oe),"full story",oe),"fullstory",oe),ae="GA";a({},"Google Analytics","GA");var ue=a(a(a(a(a({},ae,ae),"Google Analytics",ae),"GoogleAnalytics",ae),"GOOGLE ANALYTICS",ae),"google analytics",ae),ce="GA4";a({},"Google Analytics 4 (GA4)","GA4");var le=a(a(a(a(a(a(a(a(a(a({},ce,ce),"Google Analytics 4",ce),"Google analytics 4",ce),"google analytics 4",ce),"Google Analytics4",ce),"Google analytics4",ce),"google analytics4",ce),"Google Analytics 4 (GA4)",ce),"google analytics 4 (ga4)",ce),"GoogleAnalytics4",ce),fe="GOOGLEADS";a({},"Google Ads","GoogleAds");var he=a(a(a(a(a(a({},fe,fe),"Google Ads",fe),"GoogleAds",fe),"GOOGLE ADS",fe),"google ads",fe),"googleads",fe),de="GOOGLE_OPTIMIZE";a({},"Google Optimize","GoogleOptimize");var pe=a(a(a(a(a(a(a(a({},de,de),"Google Optimize",de),"GoogleOptimize",de),"Googleoptimize",de),"GOOGLEOPTIMIZE",de),"google optimize",de),"Google optimize",de),"GOOGLE OPTIMIZE",de),ge="GTM";a({},"Google Tag Manager","GoogleTagManager");var ye=a(a(a(a(a({},ge,ge),"Google Tag Manager",ge),"google tag manager",ge),"googletag manager",ge),"googletagmanager",ge),ve="HEAP";a({},"Heap.io","Heap");var me=a(a(a(a({},ve,ve),"Heap",ve),"heap",ve),"Heap.io",ve),Ie="HOTJAR";a({},"Hotjar","Hotjar");var be=a(a(a(a(a({},Ie,Ie),"Hotjar",Ie),"hotjar",Ie),"Hot Jar",Ie),"hot jar",Ie),Ae="HS";a({},"HubSpot","HubSpot");var ke=a(a(a(a(a(a({},Ae,Ae),"Hubspot",Ae),"HUBSPOT",Ae),"hub spot",Ae),"Hub Spot",Ae),"Hub spot",Ae),Ee="INTERCOM";a({},"Intercom","INTERCOM");var Ce=a(a(a({},Ee,Ee),"Intercom",Ee),"intercom",Ee),Se="KEEN";a({},"Keen","Keen");var Oe=a(a(a(a(a({},Se,Se),"Keen",Se),"Keen.io",Se),"keen",Se),"keen.io",Se),we="KISSMETRICS";a({},"Kiss Metrics","Kissmetrics");var Te=a(a(a({},we,we),"Kissmetrics",we),"kissmetrics",we),_e="KLAVIYO";a({},"Klaviyo","Klaviyo");var Re=a(a(a({},_e,_e),"Klaviyo",_e),"klaviyo",_e),Pe="LAUNCHDARKLY";a({},"LaunchDarkly","LaunchDarkly");var xe,De=a(a(a(a(a(a({},Pe,Pe),"LaunchDarkly",Pe),"Launch_Darkly",Pe),"Launch Darkly",Pe),"launchDarkly",Pe),"launch darkly",Pe),Le="LINKEDIN_INSIGHT_TAG";a({},"Linkedin Insight Tag","LinkedInInsightTag");var Me=(a(a(a(a(a(a(a(a(a(a(xe={},Le,Le),"LinkedIn Insight Tag",Le),"LinkedIn insight tag",Le),"linkedIn insight tag",Le),"Linkedin_insight_tag",Le),"LinkedinInsighttag",Le),"LinkedinInsightTag",Le),"LinkedInInsightTag",Le),"Linkedininsighttag",Le),"LINKEDININSIGHTTAG",Le),a(xe,"linkedininsighttag",Le)),Be="LOTAME";a({},"Lotame","Lotame");var Ne=a(a(a({},Be,Be),"Lotame",Be),"lotame",Be),Ge="LYTICS";a({},"Lytics","Lytics");var Fe=a(a(a({},Ge,Ge),"Lytics",Ge),"lytics",Ge),je="MP";a({},"Mixpanel","Mixpanel");var Ue=a(a(a(a(a(a({},je,je),"MIXPANEL",je),"Mixpanel",je),"MIX PANEL",je),"Mix panel",je),"Mix Panel",je),Ke="MOENGAGE";a({},"MoEngage","MoEngage");var Qe=a(a(a(a(a(a(a({},Ke,Ke),"MoEngage",Ke),"moengage",Ke),"Moengage",Ke),"Mo Engage",Ke),"mo engage",Ke),"Mo engage",Ke),ze="OPTIMIZELY";a({},"Optimizely Web","Optimizely");var He=a(a(a({},ze,ze),"Optimizely",ze),"optimizely",ze),qe="PENDO";a({},"Pendo","Pendo");var Ve,We=a(a(a({},qe,qe),"Pendo",qe),"pendo",qe),Ye="PINTEREST_TAG";a({},"Pinterest Tag","PinterestTag");var Xe=(a(a(a(a(a(a(a(a(a(a(Ve={},Ye,Ye),"PinterestTag",Ye),"Pinterest_Tag",Ye),"PINTERESTTAG",Ye),"pinterest",Ye),"PinterestAds",Ye),"Pinterest_Ads",Ye),"Pinterest",Ye),"Pinterest Tag",Ye),"Pinterest tag",Ye),a(a(a(a(Ve,"PINTEREST TAG",Ye),"pinterest tag",Ye),"Pinterest Ads",Ye),"Pinterest ads",Ye)),Je="POST_AFFILIATE_PRO";a({},"Post Affiliate Pro","PostAffiliatePro");var $e=a(a(a(a(a(a(a(a({},Je,Je),"PostAffiliatePro",Je),"Post_affiliate_pro",Je),"Post Affiliate Pro",Je),"Post affiliate pro",Je),"post affiliate pro",Je),"postaffiliatepro",Je),"POSTAFFILIATEPRO",Je),Ze="POSTHOG";a({},"PostHog","Posthog");var et=a(a(a(a(a(a(a({},Ze,Ze),"PostHog",Ze),"Posthog",Ze),"posthog",Ze),"Post Hog",Ze),"Post hog",Ze),"post hog",Ze),tt="PROFITWELL";a({},"ProfitWell","ProfitWell");var nt=a(a(a(a(a(a(a({},tt,tt),"ProfitWell",tt),"profitwell",tt),"Profitwell",tt),"Profit Well",tt),"profit well",tt),"Profit well",tt),rt="QUALTRICS";a({},"Qualtrics","Qualtrics");var it=a(a(a({},rt,rt),"Qualtrics",rt),"qualtrics",rt),ot="QUANTUMMETRIC";a({},"Quantum Metric","QuantumMetric");var st=a(a(a(a(a(a(a(a({},ot,ot),"Quantum Metric",ot),"quantum Metric",ot),"quantum metric",ot),"QuantumMetric",ot),"quantumMetric",ot),"quantummetric",ot),"Quantum_Metric",ot),at="REDDIT_PIXEL";a({},"Reddit Pixel","RedditPixel");var ut=a(a(a(a(a(a(a(a({},at,at),"Reddit_Pixel",at),"RedditPixel",at),"REDDITPIXEL",at),"redditpixel",at),"Reddit Pixel",at),"REDDIT PIXEL",at),"reddit pixel",at),ct="SENTRY";a({},"Sentry","Sentry");var lt=a(a(a({},ct,ct),"sentry",ct),"Sentry",ct),ft="SNAP_PIXEL";a({},"Snap Pixel","SnapPixel");var ht=a(a(a(a(a(a(a(a({},ft,ft),"Snap_Pixel",ft),"SnapPixel",ft),"SNAPPIXEL",ft),"snappixel",ft),"Snap Pixel",ft),"SNAP PIXEL",ft),"snap pixel",ft),dt="TVSQUARED";a({},"TVSquared","TVSquared");var pt=a(a(a(a(a(a(a(a(a({},dt,dt),"TVSquared",dt),"tvsquared",dt),"tvSquared",dt),"TvSquared",dt),"Tvsquared",dt),"TV Squared",dt),"tv squared",dt),"tv Squared",dt),gt="VWO";a({},"VWO","VWO");var yt=a(a(a(a(a(a(a(a({},gt,gt),"VisualWebsiteOptimizer",gt),"Visualwebsiteoptimizer",gt),"visualwebsiteoptimizer",gt),"vwo",gt),"Visual Website Optimizer",gt),"Visual website optimizer",gt),"visual website optimizer",gt),vt="GA360";a({},"Google Analytics 360","GA360");var mt=a(a(a(a(a(a(a(a(a({},vt,vt),"Google Analytics 360",vt),"Google analytics 360",vt),"google analytics 360",vt),"Google Analytics360",vt),"Google analytics360",vt),"google analytics360",vt),"GoogleAnalytics360",vt),"GA 360",vt),It="ADROLL";a({},"Adroll","Adroll");var bt=a(a(a(a(a({},It,It),"Adroll",It),"Ad roll",It),"ad roll",It),"adroll",It),At="DCM_FLOODLIGHT";a({},"DCM Floodlight","DCMFloodlight");var kt=a(a(a(a(a(a(a(a(a({},At,At),"DCM Floodlight",At),"dcm floodlight",At),"Dcm Floodlight",At),"DCMFloodlight",At),"dcmfloodlight",At),"DcmFloodlight",At),"dcm_floodlight",At),"DCM_Floodlight",At),Et="MATOMO";a({},"Matomo","Matomo");var Ct=a(a(a({},Et,Et),"Matomo",Et),"matomo",Et),St="VERO";a({},"Vero","Vero");var Ot=a(a(a({},St,St),"Vero",St),"vero",St),wt="MOUSEFLOW";a({},"Mouseflow","Mouseflow");var Tt=a(a(a(a(a(a(a(a(a({},wt,wt),"Mouseflow",wt),"mouseflow",wt),"mouseFlow",wt),"MouseFlow",wt),"Mouse flow",wt),"mouse flow",wt),"mouse Flow",wt),"Mouse Flow",wt),_t="ROCKERBOX";a({},"Rockerbox","Rockerbox");var Rt=a(a(a(a(a(a(a({},_t,_t),"Rockerbox",_t),"rockerbox",_t),"RockerBox",_t),"Rocker box",_t),"rocker box",_t),"Rocker Box",_t),Pt="CONVERTFLOW";a({},"ConvertFlow","ConvertFlow");var xt=a(a(a(a(a(a(a(a(a(a({},Pt,Pt),"Convertflow",Pt),"convertflow",Pt),"convertFlow",Pt),"ConvertFlow",Pt),"Convert flow",Pt),"convert flow",Pt),"convert Flow",Pt),"Convert Flow",Pt),"CONVERT FLOW",Pt),Dt="SNAPENGAGE";a({},"SnapEngage","SnapEngage");var Lt=a(a(a(a(a(a(a({},Dt,Dt),"SnapEngage",Dt),"Snap_Engage",Dt),"snapengage",Dt),"SNAP ENGAGE",Dt),"Snap Engage",Dt),"snap engage",Dt),Mt="LIVECHAT";a({},"LiveChat","LiveChat");var Bt=a(a(a(a(a(a(a({},Mt,Mt),"LiveChat",Mt),"Live_Chat",Mt),"livechat",Mt),"LIVE CHAT",Mt),"Live Chat",Mt),"live chat",Mt),Nt="SHYNET";a({},"Shynet","Shynet");var Gt=a(a(a(a(a(a(a(a(a({},Nt,Nt),"shynet",Nt),"ShyNet",Nt),"shyNet",Nt),"Shynet",Nt),"shy net",Nt),"Shy Net",Nt),"shy Net",Nt),"Shy net",Nt),Ft="WOOPRA";a({},"Woopra","Woopra");var jt=a(a(a({},Ft,Ft),"Woopra",Ft),"woopra",Ft),Ut="ROLLBAR";a({},"RollBar","RollBar");var Kt=a(a(a(a(a(a(a(a({},Ut,Ut),"RollBar",Ut),"Roll_Bar",Ut),"rollbar",Ut),"Rollbar",Ut),"ROLL BAR",Ut),"Roll Bar",Ut),"roll bar",Ut),Qt="QUORA_PIXEL";a({},"Quora Pixel","QuoraPixel");var zt=a(a(a(a(a(a(a(a(a(a({},Qt,Qt),"Quora Pixel",Qt),"Quora pixel",Qt),"QUORA PIXEL",Qt),"QuoraPixel",Qt),"Quorapixel",Qt),"QUORAPIXEL",Qt),"Quora_Pixel",Qt),"quora_pixel",Qt),"Quora",Qt),Ht="JUNE";a({},"June","June");var qt=a(a(a({},Ht,Ht),"June",Ht),"june",Ht),Vt="ENGAGE";a({},"Engage","Engage");var Wt=a(a(a({},Vt,Vt),"Engage",Vt),"engage",Vt),Yt="ITERABLE";a({},"Iterable","Iterable");var Xt=a(a(a({},Yt,Yt),"Iterable",Yt),"iterable",Yt),Jt="YANDEX_METRICA";a({},"Yandex.Metrica","YandexMetrica");var $t=a(a(a(a(a({},Jt,Jt),"Yandexmetrica",Jt),"yandexmetrica",Jt),"yandexMetrica",Jt),"YandexMetrica",Jt),Zt="REFINER";a({},"Refiner","Refiner");var en=a(a(a({},Zt,Zt),"Refiner",Zt),"refiner",Zt),tn="QUALAROO";a({},"Qualaroo","Qualaroo");var nn=a(a(a({},tn,tn),"Qualaroo",tn),"qualaroo",tn),rn="PODSIGHTS";a({},"Podsights","Podsights");var on=a(a(a(a(a(a(a(a({},rn,rn),"Podsights",rn),"PodSights",rn),"pod Sights",rn),"Pod Sights",rn),"pod sights",rn),"POD SIGHTS",rn),"Pod sights",rn),sn="AXEPTIO";a({},"Axeptio","Axeptio");var an=a(a(a({},sn,sn),"Axeptio",sn),"axeptio",sn),un="SATISMETER";a({},"Satismeter","Satismeter");var cn=a(a(a({},un,un),"Satismeter",un),"SatisMeter",un),ln="MICROSOFT_CLARITY";a({},"Microsoft Clarity","MicrosoftClarity");var fn=a(a(a(a(a(a(a(a(a({},ln,ln),"Microsoft Clarity",ln),"Microsoft clarity",ln),"microsoft clarity",ln),"Microsoft_clarity",ln),"MicrosoftClarity",ln),"MICROSOFTCLARITY",ln),"microsoftclarity",ln),"microsoftClarity",ln),hn="SENDINBLUE";a({},"Sendinblue","Sendinblue");var dn=a(a(a(a({},hn,hn),"Sendinblue",hn),"sendinblue",hn),"SendinBlue",hn),pn="OLARK";a({},"Olark","Olark");var gn=a(a(a({},pn,pn),"Olark",pn),"olark",pn),yn="LEMNISK";a({},"Lemnisk","Lemnisk");var vn=a(a(a(a(a(a(a(a({},yn,yn),"LEMNISK_MARKETING_AUTOMATION",yn),"Lemnisk Marketing Automation",yn),"LemniskMarketingAutomation",yn),"lemniskmarketingautomation",yn),"lemniskMarketingAutomation",yn),"lemnisk",yn),"Lemnisk",yn),mn="TIKTOK_ADS";a({},"TikTok Ads","TiktokAds");var In=a(a(a(a(a(a(a({},mn,mn),"TiktokAds",mn),"Tiktok ads",mn),"Tiktok Ads",mn),"Tik Tok Ads",mn),"tik tok ads",mn),"tiktokads",mn),bn="ACTIVE_CAMPAIGN";a({},"Active Campaign","ActiveCampaign");var An=a(a(a(a(a(a({ActiveCampaign:bn,"Active Campaign":bn,"ACTIVE CAMPAIGN":bn},bn,bn),"activecampaign",bn),"active campaign",bn),"Active campaign",bn),"active Campaign",bn),"active_campaign",bn),kn="SPRIG";a({},"Sprig","Sprig");var En=a(a(a({},kn,kn),"Sprig",kn),"sprig",kn),Cn="SPOTIFYPIXEL";a({},"Spotify Pixel","SpotifyPixel");var Sn=a(a(a(a({},Cn,Cn),"Spotify Pixel",Cn),"spotify pixel",Cn),"SPOTIFY_PIXEL",Cn),On="COMMANDBAR";a({},"CommandBar","CommandBar");var wn=a(a(a(a(a({},On,On),"Command Bar",On),"Commandbar",On),"COMMAND_BAR",On),"commandbar",On),Tn="NINETAILED";a({},"Ninetailed","Ninetailed");var _n=a(a(a(a({},Tn,Tn),"Ninetailed",Tn),"ninetailed",Tn),"NineTailed",Tn),Rn=t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t(t({All:"All"},L),B),G),j),K),z),wn),q),W),X),$),ee),ne),ie),se),ue),le),mt),he),pe),ye),me),be),ke),Ce),Oe),Te),Re),De),Me),Ne),Fe),Ue),Qe),_n),He),We),Xe),$e),et),nt),it),st),ut),lt),ht),pt),yt),bt),kt),Ct),Ot),Tt),xt),Lt),Bt),Gt),jt),Kt),zt),qt),Wt),Xt),Rt),$t),en),nn),on),an),cn),fn),dn),gn),vn),In),An),En),Sn),Pn=4,xn={setLogLevel:function(e){switch(e.toUpperCase()){case"INFO":Pn=1;break;case"DEBUG":Pn=2;break;case"WARN":Pn=3;break;default:Pn=4}},info:function(){var e;Pn<=1&&(e=console).info.apply(e,arguments)},debug:function(){var e;Pn<=2&&(e=console).log.apply(e,arguments)},warn:function(){var e;Pn<=3&&(e=console).warn.apply(e,arguments)},error:function(){var e;Pn<=4&&(e=console).error.apply(e,arguments)}},Dn=1e4,Ln=1e3,Mn="RS_JS_SDK",Bn="Request failed with status:",Nn=[Bn],Gn="errorReporting",Fn=function(e){var t=window.RudderStackGlobals&&window.RudderStackGlobals[Gn];t&&e instanceof Error&&t.notify(e)},jn=function(e,t){try{return JSON.stringify(e,function(e){var t=[];return function(n,i){if(!e||null!=i){if("object"!==r(i)||null===i)return i;for(;t.length>0&&t[t.length-1]!==this;)t.pop();return t.includes(i)?(xn.debug("Circular Reference detected for key: ".concat(n)),"[Circular Reference]"):(t.push(i),i)}}}(t))}catch(e){return xn.warn("Failed to convert the value to a JSON string."),null}},Un=function(e,t,n){var i,o;try{i="string"==typeof e?e:e instanceof Error||e.message?e.message:jn(e)}catch(e){i=""}if("object"===r(o=e)&&null!==o&&"target"in o){if(e.target&&"script"!==e.target.localName)return"";if(e.target.dataset&&(e.target.dataset.loader!==Mn||"true"!==e.target.dataset.isnonnativesdk))return"";if(i="error in script loading:: src::  ".concat(e.target.src," id:: ").concat(e.target.id),"ad-block"===e.target.id)return n.page("RudderJS-Initiated","ad-block page request",{path:"/ad-blocked",title:i},n.sendAdblockPageOptions),""}return"[handleError]::".concat(t||"",' "').concat(i,'"')},Kn=function(e,t,n){var r;try{r=Un(e,t,n)}catch(t){xn.error("[handleError] Exception:: ",t),xn.error("[handleError] Original error:: ",jn(e)),Fn(t)}r&&(xn.error(r),function(e){return!e.message||!Nn.some((function(t){return e.message.includes(t)}))}(e)&&Fn(e))},Qn={exports:{}};var zn,Hn={exports:{}},qn=y(Object.freeze({__proto__:null,default:{}}));function Vn(){return zn||(zn=1,Hn.exports=(e=e||function(e,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==p&&p.crypto&&(n=p.crypto),!n)try{n=qn}catch(e){}var r=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),o={},s=o.lib={},a=s.Base={extend:function(e){var t=i(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},u=s.WordArray=a.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=t?n:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,i=e.sigBytes;if(this.clamp(),r%4)for(var o=0;o<i;o++){var s=n[o>>>2]>>>24-o%4*8&255;t[r+o>>>2]|=s<<24-(r+o)%4*8}else for(var a=0;a<i;a+=4)t[r+a>>>2]=n[a>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(r());return new u.init(t,e)}}),c=o.enc={},l=c.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var o=t[i>>>2]>>>24-i%4*8&255;r.push((o>>>4).toString(16)),r.push((15&o).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new u.init(n,t/2)}},f=c.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var o=t[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(o))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new u.init(n,t)}},h=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},d=s.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=h.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,r=this._data,i=r.words,o=r.sigBytes,s=this.blockSize,a=o/(4*s),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*s,l=e.min(4*c,o);if(c){for(var f=0;f<c;f+=s)this._doProcessBlock(i,f);n=i.splice(0,c),r.sigBytes-=l}return new u.init(n,l)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});s.Hasher=d.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new g.HMAC.init(e,n).finalize(t)}}});var g=o.algo={};return o}(Math),e)),Hn.exports;var e}var Wn,Yn={exports:{}};function Xn(){return Wn||(Wn=1,Yn.exports=(e=Vn(),function(){var t=e,n=t.lib.WordArray;function r(e,t,r){for(var i=[],o=0,s=0;s<t;s++)if(s%4){var a=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;i[o>>>2]|=a<<24-o%4*8,o++}return n.create(i,o)}t.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var i=[],o=0;o<n;o+=3)for(var s=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,a=0;a<4&&o+.75*a<n;a++)i.push(r.charAt(s>>>6*(3-a)&63));var u=r.charAt(64);if(u)for(;i.length%4;)i.push(u);return i.join("")},parse:function(e){var t=e.length,n=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var o=0;o<n.length;o++)i[n.charCodeAt(o)]=o}var s=n.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return r(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64)),Yn.exports;var e}var Jn,$n={exports:{}};function Zn(){return Jn||(Jn=1,$n.exports=(e=Vn(),function(t){var n=e,r=n.lib,i=r.WordArray,o=r.Hasher,s=n.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var u=s.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,i=e[r];e[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,s=e[t+0],u=e[t+1],d=e[t+2],p=e[t+3],g=e[t+4],y=e[t+5],v=e[t+6],m=e[t+7],I=e[t+8],b=e[t+9],A=e[t+10],k=e[t+11],E=e[t+12],C=e[t+13],S=e[t+14],O=e[t+15],w=o[0],T=o[1],_=o[2],R=o[3];w=c(w,T,_,R,s,7,a[0]),R=c(R,w,T,_,u,12,a[1]),_=c(_,R,w,T,d,17,a[2]),T=c(T,_,R,w,p,22,a[3]),w=c(w,T,_,R,g,7,a[4]),R=c(R,w,T,_,y,12,a[5]),_=c(_,R,w,T,v,17,a[6]),T=c(T,_,R,w,m,22,a[7]),w=c(w,T,_,R,I,7,a[8]),R=c(R,w,T,_,b,12,a[9]),_=c(_,R,w,T,A,17,a[10]),T=c(T,_,R,w,k,22,a[11]),w=c(w,T,_,R,E,7,a[12]),R=c(R,w,T,_,C,12,a[13]),_=c(_,R,w,T,S,17,a[14]),w=l(w,T=c(T,_,R,w,O,22,a[15]),_,R,u,5,a[16]),R=l(R,w,T,_,v,9,a[17]),_=l(_,R,w,T,k,14,a[18]),T=l(T,_,R,w,s,20,a[19]),w=l(w,T,_,R,y,5,a[20]),R=l(R,w,T,_,A,9,a[21]),_=l(_,R,w,T,O,14,a[22]),T=l(T,_,R,w,g,20,a[23]),w=l(w,T,_,R,b,5,a[24]),R=l(R,w,T,_,S,9,a[25]),_=l(_,R,w,T,p,14,a[26]),T=l(T,_,R,w,I,20,a[27]),w=l(w,T,_,R,C,5,a[28]),R=l(R,w,T,_,d,9,a[29]),_=l(_,R,w,T,m,14,a[30]),w=f(w,T=l(T,_,R,w,E,20,a[31]),_,R,y,4,a[32]),R=f(R,w,T,_,I,11,a[33]),_=f(_,R,w,T,k,16,a[34]),T=f(T,_,R,w,S,23,a[35]),w=f(w,T,_,R,u,4,a[36]),R=f(R,w,T,_,g,11,a[37]),_=f(_,R,w,T,m,16,a[38]),T=f(T,_,R,w,A,23,a[39]),w=f(w,T,_,R,C,4,a[40]),R=f(R,w,T,_,s,11,a[41]),_=f(_,R,w,T,p,16,a[42]),T=f(T,_,R,w,v,23,a[43]),w=f(w,T,_,R,b,4,a[44]),R=f(R,w,T,_,E,11,a[45]),_=f(_,R,w,T,O,16,a[46]),w=h(w,T=f(T,_,R,w,d,23,a[47]),_,R,s,6,a[48]),R=h(R,w,T,_,m,10,a[49]),_=h(_,R,w,T,S,15,a[50]),T=h(T,_,R,w,y,21,a[51]),w=h(w,T,_,R,E,6,a[52]),R=h(R,w,T,_,p,10,a[53]),_=h(_,R,w,T,A,15,a[54]),T=h(T,_,R,w,u,21,a[55]),w=h(w,T,_,R,I,6,a[56]),R=h(R,w,T,_,O,10,a[57]),_=h(_,R,w,T,v,15,a[58]),T=h(T,_,R,w,C,21,a[59]),w=h(w,T,_,R,g,6,a[60]),R=h(R,w,T,_,k,10,a[61]),_=h(_,R,w,T,d,15,a[62]),T=h(T,_,R,w,b,21,a[63]),o[0]=o[0]+w|0,o[1]=o[1]+T|0,o[2]=o[2]+_|0,o[3]=o[3]+R|0},_doFinalize:function(){var e=this._data,n=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;n[i>>>5]|=128<<24-i%32;var o=t.floor(r/4294967296),s=r;n[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),n[14+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),e.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,u=a.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return a},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,r,i,o,s){var a=e+(t&n|~t&r)+i+s;return(a<<o|a>>>32-o)+t}function l(e,t,n,r,i,o,s){var a=e+(t&r|n&~r)+i+s;return(a<<o|a>>>32-o)+t}function f(e,t,n,r,i,o,s){var a=e+(t^n^r)+i+s;return(a<<o|a>>>32-o)+t}function h(e,t,n,r,i,o,s){var a=e+(n^(t|~r))+i+s;return(a<<o|a>>>32-o)+t}n.MD5=o._createHelper(u),n.HmacMD5=o._createHmacHelper(u)}(Math),e.MD5)),$n.exports;var e}var er,tr={exports:{}},nr={exports:{}};function rr(){return er||(er=1,nr.exports=(a=Vn(),t=(e=a).lib,n=t.WordArray,r=t.Hasher,i=e.algo,o=[],s=i.SHA1=r.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],s=n[2],a=n[3],u=n[4],c=0;c<80;c++){if(c<16)o[c]=0|e[t+c];else{var l=o[c-3]^o[c-8]^o[c-14]^o[c-16];o[c]=l<<1|l>>>31}var f=(r<<5|r>>>27)+u+o[c];f+=c<20?1518500249+(i&s|~i&a):c<40?1859775393+(i^s^a):c<60?(i&s|i&a|s&a)-1894007588:(i^s^a)-899497514,u=a,a=s,s=i<<30|i>>>2,i=r,r=f}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+s|0,n[3]=n[3]+a|0,n[4]=n[4]+u|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}}),e.SHA1=r._createHelper(s),e.HmacSHA1=r._createHmacHelper(s),a.SHA1)),nr.exports;var e,t,n,r,i,o,s,a}var ir,or,sr={exports:{}};function ar(){return or||(or=1,tr.exports=function(e){return n=(t=e).lib,r=n.Base,i=n.WordArray,o=t.algo,s=o.MD5,a=o.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:s,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n,r=this.cfg,o=r.hasher.create(),s=i.create(),a=s.words,u=r.keySize,c=r.iterations;a.length<u;){n&&o.update(n),n=o.update(e).finalize(t),o.reset();for(var l=1;l<c;l++)n=o.finalize(n),o.reset();s.concat(n)}return s.sigBytes=4*u,s}}),t.EvpKDF=function(e,t,n){return a.create(n).compute(e,t)},e.EvpKDF;var t,n,r,i,o,s,a}(Vn(),rr(),(ir||(ir=1,sr.exports=(e=Vn(),void function(){var t=e,n=t.lib.Base,r=t.enc.Utf8;t.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,i=4*n;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),s=this._iKey=t.clone(),a=o.words,u=s.words,c=0;c<n;c++)a[c]^=1549556828,u[c]^=909522486;o.sigBytes=s.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())),sr.exports))),tr.exports;var e}var ur,cr,lr={exports:{}};Qn.exports=function(e){return function(){var t=e,n=t.lib.BlockCipher,r=t.algo,i=[],o=[],s=[],a=[],u=[],c=[],l=[],f=[],h=[],d=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var n=0,r=0;for(t=0;t<256;t++){var p=r^r<<1^r<<2^r<<3^r<<4;p=p>>>8^255&p^99,i[n]=p,o[p]=n;var g=e[n],y=e[g],v=e[y],m=257*e[p]^16843008*p;s[n]=m<<24|m>>>8,a[n]=m<<16|m>>>16,u[n]=m<<8|m>>>24,c[n]=m,m=16843009*v^65537*y^257*g^16843008*n,l[p]=m<<24|m>>>8,f[p]=m<<16|m>>>16,h[p]=m<<8|m>>>24,d[p]=m,n?(n=g^e[e[e[v^g]]],r^=e[e[r]]):n=r=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],g=r.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4,r=4*((this._nRounds=n+6)+1),o=this._keySchedule=[],s=0;s<r;s++)s<n?o[s]=t[s]:(c=o[s-1],s%n?n>6&&s%n==4&&(c=i[c>>>24]<<24|i[c>>>16&255]<<16|i[c>>>8&255]<<8|i[255&c]):(c=i[(c=c<<8|c>>>24)>>>24]<<24|i[c>>>16&255]<<16|i[c>>>8&255]<<8|i[255&c],c^=p[s/n|0]<<24),o[s]=o[s-n]^c);for(var a=this._invKeySchedule=[],u=0;u<r;u++){if(s=r-u,u%4)var c=o[s];else c=o[s-4];a[u]=u<4||s<=4?c:l[i[c>>>24]]^f[i[c>>>16&255]]^h[i[c>>>8&255]]^d[i[255&c]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,a,u,c,i)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,l,f,h,d,o),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,r,i,o,s,a){for(var u=this._nRounds,c=e[t]^n[0],l=e[t+1]^n[1],f=e[t+2]^n[2],h=e[t+3]^n[3],d=4,p=1;p<u;p++){var g=r[c>>>24]^i[l>>>16&255]^o[f>>>8&255]^s[255&h]^n[d++],y=r[l>>>24]^i[f>>>16&255]^o[h>>>8&255]^s[255&c]^n[d++],v=r[f>>>24]^i[h>>>16&255]^o[c>>>8&255]^s[255&l]^n[d++],m=r[h>>>24]^i[c>>>16&255]^o[l>>>8&255]^s[255&f]^n[d++];c=g,l=y,f=v,h=m}g=(a[c>>>24]<<24|a[l>>>16&255]<<16|a[f>>>8&255]<<8|a[255&h])^n[d++],y=(a[l>>>24]<<24|a[f>>>16&255]<<16|a[h>>>8&255]<<8|a[255&c])^n[d++],v=(a[f>>>24]<<24|a[h>>>16&255]<<16|a[c>>>8&255]<<8|a[255&l])^n[d++],m=(a[h>>>24]<<24|a[c>>>16&255]<<16|a[l>>>8&255]<<8|a[255&f])^n[d++],e[t]=g,e[t+1]=y,e[t+2]=v,e[t+3]=m},keySize:8});t.AES=n._createHelper(g)}(),e.AES}(Vn(),Xn(),Zn(),ar(),ur||(ur=1,lr.exports=(cr=Vn(),ar(),void(cr.lib.Cipher||function(e){var t=cr,n=t.lib,r=n.Base,i=n.WordArray,o=n.BufferedBlockAlgorithm,s=t.enc;s.Utf8;var a=s.Base64,u=t.algo.EvpKDF,c=n.Cipher=o.extend({cfg:r.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){o.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?m:y}return function(t){return{encrypt:function(n,r,i){return e(r).encrypt(t,n,r,i)},decrypt:function(n,r,i){return e(r).decrypt(t,n,r,i)}}}}()});n.StreamCipher=c.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var l=t.mode={},f=n.BlockCipherMode=r.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),h=l.CBC=function(){var t=f.extend();function n(t,n,r){var i,o=this._iv;o?(i=o,this._iv=e):i=this._prevBlock;for(var s=0;s<r;s++)t[n+s]^=i[s]}return t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize;n.call(this,e,t,i),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}}),t.Decryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,o=e.slice(t,t+i);r.decryptBlock(e,t),n.call(this,e,t,i),this._prevBlock=o}}),t}(),d=(t.pad={}).Pkcs7={pad:function(e,t){for(var n=4*t,r=n-e.sigBytes%n,o=r<<24|r<<16|r<<8|r,s=[],a=0;a<r;a+=4)s.push(o);var u=i.create(s,r);e.concat(u)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};n.BlockCipher=c.extend({cfg:c.cfg.extend({mode:h,padding:d}),reset:function(){var e;c.reset.call(this);var t=this.cfg,n=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,n&&n.words):(this._mode=e.call(r,this,n&&n.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var p=n.CipherParams=r.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),g=(t.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,n=e.salt;return(n?i.create([1398893684,1701076831]).concat(n).concat(t):t).toString(a)},parse:function(e){var t,n=a.parse(e),r=n.words;return 1398893684==r[0]&&1701076831==r[1]&&(t=i.create(r.slice(2,4)),r.splice(0,4),n.sigBytes-=16),p.create({ciphertext:n,salt:t})}},y=n.SerializableCipher=r.extend({cfg:r.extend({format:g}),encrypt:function(e,t,n,r){r=this.cfg.extend(r);var i=e.createEncryptor(n,r),o=i.finalize(t),s=i.cfg;return p.create({ciphertext:o,key:n,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(n,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),v=(t.kdf={}).OpenSSL={execute:function(e,t,n,r,o){if(r||(r=i.random(8)),o)s=u.create({keySize:t+n,hasher:o}).compute(e,r);else var s=u.create({keySize:t+n}).compute(e,r);var a=i.create(s.words.slice(t),4*n);return s.sigBytes=4*t,p.create({key:s,iv:a,salt:r})}},m=n.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:v}),encrypt:function(e,t,n,r){var i=(r=this.cfg.extend(r)).kdf.execute(n,e.keySize,e.ivSize,r.salt,r.hasher);r.iv=i.iv;var o=y.encrypt.call(this,e,t,i.key,r);return o.mixIn(i),o},decrypt:function(e,t,n,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var i=r.kdf.execute(n,e.keySize,e.ivSize,t.salt,r.hasher);return r.iv=i.iv,y.decrypt.call(this,e,t,i.key,r)}})}()))));var fr=g(Qn.exports),hr={exports:{}};hr.exports=function(e){return e.enc.Utf8}(Vn());var dr=g(hr.exports),pr=function(e){return null!=e&&"object"===r(e)&&!1===Array.isArray(e)},gr=function(e,t,n){if(pr(n)||(n={default:n}),!mr(e))return void 0!==n.default?n.default:e;"number"==typeof t&&(t=String(t));var r=Array.isArray(t),i="string"==typeof t,o=n.separator||".",s=n.joinChar||("string"==typeof o?o:".");if(!i&&!r)return e;if(i&&t in e)return vr(t,e,n)?e[t]:n.default;var a=r?t:function(e,t,n){if("function"==typeof n.split)return n.split(e);return e.split(t)}(t,o,n),u=a.length,c=0;do{var l=a[c];for("number"==typeof l&&(l=String(l));l&&"\\"===l.slice(-1);)l=yr([l.slice(0,-1),a[++c]||""],s,n);if(l in e){if(!vr(l,e,n))return n.default;e=e[l]}else{for(var f=!1,h=c+1;h<u;)if(f=(l=yr([l,a[h++]],s,n))in e){if(!vr(l,e,n))return n.default;e=e[l],c=h-1;break}if(!f)return n.default}}while(++c<u&&mr(e));return c===u?e:n.default};function yr(e,t,n){return"function"==typeof n.join?n.join(e):e[0]+t+e[1]}function vr(e,t,n){return"function"!=typeof n.isValid||n.isValid(e,t)}function mr(e){return pr(e)||Array.isArray(e)||"function"==typeof e}var Ir,br,Ar=g(gr),kr={exports:{}};function Er(){if(br)return Ir;br=1;var e=1e3,t=60*e,n=60*t,i=24*n,o=7*i,s=365.25*i;function a(e,t,n,r){var i=t>=1.5*n;return Math.round(e/n)+" "+r+(i?"s":"")}return Ir=function(u,c){c=c||{};var l=r(u);if("string"===l&&u.length>0)return function(r){if((r=String(r)).length>100)return;var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(r);if(!a)return;var u=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return u*s;case"weeks":case"week":case"w":return u*o;case"days":case"day":case"d":return u*i;case"hours":case"hour":case"hrs":case"hr":case"h":return u*n;case"minutes":case"minute":case"mins":case"min":case"m":return u*t;case"seconds":case"second":case"secs":case"sec":case"s":return u*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return u;default:return}}(u);if("number"===l&&isFinite(u))return c.long?function(r){var o=Math.abs(r);if(o>=i)return a(r,o,i,"day");if(o>=n)return a(r,o,n,"hour");if(o>=t)return a(r,o,t,"minute");if(o>=e)return a(r,o,e,"second");return r+" ms"}(u):function(r){var o=Math.abs(r);if(o>=i)return Math.round(r/i)+"d";if(o>=n)return Math.round(r/n)+"h";if(o>=t)return Math.round(r/t)+"m";if(o>=e)return Math.round(r/e)+"s";return r+"ms"}(u);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(u))},Ir}var Cr=function(e){function t(e){var r,i,o,s=null;function a(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];if(a.enabled){var o=a,s=Number(new Date),u=s-(r||s);o.diff=u,o.prev=r,o.curr=s,r=s,n[0]=t.coerce(n[0]),"string"!=typeof n[0]&&n.unshift("%O");var c=0;n[0]=n[0].replace(/%([a-zA-Z%])/g,(function(e,r){if("%%"===e)return"%";c++;var i=t.formatters[r];if("function"==typeof i){var s=n[c];e=i.call(o,s),n.splice(c,1),c--}return e})),t.formatArgs.call(o,n),(o.log||t.log).apply(o,n)}}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:function(){return null!==s?s:(i!==t.namespaces&&(i=t.namespaces,o=t.enabled(e)),o)},set:function(e){s=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,n){var r=t(this.namespace+(void 0===n?":":n)+e);return r.log=this.log,r}function r(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){var e=[].concat(l(t.names.map(r)),l(t.skips.map(r).map((function(e){return"-"+e})))).join(",");return t.enable(""),e},t.enable=function(e){var n;t.save(e),t.namespaces=e,t.names=[],t.skips=[];var r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(n=0;n<i;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=Er(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((function(n){t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){for(var n=0,r=0;r<e.length;r++)n=(n<<5)-n+e.charCodeAt(r),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t};!function(e,t){var n;t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;var n="color: "+this.color;t.splice(1,0,n,"color: inherit");var r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,(function(e){"%%"!==e&&(r++,"%c"===e&&(i=r))})),t.splice(i,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){var e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(n=!1,function(){n||(n=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||function(){},e.exports=Cr(t),e.exports.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(kr,kr.exports);var Sr=kr.exports,Or=Sr("cookie");function wr(){var e;try{e=document.cookie}catch(e){return"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e.stack||e),{}}return function(e){var t,n={},r=e.split(/ *; */);if(""==r[0])return n;for(var i=0;i<r.length;++i)n[_r((t=r[i].split("="))[0])]=_r(t[1]);return n}(e)}function Tr(e){try{return encodeURIComponent(e)}catch(t){Or("error `encode(%o)` - %o",e,t)}}function _r(e){try{return decodeURIComponent(e)}catch(t){Or("error `decode(%o)` - %o",e,t)}}var Rr=g((function(e,t,n){switch(arguments.length){case 3:case 2:return function(e,t,n){n=n||{};var r=Tr(e)+"="+Tr(t);null==t&&(n.maxage=-1);n.maxage&&(n.expires=new Date(+new Date+n.maxage));n.path&&(r+="; path="+n.path);n.domain&&(r+="; domain="+n.domain);n.expires&&(r+="; expires="+n.expires.toUTCString());n.samesite&&(r+="; samesite="+n.samesite);n.secure&&(r+="; secure");document.cookie=r}(e,t,n);case 1:return function(e){return wr()[e]}(e);default:return wr()}})),Pr={exports:{}},xr=Math.max,Dr=function(e,t){var n=t?t.length:0;if(!n)return[];for(var r=xr(Number(e)||0,0),i=xr(n-r,0),o=new Array(i),s=0;s<i;s+=1)o[s]=t[s+r];return o},Lr=Math.max,Mr=function(e){if(null==e||!e.length)return[];for(var t=new Array(Lr(e.length-2,0)),n=1;n<e.length;n+=1)t[n-1]=e[n];return t},Br=Dr,Nr=Mr,Gr=Object.prototype.hasOwnProperty,Fr=Object.prototype.toString,jr=function(e){return Boolean(e)&&"[object Object]"===Fr.call(e)},Ur=function(e,t,n,r){return Gr.call(t,r)&&void 0===e[r]&&(e[r]=n),t},Kr=function(e,t,n,r){return Gr.call(t,r)&&(jr(e[r])&&jr(n)?e[r]=zr(e[r],n):void 0===e[r]&&(e[r]=n)),t},Qr=function(e,t){if(n=t,!Boolean(n)||"object"!==r(n))return t;var n;e=e||Ur;for(var i=Br(2,arguments),o=0;o<i.length;o+=1)for(var s in i[o])e(t,i[o],i[o][s],s);return t},zr=function(e){return Qr.apply(null,[Kr,e].concat(Nr(arguments)))};Pr.exports=function(e){return Qr.apply(null,[null,e].concat(Nr(arguments)))},Pr.exports.deep=zr;var Hr=g(Pr.exports),qr={exports:{}},Vr=Sr("cookie"),Wr=function(e,t,n){switch(arguments.length){case 3:case 2:return function(e,t,n){n=n||{};var r=Xr(e)+"="+Xr(t);null==t&&(n.maxage=-1);n.maxage&&(n.expires=new Date(+new Date+n.maxage));n.path&&(r+="; path="+n.path);n.domain&&(r+="; domain="+n.domain);n.expires&&(r+="; expires="+n.expires.toUTCString());n.secure&&(r+="; secure");document.cookie=r}(e,t,n);case 1:return function(e){return Yr()[e]}(e);default:return Yr()}};function Yr(){var e;try{e=document.cookie}catch(e){return"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e.stack||e),{}}return function(e){var t,n={},r=e.split(/ *; */);if(""==r[0])return n;for(var i=0;i<r.length;++i)n[Jr((t=r[i].split("="))[0])]=Jr(t[1]);return n}(e)}function Xr(e){try{return encodeURIComponent(e)}catch(t){Vr("error `encode(%o)` - %o",e,t)}}function Jr(e){try{return decodeURIComponent(e)}catch(t){Vr("error `decode(%o)` - %o",e,t)}}!function(e,t){var n=Wr;function r(e){for(var n=t.cookie,r=t.levels(e),i=0;i<r.length;++i){var o="__tld__",s=r[i],a={domain:"."+s};if(n(o,1,a),n(o))return n(o,null,a),s}return""}r.levels=function(e){var t,n,r=("function"!=typeof window.URL?(t=e,(n=document.createElement("a")).href=t,n.hostname):new URL(e).hostname).split("."),i=r[r.length-1],o=[];if(4===r.length&&i===parseInt(i,10))return o;if(r.length<=1)return o;for(var s=r.length-2;s>=0;--s)o.push(r.slice(s).join("."));return o},r.cookie=n,t=e.exports=r}(qr,qr.exports);var $r=g(qr.exports),Zr=new(function(){function e(t){i(this,e),this.cOpts={},this.options(t),this.isSupportAvailable=this.checkSupportAvailability()}return s(e,[{key:"options",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(0===arguments.length)return this.cOpts;var t=".".concat($r(window.location.href));return"."===t&&(t=null),this.cOpts=Hr(e,{maxage:31536e6,path:"/",domain:t,samesite:"Lax"}),e.sameDomainCookiesOnly&&delete this.cOpts.domain,this.cOpts}},{key:"set",value:function(e,t){try{return Rr(e,t,T(this.cOpts)),!0}catch(e){return xn.error(e),!1}}},{key:"get",value:function(e){return Rr(e)}},{key:"remove",value:function(e){try{return Rr(e,null,T(this.cOpts)),!0}catch(e){return!1}}},{key:"checkSupportAvailability",value:function(){var e="test_rudder_cookie";return this.set(e,!0),!!this.get(e)&&(this.remove(e),!0)}}]),e}())({}),ei={exports:{}};ei.exports=function(){function e(e){return e=JSON.stringify(e),!!/^\{[\s\S]*\}$/.test(e)}function t(e){return void 0===e||"function"==typeof e?e+"":JSON.stringify(e)}function n(e){if("string"==typeof e)try{return JSON.parse(e)}catch(t){return e}}function r(e){return"[object Function]"==={}.toString.call(e)}function i(e){return"[object Array]"===Object.prototype.toString.call(e)}function o(e){var t="_Is_Incognit",n="yes";try{e||(e=window.localStorage),e.setItem(t,n),e.removeItem(t)}catch(t){var r={_data:{},setItem:function(e,t){return r._data[e]=String(t)},getItem:function(e){return r._data.hasOwnProperty(e)?r._data[e]:void 0},removeItem:function(e){return delete r._data[e]},clear:function(){return r._data={}}};e=r}finally{e.getItem(t)===n&&e.removeItem(t)}return e}var s=o();function a(){if(!(this instanceof a))return new a}a.prototype={set:function(n,r){if(n&&!e(n))s.setItem(n,t(r));else if(e(n))for(var i in n)this.set(i,n[i]);return this},get:function(e){if(void 0===e){var t={};return this.forEach((function(e,n){return t[e]=n})),t}if("?"===e.charAt(0))return this.has(e.substr(1));var r=arguments;if(r.length>1){for(var i={},o=0,a=r.length;o<a;o++){var u=n(s.getItem(r[o]));this.has(r[o])&&(i[r[o]]=u)}return i}return n(s.getItem(e))},clear:function(){return s.clear(),this},remove:function(e){var t=this.get(e);return s.removeItem(e),t},has:function(e){return{}.hasOwnProperty.call(this.get(),e)},keys:function(){var e=[];return this.forEach((function(t){e.push(t)})),e},forEach:function(e){for(var t=0,n=s.length;t<n;t++){var r=s.key(t);e(r,this.get(r))}return this},search:function(e){for(var t=this.keys(),n={},r=0,i=t.length;r<i;r++)t[r].indexOf(e)>-1&&(n[t[r]]=this.get(t[r]));return n},length:s.length};var u=null;function c(t,n){var o=arguments,s=null;if(u||(u=a()),0===o.length)return u.get();if(1===o.length){if("string"==typeof t)return u.get(t);if(e(t))return u.set(t)}if(2===o.length&&"string"==typeof t){if(!n)return u.remove(t);if(n&&"string"==typeof n)return u.set(t,n);n&&r(n)&&(s=null,s=n(t,u.get(t)),c.set(t,s))}if(2===o.length&&i(t)&&r(n))for(var l=0,f=t.length;l<f;l++)s=n(t[l],u.get(t[l])),c.set(t[l],s);return c}for(var l in a.prototype)c[l]=a.prototype[l];return c}();var ti=g(ei.exports),ni=new(function(){function e(t){i(this,e),this.sOpts={},this.enabled=this.checkSupportAvailability(),this.options(t)}return s(e,[{key:"options",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===arguments.length||(Hr(e,{enabled:!0}),this.enabled=e.enabled&&this.enabled,this.sOpts=e),this.sOpts}},{key:"set",value:function(e,t){return ti.set(e,t)}},{key:"get",value:function(e){return ti.get(e)}},{key:"remove",value:function(e){return ti.remove(e)}},{key:"checkSupportAvailability",value:function(){try{var e="test_rudder_ls";return this.set(e,!0),!!this.get(e)&&(this.remove(e),!0)}catch(e){return!1}}}]),e}())({}),ri=function(e){return(new TextDecoder).decode((t=e,n=globalThis.atob(t).split("").map((function(e){return e.charCodeAt(0)})),new Uint8Array(n)));var t,n},ii="rl_user_id",oi="rl_trait",si="rl_anonymous_id",ai="rl_group_id",ui="rl_group_trait",ci="rl_page_init_referrer",li="rl_page_init_referring_domain",fi="rl_session",hi="rl_auth_token",di="RudderEncrypt:",pi="RS_ENC_v3_",gi="Rudder",yi={segment:"ajs_anonymous_id"};function vi(e){try{return e?JSON.parse(e):null}catch(t){return xn.error(t),e||null}}function mi(e){return e.replace(/^\s+|\s+$/gm,"")}function Ii(e){return e&&"string"==typeof e&&""!==mi(e)?e.substring(0,di.length)===di?fr.decrypt(e.substring(di.length),gi).toString(dr):e.substring(0,pi.length)===pi?ri(e.substring(pi.length)):e:e}for(var bi,Ai=function(){function e(){i(this,e),Zr.isSupportAvailable?this.storage=Zr:(ni.enabled&&(this.storage=ni),this.storage||xn.error("No storage is available :: initializing the SDK without storage"))}return s(e,[{key:"options",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.storage.options(e)}},{key:"setItem",value:function(e,t){var n=jn(t);null!==n&&this.storage.set(e,function(e){return""===mi(e)?e:"".concat(di).concat(fr.encrypt(e,gi).toString())}(n))}},{key:"setStringItem",value:function(e,t){"string"==typeof t?this.setItem(e,t):xn.error("[Storage] ".concat(e," should be string"))}},{key:"setUserId",value:function(e){this.setStringItem(ii,e)}},{key:"setUserTraits",value:function(e){this.setItem(oi,e)}},{key:"setGroupId",value:function(e){this.setStringItem(ai,e)}},{key:"setGroupTraits",value:function(e){this.setItem(ui,e)}},{key:"setAnonymousId",value:function(e){this.setStringItem(si,e)}},{key:"setInitialReferrer",value:function(e){this.setItem(ci,e)}},{key:"setInitialReferringDomain",value:function(e){this.setItem(li,e)}},{key:"setSessionInfo",value:function(e){this.setItem(fi,e)}},{key:"setAuthToken",value:function(e){this.setItem(hi,e)}},{key:"getItem",value:function(e){return vi(Ii(this.storage.get(e)))}},{key:"getUserId",value:function(){return this.getItem(ii)}},{key:"getUserTraits",value:function(){return this.getItem(oi)}},{key:"getGroupId",value:function(){return this.getItem(ai)}},{key:"getGroupTraits",value:function(){return this.getItem(ui)}},{key:"fetchExternalAnonymousId",value:function(e){var t,n=e.toLowerCase();return Object.keys(yi).includes(n)&&"segment"===n?(ni.enabled&&(t=ni.get(yi[n])),!t&&Zr.isSupportAvailable&&(t=Zr.get(yi[n])),t):t}},{key:"getAnonymousId",value:function(e){var t=vi(Ii(this.storage.get(si)));if(t)return t;var n=Ar(e,"autoCapture.source");if(!0===Ar(e,"autoCapture.enabled")&&"string"==typeof n){var r=this.fetchExternalAnonymousId(n);if(r)return r}return t}},{key:"getInitialReferrer",value:function(){return this.getItem(ci)}},{key:"getInitialReferringDomain",value:function(){return this.getItem(li)}},{key:"getSessionInfo",value:function(){return this.getItem(fi)}},{key:"getAuthToken",value:function(){return this.getItem(hi)}},{key:"removeItem",value:function(e){return this.storage.remove(e)}},{key:"removeSessionInfo",value:function(){this.removeItem(fi)}},{key:"clear",value:function(e){this.storage.remove(ii),this.storage.remove(oi),this.storage.remove(ai),this.storage.remove(ui),this.storage.remove(hi),e&&this.storage.remove(si)}}]),e}(),ki=new Ai,Ei=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(document.getElementById(e))return;var r=document.createElement("script");r.src=t,r.async=void 0===n.async||n.async,r.type="text/javascript",r.id=e,!0!==n.skipDatasetAttributes&&(r.setAttribute("data-loader",Mn),void 0!==n.isNonNativeSDK&&r.setAttribute("data-isNonNativeSDK",n.isNonNativeSDK));var i=document.getElementsByTagName("head");if(i.length>0)i[0].insertBefore(r,i[0].firstChild);else{var o=document.getElementsByTagName("script")[0];o.parentNode.insertBefore(r,o)}}catch(o){Kn(o)}},Ci=function(e){return null===e},Si=function(e,t){if(!Array.isArray(e)||!Array.isArray(t))return T(t);var n=T(e);return t.forEach((function(e,t){n[t]=Array.isArray(e)||function(e){return!Ci(e)&&"object"===r(e)&&!Array.isArray(e)}(e)?Oi(n[t],e):e})),n},Oi=function(e,t){return P(Si,e,t)},wi=function(e){return function(e){return!Ci(e)&&"[object Object]"===Object.prototype.toString.call(e)}(e)&&Object.keys(e).length>0},Ti=256,_i=[];Ti--;)_i[Ti]=(Ti+256).toString(16).substring(1);for(var Ri,Pi=4096,xi=[],Di=0;Di<256;Di++)xi[Di]=(Di+256).toString(16).substring(1);function Li(){var e;(!Ri||Di+16>Pi)&&(e=Pi,Ri=crypto.getRandomValues(new Uint8Array(e)),Di=0);for(var t,n=0,r="";n<16;n++)t=Ri[Di+n],r+=6==n?xi[15&t|64]:8==n?xi[63&t|128]:xi[t],1&n&&n>1&&n<11&&(r+="-");return Di+=16,r}var Mi={All:"All",GA:"Google Analytics",GOOGLEADS:"Google Ads",BRAZE:"Braze",CHARTBEAT:"Chartbeat",COMSCORE:"Comscore",CUSTOMERIO:"Customer IO",DCM_Floodlight:"DCM Floodlight",FACEBOOK_PIXEL:"Facebook Pixel",GTM:"Google Tag Manager",HOTJAR:"Hotjar",HS:"HubSpot",INTERCOM:"Intercom",KEEN:"Keen",KISSMETRICS:"Kiss Metrics",LOTAME:"Lotame",VWO:"VWO",OPTIMIZELY:"Optimizely Web",FULLSTORY:"Fullstory",TVSQUARED:"TVSquared",GA4:"Google Analytics 4 (GA4)",MOENGAGE:"MoEngage",AM:"Amplitude",PENDO:"Pendo",LYTICS:"Lytics",APPCUES:"Appcues",POSTHOG:"PostHog",PROFITWELL:"ProfitWell",KLAVIYO:"Klaviyo",CLEVERTAP:"CleverTap",BINGADS:"Bing Ads",PINTEREST_TAG:"Pinterest Tag",SNAP_PIXEL:"Snap Pixel",LINKEDIN_INSIGHT_TAG:"Linkedin Insight Tag",REDDIT_PIXEL:"Reddit Pixel",DRIP:"Drip",HEAP:"Heap.io",CRITEO:"Criteo",MP:"Mixpanel",QUALTRICS:"Qualtrics",SENTRY:"Sentry",GOOGLE_OPTIMIZE:"Google Optimize",POST_AFFILIATE_PRO:"Post Affiliate Pro",LAUNCHDARKLY:"LaunchDarkly",GA360:"Google Analytics 360",ADROLL:"Adroll",VERO:"Vero",MATOMO:"Matomo",MOUSEFLOW:"Mouseflow",ROCKERBOX:"Rockerbox",CONVERTFLOW:"ConvertFlow",SNAPENGAGE:"SnapEngage",LIVECHAT:"LiveChat",SHYNET:"Shynet",WOOPRA:"Woopra",ROLLBAR:"RollBar",QUORA_PIXEL:"Quora Pixel",JUNE:"June",ENGAGE:"Engage",ITERABLE:"Iterable",YANDEX_METRICA:"Yandex.Metrica",REFINER:"Refiner",QUALAROO:"Qualaroo",PODSIGHTS:"Podsights",AXEPTIO:"Axeptio",SATISMETER:"Satismeter",MICROSOFT_CLARITY:"Microsoft Clarity",SENDINBLUE:"Sendinblue",OLARK:"Olark",LEMNISK:"Lemnisk",TIKTOK_ADS:"TikTok Ads",ACTIVE_CAMPAIGN:"ActiveCampaign",SPRIG:"Sprig",SPOTIFYPIXEL:"Spotify Pixel",COMMANDBAR:"CommandBar",NINETAILED:"Ninetailed"},Bi=["anonymous_id","id","sent_at","received_at","timestamp","original_timestamp","event_text","event"],Ni="https://api.rudderstack.com/sourceConfig/?p=npm&v=2.48.6",Gi="v1.1",Fi="js-integrations",ji="".concat("https://cdn.rudderlabs.com","/").concat(Gi,"/").concat(Fi),Ui="https://polyfill-fastly.io/v3/polyfill.min.js?version=3.111.0&features=Number.isNaN%2CURL%2CArray.prototype.find%2CArray.prototype.includes%2CPromise%2CString.prototype.endsWith%2CString.prototype.includes%2CString.prototype.startsWith%2CObject.entries%2CObject.values%2CElement.prototype.dataset%2CString.prototype.replaceAll%2CTextEncoder%2Cnavigator.sendBeacon",Ki=["Lax","None","Strict"],Qi=["US","EU"],zi=["oneTrust","ketch"],Hi=["library","consentManagement"],qi=["none","default","full"],Vi={All:!0};function Wi(e){return e&&e.endsWith("/")?e.replace(/\/+$/,""):e}function Yi(){return window.crypto&&"function"==typeof window.crypto.getRandomValues?Li():function(){var e,t=0,n="";if(!bi||Ti+16>256){for(bi=Array(t=256);t--;)bi[t]=256*Math.random()|0;t=Ti=0}for(;t<16;t++)e=bi[Ti+t],n+=6==t?_i[15&e|64]:8==t?_i[63&e|128]:_i[e],1&t&&t>1&&t<11&&(n+="-");return Ti++,n}()}function Xi(){return(new Date).toISOString()}function Ji(e,t){Object.keys(e).forEach((function(n){e.hasOwnProperty(n)&&(t[n]&&(e[t[n]]=e[n]),"All"!==n&&void 0!==t[n]&&t[n]!==n&&delete e[n])}))}function $i(e){Ji(e,Rn)}function Zi(e){Ji(e,Mi)}function eo(e,t){var n=[];if(!t||0===t.length)return n;var i=!0;void 0!==e.All&&(i=e.All);var o=[];return"string"==typeof t[0]?t.forEach((function(e){o.push({intgName:e,intObj:e})})):"object"===r(t[0])&&t.forEach((function(e){o.push({intgName:e.name,intObj:e})})),o.forEach((function(t){var r=t.intgName,o=t.intObj;if(i){var s=!0;void 0!==e[r]&&!1===Boolean(e[r])&&(s=!1),s&&n.push(o)}else void 0!==e[r]&&!0===Boolean(e[r])&&n.push(o)})),n}var to=function(e){return"string"==typeof e||null==e?e:jn(e)},no=function(e){return"string"==typeof e&&e.trim().length>0},ro=function(e,t,n){try{var r=e.source.dataplanes||{};if(Object.keys(r).length>0){var i=function(e){var t=e?e.residencyServer:void 0;if(t)return"string"==typeof t&&Qi.includes(t.toUpperCase())?t.toUpperCase():void xn.error("Invalid residencyServer input")}(n),o=r[i]||r.US;if(o){var s=function(e){if(Array.isArray(e)&&e.length>0){var t=e.find((function(e){return!0===e.default}));if(t&&no(t.url))return t.url}}(o);if(s)return s}}if(no(t))return t;throw Error("Unable to load the SDK due to invalid data plane url")}catch(e){throw Error(e)}};function io(){var e;return(null===(e=document)||void 0===e?void 0:e.referrer)||"$direct"}function oo(e){var t=e.split("/");return t.length>=3?t[2]:""}function so(){for(var e=document.getElementsByTagName("link"),t=0;e[t];t+=1){var n=e[t];if("canonical"===n.getAttribute("rel"))return n.getAttribute("href")}}function ao(){var e=so(),t=window.location.pathname;if(e)try{t=new URL(e).pathname}catch(e){}var n=window.location,r=n.search,i=n.href,o=document.title,s=function(e){var t,n=so(),r=(t=n?n.includes("?")?n:n+e:window.location.href).indexOf("#");return r>-1?t.slice(0,r):t}(r),a=i,u=io();return{path:t,referrer:u,referring_domain:oo(u),search:r,title:o,url:s,tab_url:a,initial_referrer:ki.getInitialReferrer()||"",initial_referring_domain:ki.getInitialReferringDomain()||""}}for(var uo,co=s((function e(){i(this,e),this.name="RudderLabs JavaScript SDK",this.namespace="com.rudderlabs.javascript",this.version="2.48.6"})),lo=s((function e(){i(this,e),this.name="RudderLabs JavaScript SDK",this.version="2.48.6"})),fo=s((function e(){i(this,e),this.name="",this.version=""})),ho=s((function e(){i(this,e),this.density=0,this.width=0,this.height=0,this.innerWidth=0,this.innerHeight=0})),po=s((function e(){var t,n;i(this,e),this.app=new co,this.traits=null,this.library=new lo,this.userAgent=function(){if("undefined"==typeof navigator)return null;var e=navigator.userAgent,t=navigator.brave;if(t&&Object.getPrototypeOf(t).isBrave){var n=e.match(/(chrome)\/([\w.]+)/i);n&&(e="".concat(e," Brave/").concat(n[2]))}return e}(),this.device=null,this.network=null,this.os=new fo,this.locale="undefined"==typeof navigator?null:navigator.language||navigator.browserLanguage,this.screen=(t=new ho,"undefined"==typeof window||(t.width=window.screen.width,t.height=window.screen.height,t.density=window.devicePixelRatio,t.innerWidth=window.innerWidth,t.innerHeight=window.innerHeight),t),this.timezone=(n=(new Date).toString().match(/([A-Z]+[+-]\d+)/))&&n[1]?n[1]:"NA"})),go=function(){function e(){i(this,e),this.channel="web",this.context=new po,this.type=null,this.messageId=Yi(),this.originalTimestamp=(new Date).toISOString(),this.anonymousId=null,this.userId=null,this.event=null,this.properties={}}return s(e,[{key:"getProperty",value:function(e){return this.properties[e]}},{key:"addProperty",value:function(e,t){this.properties[e]=t}}]),e}(),yo=function(){function e(){i(this,e),this.message=new go}return s(e,[{key:"setType",value:function(e){this.message.type=e}},{key:"setProperty",value:function(e){this.message.properties=e}},{key:"setUserProperty",value:function(e){this.message.user_properties=e}},{key:"setUserId",value:function(e){this.message.userId=e}},{key:"setEventName",value:function(e){this.message.event=e}},{key:"getElementContent",value:function(){return this.message}}]),e}(),vo=function(){function e(){i(this,e),this.rudderProperty=null,this.rudderUserProperty=null,this.event=null,this.userId=null,this.type=null}return s(e,[{key:"setType",value:function(e){return this.type=e,this}},{key:"build",value:function(){var e=new yo;return e.setUserId(this.userId),e.setType(this.type),e.setEventName(this.event),e.setProperty(this.rudderProperty),e.setUserProperty(this.rudderUserProperty),e}}]),e}(),mo={},Io=256,bo=[];Io--;)bo[Io]=(Io+256).toString(16).substring(1);mo.v4=function(){var e,t=0,n="";if(!uo||Io+16>256){for(uo=Array(t=256);t--;)uo[t]=256*Math.random()|0;t=Io=0}for(;t<16;t++)e=uo[Io+t],n+=6==t?bo[15&e|64]:8==t?bo[63&e|128]:bo[e],1&t&&t>1&&t<11&&(n+="-");return Io++,n};var Ao={},ko=Object.prototype.hasOwnProperty,Eo=String.prototype.charAt,Co=Object.prototype.toString,So=function(e,t){return Eo.call(e,t)},Oo=function(e,t){return ko.call(e,t)},wo=function(e,t){t=t||Oo;for(var n=[],r=0,i=e.length;r<i;r+=1)t(e,r)&&n.push(String(r));return n},To=function(e){return null==e?[]:(t=e,"[object String]"===Co.call(t)?wo(e,So):function(e){return null!=e&&"function"!=typeof e&&"number"==typeof e.length}(e)?wo(e,Oo):function(e,t){t=t||Oo;var n=[];for(var r in e)t(e,r)&&n.push(String(r));return n}(e));var t},_o=To,Ro=mo.v4,Po={_data:{},length:0,setItem:function(e,t){return this._data[e]=t,this.length=_o(this._data).length,t},getItem:function(e){return e in this._data?this._data[e]:null},removeItem:function(e){return e in this._data&&delete this._data[e],this.length=_o(this._data).length,null},clear:function(){this._data={},this.length=0},key:function(e){return _o(this._data)[e]}};Ao.defaultEngine=function(){try{if(!window.localStorage)return!1;var e=Ro();window.localStorage.setItem(e,"test_value");var t=window.localStorage.getItem(e);return window.localStorage.removeItem(e),"test_value"===t}catch(e){return!1}}()?window.localStorage:Po,Ao.inMemoryEngine=Po;var xo=To,Do=Object.prototype.toString,Lo="function"==typeof Array.isArray?Array.isArray:function(e){return"[object Array]"===Do.call(e)},Mo=function(e){return null!=e&&(Lo(e)||"function"!==e&&function(e){var t=r(e);return"number"===t||"object"===t&&"[object Number]"===Do.call(e)}(e.length))},Bo=function(e,t){for(var n=0;n<t.length&&!1!==e(t[n],n,t);n+=1);},No=function(e,t){for(var n=xo(t),r=0;r<n.length&&!1!==e(t[n[r]],n[r],t);r+=1);},Go=function(e,t){return(Mo(t)?Bo:No).call(this,e,t)},Fo=Ao.defaultEngine,jo=Ao.inMemoryEngine,Uo=Go,Ko=To,Qo=JSON;function zo(e){try{return JSON.stringify(e,(t=[],function(e,n){if("object"!==r(n)||null===n)return n;for(;t.length>0&&t[t.length-1]!==this;)t.pop();return t.includes(n)?"[Circular Reference]":(t.push(n),n)}))}catch(e){return null}var t}function Ho(e,t,n,r){this.id=t,this.name=e,this.keys=n||{},this.engine=r||Fo,this.originalEngine=this.engine}Ho.prototype.set=function(e,t){var n=this._createValidKey(e);if(n)try{this.engine.setItem(n,zo(t))}catch(n){(function(e){var t=!1;if(e.code)switch(e.code){case 22:t=!0;break;case 1014:"NS_ERROR_DOM_QUOTA_REACHED"===e.name&&(t=!0)}else-2147024882===e.number&&(t=!0);return t})(n)&&(this._swapEngine(),this.set(e,t))}},Ho.prototype.get=function(e){try{var t=this.engine.getItem(this._createValidKey(e));return null===t?null:Qo.parse(t)}catch(e){return null}},Ho.prototype.getOriginalEngine=function(){return this.originalEngine},Ho.prototype.remove=function(e){this.engine.removeItem(this._createValidKey(e))},Ho.prototype._createValidKey=function(e){var t,n=this.name,r=this.id;return Ko(this.keys).length?(Uo((function(i){i===e&&(t=[n,r,e].join("."))}),this.keys),t):[n,r,e].join(".")},Ho.prototype._swapEngine=function(){var e=this;Uo((function(t){var n=e.get(t);jo.setItem([e.name,e.id,t].join("."),n),e.remove(t)}),this.keys),this.engine=jo};var qo=Ho;var Vo=Go,Wo={setTimeout:function(e,t){return window.setTimeout(e,t)},clearTimeout:function(e){return window.clearTimeout(e)},Date:window.Date},Yo=Wo,Xo={ASAP:1,RESCHEDULE:2,ABANDON:3};function Jo(){this.tasks={},this.nextId=1}Jo.prototype.now=function(){return+new Yo.Date},Jo.prototype.run=function(e,t,n){var r=this.nextId++;return this.tasks[r]=Yo.setTimeout(this._handle(r,e,t,n||Xo.ASAP),t),r},Jo.prototype.cancel=function(e){this.tasks[e]&&(Yo.clearTimeout(this.tasks[e]),delete this.tasks[e])},Jo.prototype.cancelAll=function(){Vo(Yo.clearTimeout,this.tasks),this.tasks={}},Jo.prototype._handle=function(e,t,n,r){var i=this,o=i.now();return function(){if(delete i.tasks[e],!(r>=Xo.RESCHEDULE&&o+2*n<i.now()))return t();r===Xo.RESCHEDULE&&i.run(t,n,r)}},Jo.setClock=function(e){Yo=e},Jo.resetClock=function(){Yo=Wo},Jo.Modes=Xo;var $o=Jo,Zo={exports:{}};!function(e){function t(e){if(e)return function(e){for(var n in t.prototype)e[n]=t.prototype[n];return e}(e)}Zo.exports=t,t.prototype.on=t.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},t.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},t.prototype.off=t.prototype.removeListener=t.prototype.removeAllListeners=t.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<r.length;i++)if((n=r[i])===t||n.fn===t){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},t.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var i=(n=n.slice(0)).length;r<i;++r)n[r].apply(this,t)}return this},t.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},t.prototype.hasListeners=function(e){return!!this.listeners(e).length}}();var es=Zo.exports,ts=mo.v4,ns=qo,rs=Go,is=$o,os=Sr("localstorage-retry");function ss(e,t){return function(){return e.apply(t,arguments)}}function as(e,t,n){"function"==typeof t&&(n=t),this.name=e,this.id=ts(),this.fn=n,this.maxItems=t.maxItems||1/0,this.maxAttempts=t.maxAttempts||1/0,this.backoff={MIN_RETRY_DELAY:t.minRetryDelay||1e3,MAX_RETRY_DELAY:t.maxRetryDelay||3e4,FACTOR:t.backoffFactor||2,JITTER:t.backoffJitter||0},this.timeouts={ACK_TIMER:1e3,RECLAIM_TIMER:3e3,RECLAIM_TIMEOUT:1e4,RECLAIM_WAIT:500},this.keys={IN_PROGRESS:"inProgress",QUEUE:"queue",RECLAIM_START:"reclaimStart",RECLAIM_END:"reclaimEnd",ACK:"ack"},this._schedule=new is,this._processId=0,this._store=new ns(this.name,this.id,this.keys),this._store.set(this.keys.IN_PROGRESS,{}),this._store.set(this.keys.QUEUE,[]),this._ack=ss(this._ack,this),this._checkReclaim=ss(this._checkReclaim,this),this._processHead=ss(this._processHead,this),this._running=!1}es(as.prototype),as.prototype.start=function(){this._running&&this.stop(),this._running=!0,this._ack(),this._checkReclaim(),this._processHead()},as.prototype.stop=function(){this._schedule.cancelAll(),this._running=!1},as.prototype.shouldRetry=function(e,t){return!(t>this.maxAttempts)},as.prototype.getDelay=function(e){var t=this.backoff.MIN_RETRY_DELAY*Math.pow(this.backoff.FACTOR,e);if(this.backoff.JITTER){var n=Math.random(),r=Math.floor(n*this.backoff.JITTER*t);Math.floor(10*n)<5?t-=r:t+=r}return Number(Math.min(t,this.backoff.MAX_RETRY_DELAY).toPrecision(1))},as.prototype.addItem=function(e){this._enqueue({item:e,attemptNumber:0,time:this._schedule.now(),id:ts()})},as.prototype.requeue=function(e,t,n,r){this.shouldRetry(e,t,n)?this._enqueue({item:e,attemptNumber:t,time:this._schedule.now()+this.getDelay(t),id:r||ts()}):this.emit("discard",e,t)},as.prototype._enqueue=function(e){var t=this._store.get(this.keys.QUEUE)||[];(t=t.slice(-(this.maxItems-1))).push(e),t=t.sort((function(e,t){return e.time-t.time})),this._store.set(this.keys.QUEUE,t),this._running&&this._processHead()},as.prototype._processHead=function(){var e=this,t=this._store;this._schedule.cancel(this._processId);var n=t.get(this.keys.QUEUE)||[],r=t.get(this.keys.IN_PROGRESS)||{},i=this._schedule.now(),o=[];function s(n,r){o.push({item:n.item,done:function(i,o){var s=t.get(e.keys.IN_PROGRESS)||{};delete s[r],t.set(e.keys.IN_PROGRESS,s),e.emit("processed",i,o,n.item),i&&e.requeue(n.item,n.attemptNumber+1,i,n.id)}})}for(var a=Object.keys(r).length;n.length&&n[0].time<=i&&a++<e.maxItems;){var u=n.shift(),c=ts();r[c]={item:u.item,attemptNumber:u.attemptNumber,time:e._schedule.now()},s(u,c)}t.set(this.keys.QUEUE,n),t.set(this.keys.IN_PROGRESS,r),rs((function(t){try{e.fn(t.item,t.done)}catch(e){os("Process function threw error: "+e)}}),o),n=t.get(this.keys.QUEUE)||[],this._schedule.cancel(this._processId),n.length>0&&(this._processId=this._schedule.run(this._processHead,n[0].time-i,is.Modes.ASAP))},as.prototype._ack=function(){this._store.set(this.keys.ACK,this._schedule.now()),this._store.set(this.keys.RECLAIM_START,null),this._store.set(this.keys.RECLAIM_END,null),this._schedule.run(this._ack,this.timeouts.ACK_TIMER,is.Modes.ASAP)},as.prototype._checkReclaim=function(){var e=this;rs((function(t){t.id!==e.id&&(e._schedule.now()-t.get(e.keys.ACK)<e.timeouts.RECLAIM_TIMEOUT||function(t){t.set(e.keys.RECLAIM_START,e.id),t.set(e.keys.ACK,e._schedule.now()),e._schedule.run((function(){t.get(e.keys.RECLAIM_START)===e.id&&(t.set(e.keys.RECLAIM_END,e.id),e._schedule.run((function(){t.get(e.keys.RECLAIM_END)===e.id&&t.get(e.keys.RECLAIM_START)===e.id&&e._reclaim(t.id)}),e.timeouts.RECLAIM_WAIT,is.Modes.ABANDON))}),e.timeouts.RECLAIM_WAIT,is.Modes.ABANDON)}(t))}),function(t){for(var n=[],r=e._store.getOriginalEngine(),i=0;i<r.length;i++){var o=r.key(i);if(null!==o){var s=o.split(".");3===s.length&&s[0]===t&&"ack"===s[2]&&n.push(new ns(t,s[1],e.keys))}}return n}(this.name)),this._schedule.run(this._checkReclaim,this.timeouts.RECLAIM_TIMER,is.Modes.RESCHEDULE)},as.prototype._reclaim=function(e){var t=this,n=new ns(this.name,e,this.keys),r={queue:this._store.get(this.keys.QUEUE)||[]},i={inProgress:n.get(this.keys.IN_PROGRESS)||{},queue:n.get(this.keys.QUEUE)||[]},o=[],s=function(e,n){rs((function(e){var i=e.id||ts();o.indexOf(i)>=0?t.emit("duplication",e.item,e.attemptNumber):(r.queue.push({item:e.item,attemptNumber:e.attemptNumber+n,time:t._schedule.now(),id:i}),o.push(i))}),e)};s(i.queue,0),s(i.inProgress,1),r.queue=r.queue.sort((function(e,t){return e.time-t.time})),this._store.set(this.keys.QUEUE,r.queue);try{this._clearOtherQueue(n,1)}catch(e){if("NS_ERROR_STORAGE_BUSY"===e.name||"NS_ERROR_STORAGE_BUSY"===e.code||2153971713===e.code)try{this._clearOtherQueue(n,40)}catch(e){console.error(e)}else console.error(e)}this._processHead()},as.prototype._clearOtherQueue=function(e,t){var n=this;setTimeout((function(){e.remove(n.keys.IN_PROGRESS),setTimeout((function(){e.remove(n.keys.QUEUE),setTimeout((function(){e.remove(n.keys.RECLAIM_START),setTimeout((function(){e.remove(n.keys.RECLAIM_END),setTimeout((function(){e.remove(n.keys.ACK)}),t)}),t)}),t)}),t)}),t)};var us=g(as),cs={maxRetryDelay:36e4,minRetryDelay:1e3,backoffFactor:2,maxAttempts:10,maxItems:100},ls=function(){function e(){i(this,e),this.url="",this.writeKey=""}return s(e,[{key:"init",value:function(e,t,n){var r=this;this.url=t,this.writeKey=e,n&&u(cs,n),this.payloadQueue=new us("rudder",cs,(function(e,t){e.message.sentAt=Xi(),r.processQueueElement(e.url,e.headers,e.message,3e4,(function(e,n){if(e)return t(e);t(null,n)}))})),this.payloadQueue.start()}},{key:"processQueueElement",value:function(e,t,n,r,i){try{var o=new XMLHttpRequest;for(var s in o.open("POST",e,!0),t)o.setRequestHeader(s,t[s]);o.timeout=r,o.ontimeout=i,o.onerror=i,o.onreadystatechange=function(){if(4===o.readyState)if(429===o.status||o.status>=500&&o.status<600){var t="".concat(Bn,' "').concat(o.status,'" status text: "').concat(o.statusText,'" for URL: "').concat(e,'"'),n=new Error(t);Kn(n),i(n)}else i(null,o.status)};var a=jn(n,!0);a?o.send(a):xn.error("Invalid payload: Event dropped")}catch(e){i(e)}}},{key:"enqueue",value:function(e,t){var n={"Content-Type":"application/json",Authorization:"Basic ".concat(btoa("".concat(this.writeKey,":"))),AnonymousId:btoa(e.anonymousId)};this.payloadQueue.addItem({url:"".concat(this.url,"/v1/").concat(t),headers:n,message:e})}}]),e}(),fs="queue",hs=64e3,ds=function(){function e(){i(this,e),this.storage=ni,this.maxItems=10,this.flushQueueTimeOut=void 0,this.timeOutActive=!1,this.flushQueueTimeOutInterval=6e5,this.url="",this.writekey="",this.queueName="".concat(fs,".").concat(Date.now())}return s(e,[{key:"sendQueueDataForBeacon",value:function(){this.sendDataFromQueueAndDestroyQueue()}},{key:"init",value:function(e,t,n){this.url=t,this.writekey=e,n.maxItems&&(this.maxItems=n.maxItems),n.flushQueueInterval&&(this.flushQueueTimeOutInterval=n.flushQueueInterval),this.sendQueueDataForBeacon=this.sendQueueDataForBeacon.bind(this),this.attachListeners()}},{key:"attachListeners",value:function(){var e=this;window.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e.sendQueueDataForBeacon()}))}},{key:"getQueue",value:function(){return this.storage.get(this.queueName)}},{key:"setQueue",value:function(e){this.storage.set(this.queueName,e)}},{key:"enqueue",value:function(e){var t=this.getQueue()||[];(t=t.slice(-(this.maxItems-1))).push(e);var n=t.slice(0),r={batch:n,sentAt:Xi()},i=jn(r,!0);i&&i.length>hs&&(n=t.slice(0,t.length-1),this.flushQueue(n),(t=this.getQueue()).push(e)),this.setQueue(t),this.setTimer(),t.length===this.maxItems&&this.flushQueue(n)}},{key:"sendDataFromQueueAndDestroyQueue",value:function(){this.sendDataFromQueue(),this.storage.remove(this.queueName)}},{key:"sendDataFromQueue",value:function(){var e=this.getQueue();if(e&&e.length>0){var t=e.slice(0,e.length);this.flushQueue(t)}}},{key:"flushQueue",value:function(e){if(e&&e.length>0){var t=Xi();e.forEach((function(e){e.sentAt=t}));var n=jn({batch:e,sentAt:t},!0);if(null!==n){var r=new Blob([n],{type:"text/plain"}),i="".concat(this.url,"?writeKey=").concat(this.writekey);try{"function"!=typeof navigator.sendBeacon&&Kn(new Error("Beacon API is not supported by browser")),navigator.sendBeacon(i,r)||Kn(new Error("Unable to queue data to browser's beacon queue"))}catch(e){e.message="".concat(e.message," - While sending Beacon data to: ").concat(i),Kn(e)}}else xn.error("Invalid payload: Event dropped")}this.setQueue([]),this.clearTimer()}},{key:"setTimer",value:function(){this.timeOutActive||(this.flushQueueTimeOut=setTimeout(this.sendDataFromQueue.bind(this),this.flushQueueTimeOutInterval),this.timeOutActive=!0)}},{key:"clearTimer",value:function(){this.timeOutActive&&(clearTimeout(this.flushQueueTimeOut),this.timeOutActive=!1)}}]),e}(),ps=function(){function e(){i(this,e),this.queue=void 0}return s(e,[{key:"initialize",value:function(e,t,n){var i={},o=Wi(t);n&&n.useBeacon&&navigator.sendBeacon?(n.beaconQueueOptions&&null!=n.beaconQueueOptions&&"object"===r(n.beaconQueueOptions)&&(i=n.beaconQueueOptions),o="".concat(o,"/beacon/v1/batch"),this.queue=new ds):(n&&n.useBeacon&&xn.info("[EventRepository] sendBeacon feature not available in this browser :: fallback to XHR"),n&&n.queueOptions&&null!=n.queueOptions&&"object"===r(n.queueOptions)&&(i=n.queueOptions),this.queue=new ls),this.queue.init(e,o,i)}},{key:"enqueue",value:function(e,t){var n=e.getElementContent();n.originalTimestamp=n.originalTimestamp||Xi(),n.sentAt=Xi();var r=jn(n,!0);r&&r.length>32e3&&xn.error("[EventRepository] enqueue:: message length greater 32 Kb ",n),this.queue.enqueue(n,t)}}]),e}(),gs=new ps,ys={maxRetryDelay:36e4,minRetryDelay:1e3,backoffFactor:2,maxAttempts:1/0},vs=function(){function e(){i(this,e),this.callback=void 0,this.processQueueElements=!1}return s(e,[{key:"init",value:function(e,t){var n=this;e&&u(ys,e),t&&(this.callback=t),this.payloadQueue=new us("rs_events",ys,(function(e,t){n.processQueueElement(e.type,e.rudderElement,(function(e,n){if(e)return t(e);t(null,n)}))})),this.payloadQueue.start()}},{key:"activateProcessor",value:function(){this.processQueueElements=!0}},{key:"processQueueElement",value:function(e,t,n){try{this.processQueueElements?(Object.setPrototypeOf(t,yo.prototype),this.callback(e,t),n(null)):n(new Error("The queue elements are not ready to be processed yet"))}catch(e){n(e)}}},{key:"enqueue",value:function(e,t){this.payloadQueue.addItem({type:e,rudderElement:t})}}]),e}(),ms=function(e){for(var t=function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}(),n=-1,r=0;r<e.length;r++)n=n>>>8^t[255&(n^e.charCodeAt(r))];return(-1^n)>>>0},Is={getUserLanguage:function(){return navigator&&navigator.language},getUserAgent:function(){return navigator&&navigator.userAgent}};function bs(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e=e.endsWith("..")?e.substr(0,e.length-2):e,decodeURIComponent(atob(e).split("").map((function(e){return"%".concat("00".concat(e.charCodeAt(0).toString(16)).slice(-2))})).join(""))}var As=/^[a-zA-Z0-9\-_.]+$/,ks=1,Es=1,Cs="*";function Ss(e,t,n,r){var i=function(e,t){return[e,(new Date).getTimezoneOffset(),t].join(Cs)}(n,r),o=t||0,s=Math.floor(Date.now()/6e4)-o;return ms([i,s,e].join(Cs)).toString(36)}function Os(e){var t=function(e){var t=e.split(Cs),n=t.length%2==0;return t.length<4||!n||Number(t.shift())!==Es?null:{checksum:t.shift(),serializedIds:t.join(Cs)}}(e);if(!t)return null;var n=t.checksum,r=t.serializedIds;return function(e,t){for(var n=Is.getUserAgent(),r=Is.getUserLanguage(),i=0;i<=ks;i+=1)if(Ss(e,i,n,r)===t)return!0;return!1}(r,n)?function(e){for(var t={},n=e.split(Cs),r=0;r<n.length;r+=2){var i=n[r];if(As.test(i)){var o=bs(n[r+1]);t[i]=o}}return t}(r):null}var ws=function(){function e(){var t=this;if(i(this,e),a(this,"isInitialized",!1),window.OneTrust&&window.OnetrustActiveGroups){this.userSetConsentGroupIds=window.OnetrustActiveGroups.split(",").filter((function(e){return e}));var n=window.OneTrust.GetDomainData().Groups;this.userSetConsentGroupNames=[],this.userDeniedConsentGroupIds=[],n.forEach((function(e){var n=e.CustomGroupId,r=e.GroupName;t.userSetConsentGroupIds.includes(n)?t.userSetConsentGroupNames.push(r.toUpperCase().trim()):t.userDeniedConsentGroupIds.push(n)})),this.userSetConsentGroupIds=this.userSetConsentGroupIds.map((function(e){return e.toUpperCase()})),this.isInitialized=!0}else xn.error("OneTrust resources are not accessible.")}return s(e,[{key:"isEnabled",value:function(e){var t=this;try{if(!this.isInitialized)return!0;var n=e.oneTrustCookieCategories;if(!n)return!0;var r=n.map((function(e){return e.oneTrustCookieCategory})).filter((function(e){return e}));return r.every((function(e){return t.userSetConsentGroupIds.includes(e.toUpperCase().trim())||t.userSetConsentGroupNames.includes(e.toUpperCase().trim())}))}catch(e){return xn.error("Error during onetrust cookie consent management ".concat(e)),!0}}},{key:"getDeniedList",value:function(){return this.isInitialized?this.userDeniedConsentGroupIds:[]}}]),e}(),Ts=function(){function e(){var t=this;if(i(this,e),a(this,"updatePurposes",(function(e){e&&Object.entries(e).forEach((function(e){var n=e[0];e[1]?t.userConsentedPurposes.push(n):t.userDeniedPurposes.push(n)}))})),this.userConsentedPurposes=[],this.userDeniedPurposes=[],window.updateKetchConsent=function(e){e&&(t.userConsentedPurposes=[],t.userDeniedPurposes=[],t.updatePurposes(e))},window.getKetchUserConsentedPurposes=function(){return t.userConsentedPurposes.slice()},window.getKetchUserDeniedPurposes=function(){return t.userDeniedPurposes.slice()},window.ketchConsent)this.updatePurposes(window.ketchConsent);else{var n=this.getConsent();this.updatePurposes(n)}}return s(e,[{key:"isEnabled",value:function(e){var t=this;try{var n=e.ketchConsentPurposes;if(!n||0===n.length)return!0;var r=n.map((function(e){return e.purpose})).filter((function(e){return e}));return r.some((function(e){return t.userConsentedPurposes.includes(e.trim())}))}catch(e){return xn.error("Error occured checking ketch consent state ".concat(e)),!0}}},{key:"getDeniedList",value:function(){return this.userDeniedPurposes}},{key:"getConsent",value:function(){var e=Zr.get("_ketch_consent_v1_");if(e){var t;try{t=JSON.parse(atob(e))}catch(e){return void xn.error("Error occurred while parsing consent cookie ".concat(e))}if(t){var n={};return Object.entries(t).forEach((function(e){var t=e[0],r=e[1];r&&r.status&&(n[t]="granted"===r.status)})),n}}}}]),e}(),_s=function(e){var t,n;return null!=e&&null!==(t=e.oneTrust)&&void 0!==t&&t.enabled?new ws:null!=e&&null!==(n=e.ketch)&&void 0!==n&&n.enabled?new Ts:null},Rs=function(){function e(){i(this,e),this.storage=ki,this.timeout=18e5,this.sessionInfo=this.storage.getSessionInfo()||{autoTrack:!0}}return s(e,[{key:"initialize",value:function(e){try{var t;if(this.sessionInfo.autoTrack=!(!1===(null==e||null===(t=e.sessions)||void 0===t?void 0:t.autoTrack)||this.sessionInfo.manualTrack),null!=e&&e.sessions&&!isNaN(e.sessions.timeout)){var n=e.sessions.timeout;0===n&&(xn.warn("[Session]:: Provided timeout value 0 will disable the auto session tracking feature."),this.sessionInfo.autoTrack=!1),n>0&&n<1e4&&xn.warn('[Session]:: It is not advised to set "timeout" less than 10 seconds'),this.timeout=n}this.sessionInfo.autoTrack?this.startAutoTracking():!1!==this.sessionInfo.autoTrack||this.sessionInfo.manualTrack||this.end()}catch(e){Kn(e)}}},{key:"isValidSession",value:function(e){return e<=this.sessionInfo.expiresAt}},{key:"generateSessionId",value:function(){return Date.now()}},{key:"startAutoTracking",value:function(){var e=Date.now();this.isValidSession(e)||(this.sessionInfo={},this.sessionInfo.id=e,this.sessionInfo.expiresAt=e+this.timeout,this.sessionInfo.sessionStart=!0,this.sessionInfo.autoTrack=!0),this.storage.setSessionInfo(this.sessionInfo)}},{key:"validateSessionId",value:function(e){if("number"==typeof e&&e%1==0){var t;if(!(((t=e)?t.toString().length:0)<10))return e;xn.error('[Session]:: "sessionId" should at least be "'.concat(10,'" digits long'))}else xn.error('[Session]:: "sessionId" should only be a positive integer')}},{key:"start",value:function(e){var t=e?this.validateSessionId(e):this.generateSessionId();this.sessionInfo={id:t||this.generateSessionId(),sessionStart:!0,manualTrack:!0},this.storage.setSessionInfo(this.sessionInfo)}},{key:"getSessionId",value:function(){return this.sessionInfo.autoTrack&&this.isValidSession(Date.now())||this.sessionInfo.manualTrack?this.sessionInfo.id:null}},{key:"end",value:function(){this.sessionInfo={},this.storage.removeSessionInfo()}},{key:"getSessionInfo",value:function(){var e={};if(this.sessionInfo.autoTrack||this.sessionInfo.manualTrack){if(this.sessionInfo.autoTrack){var t=Date.now();this.isValidSession(t)?this.sessionInfo.expiresAt=t+this.timeout:this.startAutoTracking()}this.sessionInfo.sessionStart&&(e.sessionStart=!0,this.sessionInfo.sessionStart=!1),e.sessionId=this.sessionInfo.id,this.storage.setSessionInfo(this.sessionInfo)}return e}},{key:"reset",value:function(){var e=this.sessionInfo,t=e.manualTrack;e.autoTrack?(this.sessionInfo={},this.startAutoTracking()):t&&this.start()}}]),e}(),Ps=new Rs,xs=["integrations","anonymousId","originalTimestamp"],Ds=function(e,t,n){var r="";if(n){if(!(r=Wi(n))){var i="CDN base URL for integrations is not valid";throw Kn({message:"[Analytics] load:: ".concat(i)}),Error("Failed to load Rudder SDK: ".concat(i))}return r}var o=function(){for(var e,t=document.getElementsByTagName("script"),n=0;n<t.length;n+=1){var r=Wi(t[n].getAttribute("src"));if(r&&r.match(/^.*rudder-analytics?(\.min)?\.js$/)){e=r;break}}return{sdkURL:e}}(),s=o.sdkURL;return r=s?s.split("/").slice(0,-1).concat(Fi).join("/"):ji,t&&(r=r.replace(Gi,e)),r},Ls="rs-bugsnag",Ms="bugsnag",Bs=["Bugsnag",Ms],Ns="0d1f20bcea137b20b3546ebc65e301ec",Gs=["rudder-analytics.min.js","rudder-analytics.js"].concat(l(Object.keys(x).map((function(e){return"".concat(x[e],".min.js")}))),l(Object.keys(x).map((function(e){return"".concat(x[e],".js")})))),Fs=function(e){var t=e&&e._client&&e._client._notifier&&e._client._notifier.version;if(!t){var n=e({apiKey:Ns,releaseStage:"version-test",beforeSend:function(){return!1}});t=n.notifier&&n.notifier.version,n=void 0}return t&&"6"===t.charAt(0)},js=function(e){Bs.every((function(e){return!function(e,t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(e),t)}(window,e)}))&&Ei(e,"https://d2wy8f7a9ursnm.cloudfront.net/v6/bugsnag.min.js",{isNonNativeSDK:"true",skipDatasetAttributes:!0})},Us=function(){function e(t,n,r){i(this,e),this.pluginName=Ls,this.sourceId=t,this.writeKey=n,this.onClientReady=r,this.initClientOnLibReadyInterval=void 0,this.init()}return s(e,[{key:"init",value:function(){var e=this;if(!window.RudderStackGlobals||!window.RudderStackGlobals[Gn]){if(!Ns.match(/{{.+}}/)){js(this.pluginName);var t=window[Ms];"function"!=typeof t?(this.initClientOnLibReadyInterval=setInterval((function(){var t=window[Ms];"function"==typeof t&&(Fs(t)&&e.initClient(),clearInterval(e.initClientOnLibReadyInterval))}),100),setTimeout((function(){clearInterval(e.initClientOnLibReadyInterval)}),Dn)):Fs(t)&&this.initClient()}}}},{key:"initClient",value:function(){var e,t=window[Ms];this.client=t({apiKey:Ns,appVersion:"2.48.6",metaData:{SDK:{name:"JS",installType:"npm"}},beforeSend:this.onError(),autoTrackSessions:!1,collectUserIp:!1,enabledBreadcrumbTypes:["error","log","user"],maxEvents:100,releaseStage:(e=window.location.hostname,e&&["www.test-host.com","localhost","127.0.0.1","[::1]"].includes(e),"development"),networkBreadcrumbsEnabled:!1,user:{id:this.writeKey}}),this.onClientReady()}},{key:"onError",value:function(){var e=this.sourceId;return function(t){try{return!!function(e){var t=Ar(e,"stacktrace.0.file");if(!t||"string"!=typeof t)return!1;var n=t.substring(t.lastIndexOf("/")+1);return Gs.includes(n)}(t)&&(function(e,t){e.updateMetaData("source",{metadataSource:t});var n=e.errorMessage;e.context=n,n.includes("error in script loading")&&(e.context="Script load failures"),e.severity="error"}(t,e),!0)}catch(e){return!1}}}},{key:"notify",value:function(e){this.client&&this.client.notify(e)}},{key:"leaveBreadcrumb",value:function(e){this.client&&this.client.leaveBreadcrumb(e)}}]),e}(),Ks=Ls,Qs=[Ls],zs=function(){function e(t){i(this,e),this.isEnabled=!1,this.providerName=Ks,this.provider=void 0,this.logger=t,this.onClientReady=this.onClientReady.bind(this),this.exposeToGlobal=this.exposeToGlobal.bind(this)}return s(e,[{key:"init",value:function(e,t,n){e&&t?!0===function(e){return Ar(e,"statsCollection.errors.enabled")||!1}(e)?(this.enable(),this.setProviderName(function(e){return Ar(e,"statsCollection.errors.provider")}(e)),this.initProvider(e,t,n)):this.disable():this.logger.error("[Analytics] ErrorReporting :: Invalid configuration or missing source id provided.")}},{key:"enable",value:function(){this.isEnabled=!0}},{key:"disable",value:function(){this.isEnabled=!1}},{key:"setProviderName",value:function(e){e?!e||Qs.includes(e)?this.providerName=e:this.logger.error("[Analytics] ErrorReporting :: Invalid error reporting provider value. Value should be one of: ".concat(Qs.join(","))):this.providerName=Ks}},{key:"initProvider",value:function(e,t,n){if(this.providerName===Ls)this.provider=new Us(t,n,this.onClientReady)}},{key:"onClientReady",value:function(){this.exposeToGlobal()}},{key:"exposeToGlobal",value:function(){window.RudderStackGlobals||(window.RudderStackGlobals={}),window.RudderStackGlobals[Gn]=this}},{key:"leaveBreadcrumb",value:function(e){if(this.provider)try{this.provider.leaveBreadcrumb(e)}catch(e){this.logger.error("[Analytics] ErrorReporting :: leaveBreadcrumb method ".concat(e.toString()))}}},{key:"notify",value:function(e){if(this.provider)try{this.provider.notify(e)}catch(e){this.logger.error("[Analytics] ErrorReporting :: notify method ".concat(e.toString()))}}}]),e}(),Hs=new(function(){function e(){i(this,e),this.retryAttempt=3,this.queue=[],this.isTransformationProcessing=!1,this.authToken=null}return s(e,[{key:"init",value:function(e,t,n){this.dataPlaneUrl=Wi(t),this.writeKey=e,this.authToken=n||this.authToken,this.start()}},{key:"enqueue",value:function(e,t,n){this.queue.push({event:e,destinationIds:t,cb:n})}},{key:"sendEventForTransformation",value:function(e,t){var n=this;return new Promise((function(r,i){var o="".concat(n.dataPlaneUrl,"/transform"),s={"Content-Type":"application/json",Authorization:"Basic ".concat(btoa("".concat(n.writeKey,":")))};try{var a=new XMLHttpRequest;a.open("POST",o,!0),Object.keys(s).forEach((function(e){return a.setRequestHeader(e,s[e])})),a.timeout=1e4,a.onreadystatechange=function(){if(4===a.readyState)try{var o=a.status,s=a.response;switch(o){case 200:return s=JSON.parse(s),void r({status:o,transformedPayload:s.transformedBatch});case 400:var u=s?"".concat(s):"Invalid request payload";return void r({status:o,errorMessage:u});case 404:return void r({status:o});default:if(t>0){var c=t-1;setTimeout((function(){return n.sendEventForTransformation(e,c).then(r).catch(i)}),500*Math.pow(2,n.retryAttempt-c))}else r({status:o,errorMessage:"Retries exhausted"})}}catch(e){i(e)}};var u=jn(e,!0);u?a.send(u):xn.error("Invalid payload: Event dropped")}catch(e){i(e)}}))}},{key:"checkQueueLengthAndProcess",value:function(){this.queue.length>0&&this.process()}},{key:"process",value:function(){var e=this;this.isTransformationProcessing=!0;var t,n,r=this.queue.shift(),i=(t=r.event,n=r.destinationIds,{metadata:{"Custom-Authorization":this.authToken},batch:[{orderNo:Date.now(),destinationIds:n,event:t.message}]});this.sendEventForTransformation(i,this.retryAttempt).then((function(t){e.isTransformationProcessing=!1,r.cb(t),e.checkQueueLengthAndProcess()})).catch((function(t){Kn("string"==typeof t?t:t.message),e.isTransformationProcessing=!1,r.cb({status:0}),e.checkQueueLengthAndProcess()}))}},{key:"start",value:function(){var e=this;setInterval((function(){e.isTransformationProcessing||e.checkQueueLengthAndProcess()}),100)}},{key:"setAuthToken",value:function(e){this.authToken=e}}]),e}()),qs=function(){function e(){i(this,e),this.initialized=!1,this.clientIntegrations=[],this.loadOnlyIntegrations={},this.clientIntegrationObjects=void 0,this.successfullyLoadedIntegration=[],this.failedToBeLoadedIntegration=[],this.toBeProcessedArray=[],this.toBeProcessedByIntegrationArray=[],this.storage=ki,this.eventRepository=gs,this.preProcessQueue=new vs,this.sendAdblockPage=!1,this.sendAdblockPageOptions={},this.clientSuppliedCallbacks={},this.readyCallbacks=[],this.methodToCallbackMapping={syncPixel:"syncPixelCallback"},this.loaded=!1,this.loadIntegration=!0,this.bufferDataPlaneEventsUntilReady=!1,this.dataPlaneEventsBufferTimeout=1e4,this.integrationsData={},this.dynamicallyLoadedIntegrations={},this.destSDKBaseURL=ji,this.cookieConsentOptions={},this.logLevel=void 0,this.clientIntegrationsReady=!1,this.uSession=Ps,this.version="2.48.6",this.lockIntegrationsVersion=!1,this.errorReporting=new zs(xn),this.deniedConsentIds=[],this.transformationHandler=Hs}return s(e,[{key:"initializeUser",value:function(e){this.userId=this.storage.getUserId()||"",this.storage.setUserId(this.userId),this.userTraits=this.storage.getUserTraits()||{},this.storage.setUserTraits(this.userTraits),this.groupId=this.storage.getGroupId()||"",this.storage.setGroupId(this.groupId),this.groupTraits=this.storage.getGroupTraits()||{},this.storage.setGroupTraits(this.groupTraits),this.anonymousId=this.getAnonymousId(e),this.storage.setAnonymousId(this.anonymousId)}},{key:"setInitialPageProperties",value:function(){if(null==this.storage.getInitialReferrer()&&null==this.storage.getInitialReferringDomain()){var e=io();this.storage.setInitialReferrer(e),this.storage.setInitialReferringDomain(oo(e))}}},{key:"allModulesInitialized",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new Promise((function(n){e.clientIntegrations.every((function(t){return null!=e.dynamicallyLoadedIntegrations["".concat(x[t.name]).concat("_RS")]}))||t>=2e4?n(e):e.pause(Ln).then((function(){return e.allModulesInitialized(t+Ln).then(n)}))}))}},{key:"executeReadyCallback",value:function(){this.readyCallbacks.forEach((function(e){return e()}))}},{key:"integrationSDKLoaded",value:function(e,t){return window[e]&&window[e][t]&&window[e][t].prototype&&void 0!==window[e][t].prototype.constructor}},{key:"processResponse",value:function(e,t){var n=this;try{var i=t;try{if("string"==typeof t&&(i=JSON.parse(t)),!i||"object"!==r(i)||Array.isArray(i))return void xn.error("Invalid source configuration")}catch(e){return void Kn(e)}try{window.chrome&&window.chrome.runtime&&window.chrome.runtime.id||this.errorReporting.init(i.source.config,i.source.id,this.writeKey)}catch(e){Kn(e)}if(this.serverUrl=ro(i,this.serverUrl,this.options),this.eventRepository.initialize(this.writeKey,this.serverUrl,this.options),this.transformationHandler.init(this.writeKey,this.serverUrl,this.storage.getAuthToken()),i.source.destinations.forEach((function(e){e.enabled&&this.clientIntegrations.push({name:e.destinationDefinition.name,config:e.config,destinationInfo:{shouldApplyDeviceModeTransformation:e.shouldApplyDeviceModeTransformation||!1,propagateEventsUntransformedOnError:e.propagateEventsUntransformedOnError||!1,destinationId:e.id}})}),this),this.clientIntegrations=eo(this.loadOnlyIntegrations,this.clientIntegrations),Object.keys(this.cookieConsentOptions).length>0)try{var o=_s(this.cookieConsentOptions);this.deniedConsentIds=o&&o.getDeniedList(),this.clientIntegrations=this.clientIntegrations.filter((function(e){return!o||o&&o.isEnabled(e.config)}))}catch(e){Kn(e)}this.loaded=!0,this.options&&"function"==typeof this.options.onLoaded&&this.options.onLoaded(this),function(e){if(e.toBeProcessedArray.length>0)for(;e.toBeProcessedArray.length>0;){var t=l(e.toBeProcessedArray[0]);e.toBeProcessedArray.shift();var n=t[0];t.shift(),e[n].apply(e,l(t))}}(this),this.clientIntegrations=this.clientIntegrations.filter((function(e){return!!x[e.name]||(xn.error("[Analytics] Integration:: ".concat(e.name," not available for initialization")),!1)})),this.bufferDataPlaneEventsUntilReady&&setTimeout((function(){n.processBufferedCloudModeEvents()}),this.dataPlaneEventsBufferTimeout),this.errorReporting.leaveBreadcrumb("Starting device-mode initialization"),this.clientIntegrations.forEach((function(e){var t=x[e.name],r="".concat(t).concat("_RS"),i="".concat(n.destSDKBaseURL,"/").concat(t,".min.js");window.hasOwnProperty(r)||Ei(r,i,{isNonNativeSDK:!0});var o=n,s=setInterval((function(){if(o.integrationSDKLoaded(r,t)){var i,a=window[r];clearInterval(s);try{var u="[Analytics] processResponse :: trying to initialize integration name:: ".concat(r);n.errorReporting.leaveBreadcrumb(u),(i=new a[t](e.config,o,e.destinationInfo)).init(),o.isInitialized(i).then((function(){o.dynamicallyLoadedIntegrations[r]=a[t]}))}catch(e){var c="[Analytics] 'integration.init()' failed :: ".concat(r," :: ").concat(e.message);Kn(e,c),o.failedToBeLoadedIntegration.push(i)}}}),100);setTimeout((function(){clearInterval(s)}),Dn)}));var s=this;this.allModulesInitialized().then((function(){if(!s.clientIntegrations||0===s.clientIntegrations.length)return n.clientIntegrationsReady=!0,n.executeReadyCallback(),void(n.toBeProcessedByIntegrationArray=[]);s.replayEvents(s)}))}catch(e){Kn(e)}}},{key:"sendDataToDestination",value:function(e,t,n){try{if(e[n]){var r=T(t);e[n](r)}}catch(t){var i="[sendToNative]:: [Destination: ".concat(e.name,"]:: ");Kn(t,i)}}},{key:"sendTransformedDataToDestination",value:function(e,t,n){var r=this;try{Zi(t.message.integrations);var i=e.map((function(e){return e.destinationId}));this.transformationHandler.enqueue(t,i,(function(i){var o=i.status,s=i.transformedPayload,a=i.errorMessage;e.forEach((function(e){try{switch(o){case 200:var i=s.find((function(t){return t.id===e.destinationId})),u=[];null==i||i.payload.forEach((function(n){if("200"===n.status)u.push(n);else{var r='[DMT]:: Event transformation unsuccessful for destination "'.concat(e.name,'". Reason: '),i="Unknown";"410"===n.status&&(i="Transformation is not available");var o="Dropping the event",s=xn.error;!0===e.propagateEventsUntransformedOnError&&(o="Sending untransformed event to the destination",s=xn.warn,u.push({event:t.message})),s("".concat(r," ").concat(i,". ").concat(o,"."))}})),null==u||u.forEach((function(t){wi(t.event)&&r.sendDataToDestination(e,{message:t.event},n)}));break;case 404:xn.warn("[DMT]:: Transformation server access is denied. The configuration data seems to be out of sync. Sending untransformed event to the destination."),r.sendDataToDestination(e,t,n);break;default:!0===e.propagateEventsUntransformedOnError?(xn.warn("[DMT]::[Destination: ".concat(e.name,"] :: Transformation request failed with status: ").concat(o," ").concat(a,". Sending untransformed event.")),r.sendDataToDestination(e,t,n)):xn.error("[DMT]::[Destination: ".concat(e.name,"] :: Transformation request failed with status: ").concat(o," ").concat(a,". Dropping the event."))}}catch(t){t instanceof Error&&(t.message="[DMT]::[Destination:".concat(e.name,"]:: ").concat(t.message)),Kn(t)}}))}))}catch(e){e instanceof Error&&(e.message="[DMT]:: ".concat(e.message)),Kn(e)}}},{key:"processAndSendEventsToDeviceMode",value:function(e,t,n){var r=this,i=[],o=[];e.forEach((function(e){try{!r.IsEventBlackListed(t.message.event,e.name)&&(e.shouldApplyDeviceModeTransformation?o.push(e):i.push(e))}catch(e){Kn(e)}})),i.forEach((function(e){r.sendDataToDestination(e,t,n)})),o.length>0&&this.sendTransformedDataToDestination(o,t,n)}},{key:"queueEventForDataPlane",value:function(e,t){var n=t.message.integrations||{All:!0};t.message.integrations=function(e,t){var n=T(e),r=Object.keys(t).filter((function(e){return!(!0===t[e]&&n[e])})).reduce((function(e,n){return e[n]=t[n],e}),{});return Oi(n,r)}(this.integrationsData,n),this.eventRepository.enqueue(t,e)}},{key:"processBufferedCloudModeEvents",value:function(){this.bufferDataPlaneEventsUntilReady&&this.preProcessQueue.activateProcessor()}},{key:"replayEvents",value:function(e){var t,n,r,i=this;this.errorReporting.leaveBreadcrumb("Started replaying buffered events"),e.clientIntegrationObjects=[],e.clientIntegrationObjects=e.successfullyLoadedIntegration;try{e.clientIntegrationObjects.every((function(e){return!e.isReady||e.isReady()}))&&(this.integrationsData=(t=this.integrationsData,n=e.clientIntegrationObjects,r=T(t),n.forEach((function(e){if(e.getDataForIntegrationsObject)try{r=Oi(r,e.getDataForIntegrationsObject())}catch(e){xn.debug("[Analytics: prepareDataForIntegrationsObj]",e)}})),r),e.clientIntegrationsReady=!0,e.executeReadyCallback())}catch(e){Kn(e,"Replay buffered cloud mode events")}this.processBufferedCloudModeEvents(),e.toBeProcessedByIntegrationArray.forEach((function(t){var n=t[0];t.shift(),Object.keys(t[0].message.integrations).length>0&&$i(t[0].message.integrations);var r=eo(t[0].message.integrations,e.clientIntegrationObjects);i.processAndSendEventsToDeviceMode(r,t[0],n)})),e.toBeProcessedByIntegrationArray=[]}},{key:"pause",value:function(e){return new Promise((function(t){setTimeout(t,e)}))}},{key:"isInitialized",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return new Promise((function(r){e.isLoaded()?(t.successfullyLoadedIntegration.push(e),r(t)):n>=Dn?(t.failedToBeLoadedIntegration.push(e),r(t)):t.pause(Ln).then((function(){return t.isInitialized(e,n+Ln).then(r)}))}))}},{key:"page",value:function(e,t,n,i,o){if(this.errorReporting.leaveBreadcrumb("Page event"),this.loaded){"function"==typeof i&&(o=i,i=null),"function"==typeof n&&(o=n,i=n=null),"function"==typeof t&&(o=t,i=n=t=null),"function"==typeof e&&(o=e,i=n=t=e=null),"object"===r(e)&&null!=e&&null!=e&&(i=t,n=e,t=e=null),"object"===r(t)&&null!=t&&null!=t&&(i=n,n=t,t=null),"string"==typeof e&&"string"!=typeof t&&(t=e,e=null),this.sendAdblockPage&&"RudderJS-Initiated"!==e&&this.sendSampleRequest();var s=T(n),a=T(i),u=(new vo).setType("page").build();s||(s={}),t&&(u.message.name=s.name=t),e&&(u.message.category=s.category=e),u.message.properties=this.getPageProperties(s),this.processAndSendDataToDestinations("page",u,a,o)}else this.toBeProcessedArray.push(["page"].concat(Array.prototype.slice.call(arguments)))}},{key:"track",value:function(e,t,n,r){if(this.errorReporting.leaveBreadcrumb("Track event"),this.loaded){"function"==typeof n&&(r=n,n=null),"function"==typeof t&&(r=t,n=null,t=null);var i=T(t),o=T(n),s=(new vo).setType("track").build();e&&s.setEventName(e),s.setProperty(i||{}),this.processAndSendDataToDestinations("track",s,o,r)}else this.toBeProcessedArray.push(["track"].concat(Array.prototype.slice.call(arguments)))}},{key:"identify",value:function(e,t,n,i){if(this.errorReporting.leaveBreadcrumb("Identify event"),this.loaded){"function"==typeof n&&(i=n,n=null),"function"==typeof t&&(i=t,n=null,t=null),"object"===r(e)&&(n=t,t=e,e=this.userId);var o=to(e);o&&this.userId&&o!==this.userId&&this.reset(),this.userId=o,this.storage.setUserId(this.userId);var s=T(t),a=T(n);if(s){for(var u in s)this.userTraits[u]=s[u];this.storage.setUserTraits(this.userTraits)}var c=(new vo).setType("identify").build();this.processAndSendDataToDestinations("identify",c,a,i)}else this.toBeProcessedArray.push(["identify"].concat(Array.prototype.slice.call(arguments)))}},{key:"alias",value:function(e,t,n,i){if(this.errorReporting.leaveBreadcrumb("Alias event"),this.loaded){"function"==typeof n&&(i=n,n=null),"function"==typeof t&&(i=t,n=null,t=null),"function"==typeof e&&(i=e,n=null,t=null,e=null),"object"===r(t)&&(n=t,t=null),"object"===r(e)&&(n=e,t=null,e=null);var o=(new vo).setType("alias").build();o.message.previousId=to(t)||(this.userId?this.userId:this.getAnonymousId()),o.message.userId=to(e);var s=T(n);this.processAndSendDataToDestinations("alias",o,s,i)}else this.toBeProcessedArray.push(["alias"].concat(Array.prototype.slice.call(arguments)))}},{key:"group",value:function(e,t,n,i){if(this.errorReporting.leaveBreadcrumb("Group event"),this.loaded){if(0!==arguments.length){"function"==typeof n&&(i=n,n=null),"function"==typeof t&&(i=t,n=null,t=null),"object"===r(e)&&(n=t,t=e,e=this.groupId),"function"==typeof e&&(i=e,n=null,t=null,e=this.groupId),this.groupId=to(e),this.storage.setGroupId(this.groupId);var o=T(t),s=T(n),a=(new vo).setType("group").build();if(o)for(var u in o)this.groupTraits[u]=o[u];else this.groupTraits={};this.storage.setGroupTraits(this.groupTraits),this.processAndSendDataToDestinations("group",a,s,i)}}else this.toBeProcessedArray.push(["group"].concat(Array.prototype.slice.call(arguments)))}},{key:"IsEventBlackListed",value:function(e,t){if(!e||"string"!=typeof e)return!1;var n=Rn[t],r=this.clientIntegrations.find((function(e){return e.name===n})).config,i=r.blacklistedEvents,o=r.whitelistedEvents,s=r.eventFilteringOption;if(!s)return!1;var a=e.trim().toUpperCase();switch(s){case"disable":default:return!1;case"blacklistedEvents":return!!Array.isArray(i)&&i.some((function(e){return e.eventName.trim().toUpperCase()===a}));case"whitelistedEvents":return!Array.isArray(o)||!o.some((function(e){return e.eventName.trim().toUpperCase()===a}))}}},{key:"shouldUseGlobalIntegrationsConfigInEvents",value:function(){return this.useGlobalIntegrationsConfigInEvents&&this.loadOnlyIntegrations&&Object.keys(this.loadOnlyIntegrations).length>0}},{key:"processAndSendDataToDestinations",value:function(e,n,r,i){try{this.anonymousId||this.setAnonymousId(),this.errorReporting.leaveBreadcrumb("Started sending data to destinations"),n.message.context.traits=t({},this.userTraits),n.message.anonymousId=this.anonymousId,n.message.userId=n.message.userId?n.message.userId:this.userId,"group"==e&&(this.groupId&&(n.message.groupId=this.groupId),this.groupTraits&&(n.message.traits=t({},this.groupTraits)));try{var o=this.uSession.getSessionInfo(),s=o.sessionId,a=o.sessionStart;n.message.context.sessionId=s,a&&(n.message.context.sessionStart=!0)}catch(e){Kn(e)}f=this.cookieConsentOptions,h=!1,Object.keys(f).forEach((function(e){zi.includes(e)&&"boolean"==typeof f[e].enabled&&!0===f[e].enabled&&(h=!0)})),h&&(n.message.context.consentManagement={deniedConsentIds:this.deniedConsentIds||[]}),this.processOptionsParam(n,r),function(e,t){var n=e.properties,r=e.traits,i=e.context;n&&Object.keys(n).forEach((function(e){Bi.indexOf(e.toLowerCase())>=0&&xn.error("Warning! : Reserved keyword used in properties--\x3e ".concat(e," with ").concat(t," call"))})),r&&Object.keys(r).forEach((function(e){Bi.indexOf(e.toLowerCase())>=0&&xn.error("Warning! : Reserved keyword used in traits--\x3e ".concat(e," with ").concat(t," call"))}));var o=i.traits;o&&Object.keys(o).forEach((function(e){Bi.indexOf(e.toLowerCase())>=0&&xn.error("Warning! : Reserved keyword used in traits --\x3e ".concat(e," with ").concat(t," call"))}))}(n.message,e);var u=n.message.integrations;u?$i(u):u=this.shouldUseGlobalIntegrationsConfigInEvents()?this.loadOnlyIntegrations:Vi,n.message.integrations=u;try{n.message.context["ua-ch"]=this.uach}catch(e){Kn(e)}try{if(this.clientIntegrationObjects){var c=eo(u,this.clientIntegrationObjects);this.processAndSendEventsToDeviceMode(c,n,e)}else this.toBeProcessedByIntegrationArray.push([e,n])}catch(e){Kn(e,"processAndSendEventsToDeviceMode::")}var l=T(n);Zi(l.message.integrations),!this.bufferDataPlaneEventsUntilReady||this.clientIntegrationObjects?this.queueEventForDataPlane(e,l):this.preProcessQueue.enqueue(e,l),i&&"function"==typeof i&&i(l)}catch(e){Kn(e)}var f,h}},{key:"utm",value:function(e){var t={};try{var n=new URL(e),r="utm_";n.searchParams.forEach((function(e,n){if(n.startsWith(r)){var i=n.substring(4);"campaign"===i&&(i="name"),t[i]=e}}))}catch(e){}return t}},{key:"addCampaignInfo",value:function(e){var t=e.message.context;t&&"object"===r(t)&&(e.message.context.campaign=this.utm(window.location.href))}},{key:"processOptionsParam",value:function(e,n){var i=e.message,o=i.type,s=i.properties;this.addCampaignInfo(e),e.message.context.page=this.getContextPageProperties("page"===o?s:void 0),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"object"===r(t)&&null!==t&&Object.keys(t).forEach((function(n){xs.includes(n)&&(e[n]=t[n])}))}(e.message,n),e.message.context=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.context;return"object"!==r(n)||null===n||Object.keys(n).forEach((function(e){if(!xs.includes(e)&&!Hi.includes(e))if("context"!==e)i=Oi(i,a({},e,n[e]));else if("object"===r(n[e])&&null!==n[e]){var o={};Object.keys(n[e]).forEach((function(t){Hi.includes(t)||(o[t]=n[e][t])})),i=Oi(i,t({},o))}else xn.error("[Analytics: processOptionsParam] context passed in options ".concat(e," is not object."))})),i}(e.message,n)}},{key:"getPageProperties",value:function(e,t){var n=ao(),r=t&&t.page||{};for(var i in n)void 0===e[i]&&(e[i]=r[i]||n[i]);return e}},{key:"getContextPageProperties",value:function(e){var t=ao(),n={};for(var r in t)n[r]=e&&e[r]?e[r]:t[r];return n}},{key:"reset",value:function(e){this.errorReporting.leaveBreadcrumb("reset API :: flag: ".concat(e)),this.loaded?(e&&(this.anonymousId=""),this.userId="",this.userTraits={},this.groupId="",this.groupTraits={},this.uSession.reset(),this.storage.clear(e)):this.toBeProcessedArray.push(["reset",e])}},{key:"getAnonymousId",value:function(e){return this.anonymousId=this.storage.getAnonymousId(e),this.anonymousId||this.setAnonymousId(),this.anonymousId}},{key:"getUserId",value:function(){return this.userId}},{key:"getSessionId",value:function(){return this.uSession.getSessionId()}},{key:"getUserTraits",value:function(){return this.userTraits}},{key:"getGroupId",value:function(){return this.groupId}},{key:"getGroupTraits",value:function(){return this.groupTraits}},{key:"setAnonymousId",value:function(e,t){var n=t?Os(t):null,r=n?n.rs_amp_id:null;this.anonymousId=e||r||Yi(),this.storage.setAnonymousId(this.anonymousId)}},{key:"isValidWriteKey",value:function(e){return e&&"string"==typeof e&&e.trim().length>0}},{key:"isValidServerUrl",value:function(e){return e&&"string"==typeof e&&e.trim().length>0}},{key:"isDatasetAvailable",value:function(){var e=document.createElement("div");return e.setAttribute("data-a-b","c"),!!e.dataset&&"c"===e.dataset.aB}},{key:"loadAfterPolyfill",value:function(e,n,i){var o=this;if("object"===r(n)&&null!==n&&(i=n,n=null),i&&i.logLevel&&(this.logLevel=i.logLevel,xn.setLogLevel(i.logLevel)),!this.isValidWriteKey(e))throw Error("Unable to load the SDK due to invalid writeKey");if(!this.storage||0===Object.keys(this.storage).length)throw Error("Cannot proceed as no storage is available");i&&i.cookieConsentManager&&(this.cookieConsentOptions=i.cookieConsentManager),this.writeKey=e,this.serverUrl=n,this.options=i;var s={};if(i&&i.setCookieDomain&&(s=t(t({},s),{},{domain:i.setCookieDomain})),i&&"boolean"==typeof i.secureCookie&&(s=t(t({},s),{},{secure:i.secureCookie})),i&&"boolean"==typeof i.sameDomainCookiesOnly&&(s=t(t({},s),{},{sameDomainCookiesOnly:i.sameDomainCookiesOnly})),i&&-1!==Ki.indexOf(i.sameSiteCookie)&&(s=t(t({},s),{},{samesite:i.sameSiteCookie})),this.storage.options(s),i&&"string"==typeof i.uaChTrackLevel&&qi.includes(i.uaChTrackLevel)&&(this.uaChTrackLevel=i.uaChTrackLevel),navigator.userAgentData&&function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"none";"none"===t&&e(void 0),"default"===t&&e(navigator.userAgentData),"full"===t&&navigator.userAgentData.getHighEntropyValues(["architecture","bitness","brands","mobile","model","platform","platformVersion","uaFullVersion","fullVersionList","wow64"]).then((function(t){e(t)}))}((function(e){o.uach=e}),this.uaChTrackLevel),i&&i.integrations&&(u(this.loadOnlyIntegrations,i.integrations),$i(this.loadOnlyIntegrations)),this.useGlobalIntegrationsConfigInEvents=i&&!0===i.useGlobalIntegrationsConfigInEvents,i&&i.sendAdblockPage&&(this.sendAdblockPage=!0),i&&i.sendAdblockPageOptions&&"object"===r(i.sendAdblockPageOptions)&&(this.sendAdblockPageOptions=i.sendAdblockPageOptions),this.uSession.initialize(i),i&&i.clientSuppliedCallbacks){var a={};Object.keys(this.methodToCallbackMapping).forEach((function(e){o.methodToCallbackMapping.hasOwnProperty(e)&&i.clientSuppliedCallbacks[o.methodToCallbackMapping[e]]&&(a[e]=i.clientSuppliedCallbacks[o.methodToCallbackMapping[e]])})),u(this.clientSuppliedCallbacks,a),this.registerCallbacks(!0)}if(i&&null!=i.loadIntegration&&(this.loadIntegration=!!i.loadIntegration),i&&"boolean"==typeof i.bufferDataPlaneEventsUntilReady&&(this.bufferDataPlaneEventsUntilReady=!0===i.bufferDataPlaneEventsUntilReady,this.bufferDataPlaneEventsUntilReady&&this.preProcessQueue.init(this.options,this.queueEventForDataPlane.bind(this))),i&&"number"==typeof i.dataPlaneEventsBufferTimeout&&(this.dataPlaneEventsBufferTimeout=i.dataPlaneEventsBufferTimeout),i&&void 0!==i.lockIntegrationsVersion&&(this.lockIntegrationsVersion=!0===i.lockIntegrationsVersion),this.initializeUser(i?i.anonymousIdOptions:void 0),this.setInitialPageProperties(),this.destSDKBaseURL=Ds(this.version,this.lockIntegrationsVersion,i&&i.destSDKBaseURL),i&&i.getSourceConfig)if("function"!=typeof i.getSourceConfig)Kn(new Error('option "getSourceConfig" must be a function'));else{var c=i.getSourceConfig();c instanceof Promise?c.then((function(e){return o.processResponse(200,e)})).catch(Kn):this.processResponse(200,c)}else{var l=function(e,t){var n=Ni,r=[];return e&&r.push("writeKey=".concat(e)),t&&r.push("lockIntegrationsVersion=".concat(t)),r.length>0&&(n=(n=Ni.concat(Ni.includes("?")?"&":"?")).concat(r.join("&"))),n}(e,this.lockIntegrationsVersion);i&&i.configUrl&&(l=function(e,t){var n=e;-1===n.indexOf("sourceConfig")&&(n="".concat(Wi(n),"/sourceConfig/")),n="/"===n.slice(-1)?n:"".concat(n,"/");var r=t.split("?")[1],i=n.split("?");return i.length>1&&i[1]!==r?"".concat(i[0],"?").concat(r):"".concat(n,"?").concat(r)}(i.configUrl,l));try{!function(e,t,n,r){var i=r.bind(e),o=new XMLHttpRequest;o.open("GET",t,!0),o.setRequestHeader("Authorization","Basic ".concat(btoa("".concat(n,":")))),o.onload=function(){var e=o.status,n=o.responseText;200===e?i(200,n):(Kn(new Error("".concat(Bn," ").concat(e," for url: ").concat(t))),i(e))},o.send()}(this,l,e,this.processResponse)}catch(e){Kn(e)}}}},{key:"arePolyfillsRequired",value:function(e){return(!e||"boolean"!=typeof e.polyfillIfRequired||e.polyfillIfRequired)&&(!String.prototype.endsWith||!String.prototype.startsWith||!String.prototype.includes||!Array.prototype.find||!Array.prototype.includes||"function"!=typeof window.URL||"undefined"==typeof Promise||!Object.entries||!Object.values||!String.prototype.replaceAll||!this.isDatasetAvailable()||"function"!=typeof TextDecoder||"function"!=typeof TextEncoder)}},{key:"load",value:function(e,t,n){if(!this.loaded){var r=T(n);if(this.arePolyfillsRequired(r)&&Ui){var i="polyfill";Ei(i,Ui,{skipDatasetAttributes:!0});var o=this,s=setInterval((function(){!window.hasOwnProperty(i)&&null===document.getElementById(i)||"undefined"==typeof Promise||(clearInterval(s),o.loadAfterPolyfill(e,t,r))}),100);setTimeout((function(){clearInterval(s)}),Dn)}else this.loadAfterPolyfill(e,t,r)}}},{key:"ready",value:function(e){this.loaded?"function"!=typeof e?xn.error("ready callback is not a function"):this.clientIntegrationsReady?e():this.readyCallbacks.push(e):this.toBeProcessedArray.push(["ready",e])}},{key:"initializeCallbacks",value:function(){var e=this;Object.keys(this.methodToCallbackMapping).forEach((function(t){e.methodToCallbackMapping.hasOwnProperty(t)&&e.on(t,(function(){}))}))}},{key:"registerCallbacks",value:function(e){var t=this;e||Object.keys(this.methodToCallbackMapping).forEach((function(e){t.methodToCallbackMapping.hasOwnProperty(e)&&window.rudderanalytics&&"function"==typeof window.rudderanalytics[t.methodToCallbackMapping[e]]&&(t.clientSuppliedCallbacks[e]=window.rudderanalytics[t.methodToCallbackMapping[e]])})),Object.keys(this.clientSuppliedCallbacks).forEach((function(e){t.clientSuppliedCallbacks.hasOwnProperty(e)&&t.on(e,t.clientSuppliedCallbacks[e])}))}},{key:"sendSampleRequest",value:function(){Ei("ad-block","//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js",{isNonNativeSDK:!0})}},{key:"startSession",value:function(e){this.uSession.start(e)}},{key:"endSession",value:function(){this.uSession.end()}},{key:"setAuthToken",value:function(e){"string"==typeof e?(this.storage.setAuthToken(e),this.transformationHandler.setAuthToken(e)):xn.error("Provided input should be in string format")}}]),e}(),Vs=new qs;m(Vs),window.addEventListener("error",(function(e){Kn(e,void 0,Vs)}),!0),Vs.initializeCallbacks(),Vs.registerCallbacks(!1);var Ws,Ys="load",Xs=window.rudderanalytics,Js=Array.isArray(Xs);if(Js)for(var $s=0;$s<Xs.length;){if(Xs[$s]&&Xs[$s][0]===Ys){Ws=Xs[$s],Xs.splice($s,1);break}$s+=1}!function(e){var t="ajs_trait_",n="ajs_prop_";function r(e,t){var n={};return Object.keys(e).forEach((function(r){r.startsWith(t)&&(n[r.substr(t.length)]=e[r])})),n}var i=function(e){var t={};try{new URL(e).searchParams.forEach((function(e,n){t[n]=e}))}catch(e){}return t}(e);i.ajs_aid&&Vs.toBeProcessedArray.push(["setAnonymousId",i.ajs_aid]),i.ajs_uid&&Vs.toBeProcessedArray.push(["identify",i.ajs_uid,r(i,t)]),i.ajs_event&&Vs.toBeProcessedArray.push(["track",i.ajs_event,r(i,n)])}(window.location.href),Js&&Xs.forEach((function(e){return Vs.toBeProcessedArray.push(e)})),Ws&&Ws.length>0&&(Ws.shift(),Vs[Ys].apply(Vs,l(Ws)));var Zs=Vs.ready.bind(Vs),ea=Vs.identify.bind(Vs),ta=Vs.page.bind(Vs),na=Vs.track.bind(Vs),ra=Vs.alias.bind(Vs),ia=Vs.group.bind(Vs),oa=Vs.reset.bind(Vs),sa=Vs.load.bind(Vs),aa=Vs.initialized=!0,ua=Vs.getUserId.bind(Vs),ca=Vs.getSessionId.bind(Vs),la=Vs.getUserTraits.bind(Vs),fa=Vs.getAnonymousId.bind(Vs),ha=Vs.setAnonymousId.bind(Vs),da=Vs.getGroupId.bind(Vs),pa=Vs.getGroupTraits.bind(Vs),ga=Vs.startSession.bind(Vs),ya=Vs.endSession.bind(Vs),va=Vs.setAuthToken.bind(Vs);exports.alias=ra,exports.endSession=ya,exports.getAnonymousId=fa,exports.getGroupId=da,exports.getGroupTraits=pa,exports.getSessionId=ca,exports.getUserId=ua,exports.getUserTraits=la,exports.group=ia,exports.identify=ea,exports.initialized=aa,exports.load=sa,exports.page=ta,exports.ready=Zs,exports.reset=oa,exports.setAnonymousId=ha,exports.setAuthToken=va,exports.startSession=ga,exports.track=na;


    //=====================================================================================

    console.log("This prints to the console of the page (injected only if the page url matched)");

    const initialiseRudderstack = () => {
        rudderanalytics.ready(() => {
            console.log("we are all set!!!");
        });

        rudderanalytics.load("<writeKey>", "<dataPlaneURL>");
    }


    const testEvents = () => {
        rudderanalytics.page();

        rudderanalytics.identify(
            "moumita123",
            {email: "<EMAIL>"},
            {
                page: {
                    path: "",
                    referrer: "",
                    search: "",
                    title: "",
                    url: "",
                },
            },
            () => {
                console.log("in identify call");
            }
        );

        rudderanalytics.track(
            "test track event GA3",
            {
                revenue: 30,
                currency: "USD",
                user_actual_id: 12345,
            },
            () => {
                console.log("in track call");
            }
        );
    };

    chrome.runtime.onMessage.addListener((obj, sender, response) => {
        const { type, value} = obj;

        if (type === "track") {
            testEvents(value);
        }
    });

    initialiseRudderstack();
})();
