<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>RudderStack JS SDK v3 Example</title>
    <script>
      (function() {
        "use strict";
        window.RudderSnippetVersion = "3.0.60";
        var identifier = "rudderanalytics";
        if (!window[identifier]) {
          window[identifier] = [];
        }
        var rudderanalytics = window[identifier];
        if (Array.isArray(rudderanalytics)) {
          if (rudderanalytics.snippetExecuted === true && window.console && console.error) {
            console.error("RudderStack JavaScript SDK snippet included more than once.");
          } else {
            rudderanalytics.snippetExecuted = true;
            window.rudderAnalyticsBuildType = "legacy";
            var sdkBaseUrl = "https://cdn.rudderlabs.com";
            var sdkVersion = "v3";
            var sdkFileName = "rsa.min.js";
            var scriptLoadingMode = "async";
            var methods = [ "setDefaultInstanceKey", "load", "ready", "page", "track", "identify", "alias", "group", "reset", "setAnonymousId", "startSession", "endSession", "consent" ];
            for (var i = 0; i < methods.length; i++) {
              var method = methods[i];
              rudderanalytics[method] = function(methodName) {
                return function() {
                  if (Array.isArray(window[identifier])) {
                    rudderanalytics.push([ methodName ].concat(Array.prototype.slice.call(arguments)));
                  } else {
                    var _methodName;
                    (_methodName = window[identifier][methodName]) === null || _methodName === undefined || _methodName.apply(window[identifier], arguments);
                  }
                };
              }(method);
            }
            try {
              new Function('class Test{field=()=>{};test({prop=[]}={}){return prop?(prop?.property??[...prop]):import("");}}');
              window.rudderAnalyticsBuildType = "modern";
            } catch (e) {}
            var head = document.head || document.getElementsByTagName("head")[0];
            var body = document.body || document.getElementsByTagName("body")[0];
            window.rudderAnalyticsAddScript = function(url, extraAttributeKey, extraAttributeVal) {
              var scriptTag = document.createElement("script");
              scriptTag.src = url;
              scriptTag.setAttribute("data-loader", "RS_JS_SDK");
              if (extraAttributeKey && extraAttributeVal) {
                scriptTag.setAttribute(extraAttributeKey, extraAttributeVal);
              }
              if (scriptLoadingMode === "async") {
                scriptTag.async = true;
              } else if (scriptLoadingMode === "defer") {
                scriptTag.defer = true;
              }
              if (head) {
                head.insertBefore(scriptTag, head.firstChild);
              } else {
                body.insertBefore(scriptTag, body.firstChild);
              }
            };
            window.rudderAnalyticsMount = function() {
              (function() {
                if (typeof globalThis === "undefined") {
                  var getGlobal = function getGlobal() {
                    if (typeof self !== "undefined") {
                      return self;
                    }
                    if (typeof window !== "undefined") {
                      return window;
                    }
                    return null;
                  };
                  var global = getGlobal();
                  if (global) {
                    Object.defineProperty(global, "globalThis", {
                      value: global,
                      configurable: true
                    });
                  }
                }
              })();
              window.rudderAnalyticsAddScript("".concat(sdkBaseUrl, "/").concat(sdkVersion, "/").concat(window.rudderAnalyticsBuildType, "/").concat(sdkFileName), "data-rsa-write-key", "__WRITE_KEY__");
            };
            if (typeof Promise === "undefined" || typeof globalThis === "undefined") {
              window.rudderAnalyticsAddScript("https://polyfill-fastly.io/v3/polyfill.min.js?version=3.111.0&features=Symbol%2CPromise&callback=rudderAnalyticsMount");
            } else {
              window.rudderAnalyticsMount();
            }
            var loadOptions = {
              logLevel: 'DEBUG',
              configUrl: 'https://api.rudderstack.com',
              destSDKBaseURL: sdkBaseUrl + '/' + sdkVersion + '/' + window.rudderAnalyticsBuildType + '/js-integrations',
              pluginsSDKBaseURL: sdkBaseUrl + '/' + sdkVersion + '/' + window.rudderAnalyticsBuildType + '/plugins',
              plugins: [
                'StorageEncryption',
                'XhrQueue'
              ]
            };
            rudderanalytics.load('__WRITE_KEY__', '__DATAPLANE_URL__', loadOptions);
          }
        }
      })();

      rudderanalytics.identify(
        'customUserID',
        {
          name: 'John Doe',
          title: 'CEO',
          email: '<EMAIL>',
          company: 'Company123',
          phone: '************',
          rating: 'Hot',
          city: 'Austin',
          postalCode: '12345',
          country: 'US',
          street: 'Sample Address',
          state: 'TX',
        },
        function (message) {
          console.log('in identify call', message);
        },
      );

      rudderanalytics.page(
        'Home',
        'Cart Viewed',
        {
          path: '',
          referrer: '',
          search: '',
          title: '',
          url: '',
        },
        function (message) {
          console.log('in page call', message);
        },
      );

      rudderanalytics.track(
        'test track event 1',
        {
          revenue: 30,
          currency: 'USD',
          user_actual_id: 12345,
        },
        function (message) {
          console.log('in track call 1', message);
        },
      );

      rudderanalytics.track(
        'test track event 2',
        {
          revenue: 45,
          currency: 'INR',
          user_actual_id: 333,
        },
        function (message) {
          console.log('in track call 2', message);
        },
      );

      rudderanalytics.track(
        'test track event 3',
        {
          revenue: 10003,
          currency: 'EUR',
          user_actual_id: 5678,
        },
        function (message) {
          console.log('in track call 3', message);
        },
      );

      rudderanalytics.ready(function () {
        console.log('All ready!!!');
      });

      document.addEventListener('RSA_Initialised', function (e) {
        console.log('RSA_Initialised', e.detail.analyticsInstance);
      });

      document.addEventListener('RSA_Ready', function (e) {
        console.log('RSA_Ready', e.detail.analyticsInstance);
      });

      // TODO: Call other APIs here
    </script>
  </head>
  <body>
    <h1>Page Loaded</h1>
    <br />

    <button data-testid="page-btn" onclick="page()">Page</button>
    <button data-testid="identify-btn" onclick="identify()">identify</button>
    <button data-testid="track-btn" onclick="track()">Track</button>
    <button data-testid="alias-btn" onclick="alias()">Alias</button>
    <button data-testid="group-btn" onclick="group()">Group</button>

    <p data-testid="action" id="action"></p>
    <p data-testid="payload" id="rudderElement"></p>

    <script>
      function page() {
        rudderanalytics.page(
          'Home',
          'Cart Viewed',
          {
            path: '',
            referrer: '',
            search: '',
            title: '',
            url: '',
          },
          function (rudderElement) {
            console.log('in page call');
            document.getElementById('action').innerHTML = 'Page called';
            document.getElementById('rudderElement').innerHTML = JSON.stringify(rudderElement);
          },
        );
      }

      function identify() {
        rudderanalytics.identify(
          'customUserID',
          {
            name: 'John Doe',
            title: 'CEO',
            email: '<EMAIL>',
            company: 'Company123',
            phone: '************',
            rating: 'Hot',
            city: 'Austin',
            postalCode: '12345',
            country: 'US',
            street: 'Sample Address',
            state: 'TX',
          },
          {},
          function (rudderElement) {
            console.log('in identify call');
            document.getElementById('action').innerHTML = 'Identify called';
            document.getElementById('rudderElement').innerHTML = JSON.stringify(rudderElement);
          },
        );
      }

      function track() {
        rudderanalytics.track(
          'test track event 1',
          {
            revenue: 30,
            currency: 'USD',
            user_actual_id: 12345,
          },
          function (rudderElement) {
            console.log('in track call');
            document.getElementById('action').innerHTML = 'Track called';
            document.getElementById('rudderElement').innerHTML = JSON.stringify(rudderElement);
          },
        );
      }

      function alias() {
        rudderanalytics.alias('alias-user-id', function (rudderElement) {
          console.log('alias call');
          document.getElementById('action').innerHTML = 'Alias called';
          document.getElementById('rudderElement').innerHTML = JSON.stringify(rudderElement);
        });
      }

      function group() {
        rudderanalytics.group(
          'sample_group_id',
          {
            name: 'Apple Inc.',
            location: 'USA',
          },
          function (rudderElement) {
            console.log('group call');
            document.getElementById('action').innerHTML = 'Group called';
            document.getElementById('rudderElement').innerHTML = JSON.stringify(rudderElement);
          },
        );
      }
    </script>
  </body>
</html>
