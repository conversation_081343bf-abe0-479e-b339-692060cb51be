There are 3 sample apps:

1.  NPM package: This is for Production Use Case where rudderstack would be installed using npm install command and the app will initialise its instance.

2.  V1.1 CDN: This app can be used for any environment either for dev, staging or prod by providing the right configurations in config.js file .This uses the legacy SDK.

3.  V3 CDN: This app can be used for any environment either for dev, staging or prod by providing the right configurations in config.js file . This uses the V3 SDK.

For Customers: The NPM package is recommended for production use. The other versions are intended primarily for testing and development purposes.
