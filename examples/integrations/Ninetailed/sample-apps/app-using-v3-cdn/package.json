{"name": "sample-app", "version": "0.1.0", "private": true, "dependencies": {"@ninetailed/experience.js": "7.11.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "react": "19.1.0", "react-dom": "19.1.0", "react-scripts": "5.0.1", "web-vitals": "4.2.4"}, "overrides": {"nth-check": "2.1.1", "postcss": "8.4.47", "react-scripts": {"workbox-webpack-plugin": "7.3.0"}}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "clean": "rm -rf node_modules package-lock.json dist .next", "setup": "npm install"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}