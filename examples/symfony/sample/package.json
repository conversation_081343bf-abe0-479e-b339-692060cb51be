{"devDependencies": {"@babel/core": "7.26.10", "@babel/preset-env": "7.26.9", "@symfony/webpack-encore": "5.1.0", "core-js": "3.41.0", "regenerator-runtime": "0.14.1", "webpack": "5.99.5", "webpack-cli": "5.1.4", "webpack-dev-server": "5.2.1", "webpack-notifier": "1.15.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress", "clean": "rm -rf node_modules package-lock.json public/build", "setup": "npm install"}, "name": "sample", "version": "1.0.0", "main": "webpack.config.js", "directories": {"test": "tests"}, "keywords": [], "author": "", "description": "", "dependencies": {"@rudderstack/analytics-js": "*"}}