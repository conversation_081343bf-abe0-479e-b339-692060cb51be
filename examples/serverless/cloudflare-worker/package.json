{"name": "example-cloudflare-worker", "version": "0.0.1", "private": true, "scripts": {"deploy": "wrangler deploy", "start": "wrangler dev", "config": "node scripts/update-wrangler-config.js"}, "devDependencies": {"dotenv": "16.4.5", "wrangler": "3.82.0"}, "dependencies": {"@rudderstack/analytics-js-service-worker": "*", "@vespaiach/axios-fetch-adapter": "0.3.1", "host-proxy": "1.0.2", "http-forward": "0.1.3"}}