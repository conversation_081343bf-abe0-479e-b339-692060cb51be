{"name": "sample-app", "version": "0.1.0", "private": true, "dependencies": {"@rudderstack/analytics-js": "*", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "react": "19.1.0", "react-dom": "19.1.0", "web-vitals": "4.2.4"}, "devDependencies": {"@testing-library/dom": "10.4.0", "@types/jest": "29.5.14", "@types/node": "22.14.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "react-scripts": "5.0.1"}, "overrides": {"nth-check": "2.1.1", "postcss": "8.4.35"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "clean": "rm -rf node_modules package-lock.json build", "setup": "npm install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}