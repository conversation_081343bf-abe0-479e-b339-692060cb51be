<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
    <script>
      (function() {
        "use strict";
        window.RudderSnippetVersion = "3.0.60";
        var identifier = "rudderanalytics";
        if (!window[identifier]) {
          window[identifier] = [];
        }
        var rudderanalytics = window[identifier];
        if (Array.isArray(rudderanalytics)) {
          if (rudderanalytics.snippetExecuted === true && window.console && console.error) {
            console.error("RudderStack JavaScript SDK snippet included more than once.");
          } else {
            rudderanalytics.snippetExecuted = true;
            window.rudderAnalyticsBuildType = "legacy";
            var sdkBaseUrl = "https://cdn.rudderlabs.com";
            var sdkVersion = "v3";
            var sdkFileName = "rsa.min.js";
            var scriptLoadingMode = "async";
            var methods = [ "setDefaultInstanceKey", "load", "ready", "page", "track", "identify", "alias", "group", "reset", "setAnonymousId", "startSession", "endSession", "consent" ];
            for (var i = 0; i < methods.length; i++) {
              var method = methods[i];
              rudderanalytics[method] = function(methodName) {
                return function() {
                  if (Array.isArray(window[identifier])) {
                    rudderanalytics.push([ methodName ].concat(Array.prototype.slice.call(arguments)));
                  } else {
                    var _methodName;
                    (_methodName = window[identifier][methodName]) === null || _methodName === undefined || _methodName.apply(window[identifier], arguments);
                  }
                };
              }(method);
            }
          }
        }
      })();
      // Below line is only for demonstration purpose, SPA code is better place for auto page call
      window.rudderanalytics.page('sample page call');
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
