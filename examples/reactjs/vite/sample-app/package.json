{"name": "sample-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint --config eslint.config.js src", "preview": "vite preview", "clean": "rm -rf node_modules package-lock.json dist", "setup": "npm install"}, "dependencies": {"@rudderstack/analytics-js": "*", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@typescript-eslint/eslint-plugin": "8.29.1", "@typescript-eslint/parser": "8.29.1", "@vitejs/plugin-react-swc": "3.8.1", "eslint": "9.24.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.19", "globals": "16.0.0", "typescript": "5.8.3", "vite": "6.2.5"}}