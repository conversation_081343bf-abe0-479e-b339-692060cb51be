{"name": "sample-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf node_modules package-lock.json .next", "setup": "npm install"}, "dependencies": {"@rudderstack/analytics-js": "*", "@tailwindcss/postcss": "4.1.3", "eslint": "9.24.0", "eslint-config-next": "15.2.5", "next": "15.2.5", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@types/react": "19.1.0", "autoprefixer": "10.4.21", "postcss": "8.5.3", "tailwindcss": "4.1.3"}, "overrides": {"cross-spawn": "7.0.6", "nanoid": "3.3.8"}}