{"name": "sample-app-ts", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf node_modules package-lock.json .next", "setup": "npm install"}, "dependencies": {"@rudderstack/analytics-js": "*", "@tailwindcss/postcss": "4.1.3", "next": "15.2.4", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@types/node": "22.14.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "10.4.21", "eslint": "9.24.0", "eslint-config-next": "15.2.4", "postcss": "8.5.3", "tailwindcss": "4.1.3", "typescript": "5.8.3"}}