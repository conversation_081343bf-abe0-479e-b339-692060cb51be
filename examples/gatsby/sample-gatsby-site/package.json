{"name": "sample-gatsby-site", "version": "1.0.0", "private": true, "description": "sample-gatsby-site", "author": "RudderStack", "keywords": ["gatsby"], "scripts": {"develop": "gatsby develop", "start": "gatsby develop", "build": "gatsby build", "serve": "gatsby serve", "clean": "gatsby clean && rm -rf node_modules package-lock.json", "setup": "npm install", "typecheck": "tsc --noEmit"}, "dependencies": {"@rudderstack/analytics-js": "*", "dotenv": "16.4.7", "gatsby": "5.14.3", "gatsby-plugin-env-variables": "2.3.0", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@types/node": "22.10.5", "@types/react": "18.3.9", "@types/react-dom": "18.3.0", "typescript": "5.7.2"}, "overrides": {"axios": "1.8.4", "cookie": "0.7.0", "path-to-regexp": "0.1.12", "gatsby-cli": {"pretty-error": "4.0.0"}, "gatsby": {"css-minimizer-webpack-plugin": "4.2.2"}}}