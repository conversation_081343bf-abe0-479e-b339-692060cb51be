{"name": "@rudderstack/analytics-js-monorepo", "version": "3.90.0", "private": true, "description": "Monorepo for RudderStack Analytics JS SDK", "workspaces": ["packages/*"], "scripts": {"setup": "npm i --include=optional && npm run build:package:modern", "setup:ci": "npm run setup:deps && npm run build:package:modern", "setup:deps": "npm ci --prefer-offline --no-audit", "clean": "nx run-many -t clean && nx reset && git clean -xdf node_modules", "clean:cache": "rimraf -rf ./node_modules/.cache && rimraf -rf ./.nx/cache", "start": "nx run-many --targets=start --parallel=3 --projects=@rudderstack/analytics-js-integrations,@rudderstack/analytics-js-plugins,@rudderstack/analytics-js", "start:modern": "nx run-many --targets=start-modern --parallel=3 --projects=@rudderstack/analytics-js-integrations,@rudderstack/analytics-js-plugins,@rudderstack/analytics-js", "build": "nx run-many -t build", "build:ci": "nx affected -t build --base=$BASE_REF", "build:modern": "nx run-many -t build:modern", "build:modern:ci": "nx affected -t build:modern --base=$BASE_REF", "build:browser": "nx run-many -t build:browser", "build:browser:modern": "nx run-many -t build:browser:modern", "build:package": "nx run-many -t build:package", "build:package:modern": "nx run-many -t build:package:modern", "package": "nx run-many -t package", "test": "nx run-many -t test", "test:pre-commit:affected": "nx affected -t test --base=HEAD", "test:ci": "nx affected -t test --base=$BASE_REF --configuration=ci --runInBand --maxWorkers=1 --forceExit", "check:lint:ci": "nx affected -t check:lint:ci --base=$BASE_REF --verbose", "check:lint": "nx run-many -t check:lint --verbose", "check:size": "nx run-many -t check:size --verbose", "check:size:build": "nx run-many -t check:size:build", "check:size:build:pre-commit:affected": "nx affected -t check:size:build --base=HEAD", "check:size:json": "npm run check:size:json:ci -- --verbose", "check:size:json:pre-commit:affected": "nx affected -t check:size:json --base=HEAD --verbose", "check:size:json:ci": "nx run-many -t check:size:json", "check:circular": "nx run-many -t check:circular --verbose", "check:support": "NODE_ENV=production npx browserslist --mobile-to-desktop", "check:support:modern": "NODE_ENV=modern npx browserslist --mobile-to-desktop", "check:duplicates": "nx run-many -t check:duplicates --verbose", "check:security": "npm audit --recursive --audit-level=high --omit=dev", "check:pub": "nx run-many -t check:pub --verbose", "check:pub:ci": "nx affected -t check:pub --verbose --base=$BASE_REF", "format": "prettier --write .", "lint:fix": "nx run-many -t check:lint --fix", "prepare": "husky", "pre-commit": "npm run test:pre-commit:affected && npm run check:size:build:pre-commit:affected && npm run check:size:json:pre-commit:affected && npx lint-staged", "commit-msg": "commitlint --edit", "commit": "git-cz", "postinstall": "patch-package", "build:integrations": "nx run-many --targets=build:browser,build:browser:modern --projects=@rudderstack/analytics-js-integrations", "build:v3": "nx run-many -t build --exclude=rudder-sdk-js,@rudderstack/analytics-js-sanity-suite,@rudderstack/analytics-js-integrations", "build:v1.1": "nx run-many -t build --projects=rudder-sdk-js", "build:sanity": "rm -rf packages/sanity-suite/dist && nx run-many --targets=build:all --projects=@rudderstack/analytics-js-sanity-suite", "bump-version:monorepo": "npm version minor --git-tag-version false", "release": "nx affected --base=origin/main --target=version --parallel=1 --skipCommitTypes=docs,ci,chore,test --baseBranch=$BASE_BRANCH", "release:nx": "nx release --skip-publish --verbose --dry-run", "release:github": "nx affected --target=github --parallel=1 --skipCommitTypes=docs,ci,chore,test", "reset:nx": "nx reset", "deploy:npm": "nx affected --target=deploy --parallel=1 --skipCommitTypes=docs,ci,chore,test"}, "author": "RudderStack", "license": "Elastic-2.0", "bugs": {"url": "https://github.com/rudderlabs/rudder-sdk-js/issues"}, "homepage": "https://github.com/rudderlabs/rudder-sdk-js/blob/main/#readme", "repository": {"type": "git", "url": "https://github.com/rudderlabs/rudder-sdk-js.git"}, "dependencies": {"@lukeed/uuid": "2.0.1", "@ndhoule/each": "2.0.1", "@ndhoule/extend": "2.0.0", "@preact/signals-core": "1.11.0", "@segment/localstorage-retry": "1.3.0", "@segment/top-domain": "3.0.1", "@vespaiach/axios-fetch-adapter": "0.3.1", "assert": "2.1.0", "axios": "1.10.0", "axios-retry": "4.5.0", "component-each": "0.2.6", "component-emitter": "2.0.0", "component-type": "2.0.0", "crypto-es": "2.1.0", "crypto-js": "4.2.0", "deep-object-diff": "1.1.9", "error-stack-parser": "2.1.4", "get-value": "4.0.1", "is": "3.3.0", "join-component": "1.1.0", "lodash.get": "4.4.2", "lodash.isempty": "4.4.0", "lodash.isequal": "4.5.0", "lodash.isundefined": "3.0.1", "lodash.pickby": "4.6.0", "lodash.tostring": "4.1.4", "md5": "2.3.0", "ms": "2.1.3", "obj-case": "0.2.1", "object-path": "0.11.8", "on-body": "0.0.1", "ramda": "0.31.3", "rudder-component-cookie": "0.0.1", "storejs": "2.1.0"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/eslint-parser": "7.28.0", "@babel/plugin-transform-arrow-functions": "7.27.1", "@babel/plugin-transform-class-properties": "7.27.1", "@babel/plugin-transform-object-assign": "7.27.1", "@babel/plugin-transform-private-methods": "7.27.1", "@babel/plugin-transform-private-property-in-object": "7.27.1", "@babel/plugin-transform-runtime": "7.28.0", "@babel/preset-env": "7.28.0", "@babel/preset-typescript": "7.27.1", "@commitlint/config-conventional": "19.8.1", "@commitlint/config-nx-scopes": "19.8.1", "@digitalroute/cz-conventional-changelog-for-jira": "8.0.1", "@eslint/js": "9.30.1", "@jscutlery/semver": "5.6.0", "@nx/eslint": "21.2.2", "@nx/eslint-plugin": "21.2.2", "@nx/jest": "21.2.2", "@nx/js": "21.2.2", "@nx/workspace": "21.2.2", "@originjs/vite-plugin-federation": "1.4.1", "@rollup/plugin-alias": "5.1.1", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "28.0.6", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "16.0.1", "@rollup/plugin-replace": "6.0.2", "@rollup/plugin-terser": "0.4.4", "@size-limit/file": "11.2.0", "@size-limit/webpack": "11.2.0", "@swc-node/register": "1.10.10", "@swc/core": "1.12.11", "@types/component-emitter": "1.2.14", "@types/jest": "30.0.0", "@types/node": "24.0.10", "@types/ramda": "0.30.2", "@typescript-eslint/eslint-plugin": "8.36.0", "@typescript-eslint/parser": "8.36.0", "babel-plugin-transform-object-hasown": "1.1.0", "commitizen": "4.3.1", "commitlint": "19.8.1", "core-js": "3.44.0", "cross-env": "7.0.3", "dotenv": "17.1.0", "each": "2.7.2", "eslint": "9.30.1", "eslint-config-prettier": "10.1.5", "eslint-import-resolver-node": "0.3.9", "eslint-import-resolver-typescript": "4.4.4", "eslint-plugin-compat": "6.0.2", "eslint-plugin-import": "2.32.0", "eslint-plugin-sonarjs": "3.0.4", "eslint-plugin-unicorn": "59.0.1", "http-server": "14.1.1", "husky": "9.1.7", "isomorphic-fetch": "3.0.0", "jest": "30.0.4", "jest-date-mock": "1.0.10", "jest-environment-jsdom": "30.0.4", "jest-junit": "16.0.0", "jest-sonar": "0.2.16", "jest-watch-typeahead": "3.0.1", "join-component": "1.1.0", "jscpd": "4.0.5", "lint-staged": "16.1.2", "madge": "8.0.0", "msw": "2.10.3", "nx": "21.2.2", "patch-package": "8.0.0", "prettier": "3.6.2", "publint": "0.3.12", "rollup": "4.44.2", "rollup-plugin-copy": "3.5.0", "rollup-plugin-delete": "3.0.1", "rollup-plugin-dts": "6.2.1", "rollup-plugin-exclude-dependencies-from-bundle": "1.1.24", "rollup-plugin-external-globals": "0.13.0", "rollup-plugin-filesize": "10.0.0", "rollup-plugin-generate-html-template": "1.7.0", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-polyfill-node": "0.13.0", "rollup-plugin-serve": "3.0.0", "rollup-plugin-typescript2": "0.36.0", "rollup-plugin-visualizer": "6.0.3", "serve": "14.2.4", "size-limit": "11.2.0", "ts-jest": "29.4.0", "ts-node": "10.9.2", "tslib": "2.8.1", "typescript": "5.8.3", "user-agent-data-types": "0.4.2"}, "optionalDependencies": {"@nx/nx-darwin-arm64": "21.2.2", "@nx/nx-darwin-x64": "21.2.2", "@nx/nx-linux-x64-gnu": "21.2.2", "@nx/nx-win32-x64-msvc": "21.2.2", "@rollup/rollup-linux-x64-gnu": "4.44.2"}, "overrides": {"rudder-component-cookie": {"debug": "2.6.9"}, "@segment/localstorage-retry": {"debug": "2.6.9"}}, "lint-staged": {"*.{json,js,md,ts}": "prettier --write"}, "config": {"commitizen": {"path": "./scripts/commitizen"}}, "browserslist": {"production": ["> 0.1%", "IE >= 11"], "modern": ["defaults and supports es6-module-dynamic-import and not dead"]}}