{"name": "@rudderstack/analytics-js-integrations", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/analytics-js-integrations/src", "projectType": "library", "tags": ["type:sdk", "scope:integrations"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/analytics-js-integrations/jest.config.mjs", "passWithNoTests": true, "codeCoverage": true, "watchAll": false, "forceExit": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"hasTypeAwareRules": true, "lintFilePatterns": ["packages/analytics-js-integrations/src/**/*.{ts,js}", "packages/analytics-js-integrations/{package,project}.json"]}, "configurations": {"ci": {"force": true, "outputFile": "packages/analytics-js-integrations/reports/eslint.json", "format": "json"}}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventionalcommits", "tagPrefix": "{projectName}@", "trackDeps": true}}, "github": {"executor": "@jscutlery/semver:github", "options": {"tag": "@rudderstack/analytics-js-integrations@3.14.3", "title": "@rudderstack/analytics-js-integrations@3.14.3", "discussion-category": "@rudderstack/analytics-js-integrations@3.14.3", "notesFile": "./packages/analytics-js-integrations/CHANGELOG_LATEST.md"}}}}