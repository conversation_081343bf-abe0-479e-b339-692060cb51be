# Supporting Custom Events in Microsoft Clarity Destination for RudderStack

---

## Overview

Microsoft Clarity is a user behavior analytics tool that provides features like session recordings, heatmaps, and smart events to help website owners understand user interactions. RudderStack is a customer data platform that routes event data from various sources to destinations, including Microsoft Clarity. Adding support for custom events in the Clarity destination allows users to track key user actions beyond the default set, enhancing analytics flexibility.

---

## Key Concepts & Features

### 1. Microsoft Clarity Event Types

- **Smart Events**: Predefined or code-free user actions (e.g., “Purchase”, “Add to Cart”) tracked automatically or configured in the Clarity dashboard. (Add a mapping in UI for smart events)
  - Purchase
  - Add to Cart
  - Begin Checkout
  - Contact Us
  - Submit Form
  - Request Quote
  - Sign Up
  - Login
  - Download
- **Custom Events (API Events)**: User-defined events sent via Clarity’s JavaScript API. (Any other events apart from the ones mapped will be sent here)
- **Custom Tags**: Key-value pairs for session-level metadata, used for filtering and segmentation. (TBD)

### 2. Clarity Client API for Custom Events

- **Send Custom Events**:
  ```
  window.clarity("event", "eventName");

  ```
  - `eventName` is a string identifier for the custom action.
  - Events appear in Clarity’s dashboard, filters, and session recordings.
  - No additional event properties/parameters are supported—only the event name.

### 3. RudderStack Integration

- **Device Mode Only**: Clarity destination in RudderStack supports only device mode for web sources.
- **Supported Message Types**: Only `identify` and (with enhancement) `track` events should be mapped to Clarity.
- **Current Mapping**: `identify` events are mapped to Clarity identifiers.
- **Custom Event Support**: To support custom events, RudderStack should map `track` events to Clarity’s custom event API.

---

## Implementation Guidance

### 1. Event Mapping

- **RudderStack `track` → Clarity Custom Event**
  - For every `track` event, send:
    ```
    window.clarity("event", );

    ```
    - : Use the `event` property from the RudderStack `track` payload.
- **Custom Properties**
  - **Clarity does not support properties on events.**

### 2. Limitations & Considerations

- **No Event Parameters**: Clarity custom events accept only an event name, not arbitrary properties

### 3. Example Integration

```
// On a 'track' event from RudderStack:
rudderanalytics.track("Product Added", { productId: "123", category: "Shoes" });

// In the Clarity destination:
window.clarity("event", "Product Added");
// properties are ignored
```

### 4. Configuration

- **Project ID**: Required for Clarity integration.
- **Consent Management**: Optionally configure via RudderStack settings to trigger Clarity’s consent API.

---

## Best Practices

- **Event Naming**: Use consistent, descriptive names for custom events.
- **Validation**: Test event flow end-to-end to ensure events and tags appear correctly in Clarity dashboards and filters.
- **Documentation**: Clearly document which RudderStack events are mapped to Clarity events and which properties are forwarded as tags.

---

## References

- [Clarity client API | Microsoft Learn](https://learn.microsoft.com/en-us/clarity/setup-and-installation/clarity-api)
- [Custom tags in Clarity | Microsoft Learn](https://learn.microsoft.com/en-us/clarity/filters/custom-tags)
- [Microsoft Clarity | RudderStack Docs](https://www.rudderstack.com/docs/destinations/streaming-destinations/microsoft-clarity/)
