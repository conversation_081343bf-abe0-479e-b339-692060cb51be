# Microsoft Clarity

<aside>
🚨 **IMPORTANT: Make sure to fill out the below sections colored red.**

</aside>

Prepared by: @<PERSON>di<PERSON> <PERSON> @Mihir Bhalala

Enhancement([supporting track events](https://www.notion.so/Supporting-Custom-Events-in-Microsoft-Clarity-Destination-for-RudderStack-22af2b415dd08047826afc9fbf291d3b?pvs=21)): @Yashasvi Bajpai

## Genre

- Heatmap & Recordings

## Overview

[Clarity](https://clarity.microsoft.com/) is a cutting-edge behavioral analysis tool that helps you understand user interaction with your website. By using Clarity's robust analysis tools, you can enhance your website for your clients and your business.

### Useful Links

- https://learn.microsoft.com/en-us/clarity/clarity-api - API Spec
- https://learn.microsoft.com/en-us/clarity/about-clarity - Overall Doc
- https://learn.microsoft.com/en-us/clarity/setup-and-installation/clarity-api#add-custom-events - For custom events

---

## Modes

|                 | **Web** | **Mobile** | **Server** |
| --------------- | ------- | ---------- | ---------- |
| **Device Mode** | Yes     |            |            |
| **Cloud Mode**  |         |            |            |

---

### Test API Support

Yes/No

<aside>
💡 Test API is not supported for those destinations that have OAuth constraints and/or in which the final event delivery is done from the server, i.e., warehouse, object storage, and custom destinations (Kafka, PubSub, etc.)

</aside>

---

## Developer Notes

- SDK Script
  ```jsx
  <script type="text/javascript">
      (function(c,l,a,r,i,t,y){
          c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "ej81fqgqfr");
  </script>
  ```

## Device Mode Integration (if applicable)

## Transformation Plan

### Supported Calls

- Device Mode
  - Identify
    - identify call will create user on Microsoft clarity dashboard
    - If your project is configured to require [cookie consent](https://learn.microsoft.com/en-us/clarity/cookie-consent), Clarity uses a unique first-party cookie to track each user with a browser cookie. If cookie consent is required, you must call the consent API to indicate that you have consent from the user to track them using a cookie. Otherwise, Clarity gives each page a unique ID instead of a cookie when cookie consent is required. Clarity will automatically set the first-party cookie by default, and you don't need to call this API.
    - SDK identify call
      ```jsx

      window.clarity("identify", "user@3", "dvnefhbjhb", "page@3");
      window.clarity("set", "email", "<EMAIL>"); --> context.traits
      window.clarity("set", "firstName", "saurabh");
      ```
    ![Screenshot 2022-11-16 at 7.57.11 PM.png](https://s3-us-west-2.amazonaws.com/secure.notion-static.com/30478686-e59b-4483-af27-6f82e68080c9/Screenshot_2022-11-16_at_7.57.11_PM.png)
    | RudderStack property/event  | Destination property/event | Presence |
    | --------------------------- | -------------------------- | -------- |
    | userId                      | customuserid               | Required |
    | context.sessionId           | customsessionid            | Optional |
    | context.traits.customPageId | custompageid               | Optional |
    - Sample code for docs
      ```jsx
      rudderanalytics.identify('19Ub238Y5', {
        name: 'Chris Miles',
        email: '<EMAIL>',
      });
      ```
  - Track
    API docs: https://learn.microsoft.com/en-us/clarity/setup-and-installation/clarity-api#add-custom-events
    Track call will fire the custom event.
    Clarity currently supports nine auto event types:
    - Purchase
    - Add to Cart
    - Begin Checkout
    - Contact Us
    - Submit Form
    - Request Quote
    - Sign Up
    - Login
    - Download
    Apart from these, any user-defined event is also supported
    - SDK Track call
      ```json
      window.clarity("event", "newsletterSignup")
      window.clarity("event", "Purchase")
      ```

---

## Destination Definition

- Microsoft Clarity Project Id → Required
- Microsoft Clarity Cookie Consent → by default enabled

![Screenshot 2022-11-17 at 2.24.41 PM.png](https://s3-us-west-2.amazonaws.com/secure.notion-static.com/5ed9b3f1-e01b-4e3b-805f-5988fdc05c25/Screenshot_2022-11-17_at_2.24.41_PM.png)

### User Inputs

---

## Doc guideline

**_(Any information which needs to be mentioned in the docs apart from what is covered in the rest of the spec. Remove this statement afterward)_**

→ data will take 1-2 hour reflect on microsoft clarity dashboard

### Edge Cases

---

## Test Cases

| ID                                                             | Description                  | Input Scenario                      | Expected Output Behavior       |
| -------------------------------------------------------------- | ---------------------------- | ----------------------------------- | ------------------------------ |
| 1                                                              | identify call without userId | rudderanalytics.identify();         | request failed with an error : |
| [Microsoft Clarity] :: userId is required for an identify call |
| 2                                                              | identify call with userId    | rudderanalytics.identify(”user@1”); | passed with status code 200    |

---

### FAQs

-

# Test results

[Test Results](https://www.notion.so/Test-Results-e4880ee56c4549e4b93a6f02272db755?pvs=21)

[Common Checklist for Quality Assessment While Dev Testing](https://www.notion.so/Common-Checklist-for-Quality-Assessment-While-Dev-Testing-e8a9f00a02654e4bb90a77b6e23f8bbd?pvs=21)

# Demo

[Demo Video](https://www.loom.com/share/60461a60815f431d8dee0869e810fdc0)
