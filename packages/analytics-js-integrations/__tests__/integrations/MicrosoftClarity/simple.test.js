// Mock the nativeSdkLoader
jest.mock('../../../src/integrations/MicrosoftClarity/nativeSdkLoader', () => ({
  loadNativeSdk: jest.fn(),
}));

// Mock the logger
jest.mock('../../../src/utils/logger', () => {
  return jest.fn().mockImplementation(() => ({
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    setLogLevel: jest.fn(),
  }));
});

import MicrosoftClarity from '../../../src/integrations/MicrosoftClarity/browser';

describe('MicrosoftClarity track method', () => {
  let msClarity;

  beforeEach(() => {
    msClarity = new MicrosoftClarity(
      { projectId: 'test-project-123', cookieConsent: true },
      { loglevel: 'debug' },
    );
    window.clarity = jest.fn();
  });

  afterEach(() => {
    delete window.clarity;
  });

  test('should call clarity event with event name', () => {
    msClarity.track({
      message: {
        event: 'Product Added',
        properties: {
          productId: '123',
        },
      },
    });

    expect(window.clarity).toHaveBeenCalledWith('event', 'Product Added');
  });
});
