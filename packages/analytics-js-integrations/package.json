{"name": "@rudderstack/analytics-js-integrations", "version": "3.14.3", "private": true, "description": "RudderStack JavaScript SDK device mode integrations", "main": "dist/npm/modern/cjs/index.js", "module": "dist/npm/modern/esm/index.js", "exports": {".": {"types": "./dist/npm/index.d.ts", "require": "./dist/npm/modern/cjs/index.js", "import": "./dist/npm/modern/esm/index.js"}, "./legacy": {"types": "./dist/npm/index.d.ts", "require": "./dist/npm/legacy/cjs/index.js", "import": "./dist/npm/legacy/esm/index.js"}}, "types": "./dist/npm/index.d.ts", "typesVersions": {"*": {"*": ["./dist/npm/index.d.ts"]}}, "publishConfig": {}, "files": ["dist/npm", "LICENSE.md", "README.md", "CHANGELOG.md"], "keywords": ["analytics", "rudder"], "author": "RudderStack", "license": "Elastic-2.0", "repository": {"type": "git", "url": "git+https://github.com/rudderlabs/rudder-sdk-js.git", "directory": "packages/analytics-js-integrations"}, "bugs": {"url": "https://github.com/rudderlabs/rudder-sdk-js/issues"}, "homepage": "https://github.com/rudderlabs/rudder-sdk-js/blob/main/packages/analytics-js-integrations/README.md", "scripts": {"clean": "rimraf -rf ./dist && rimraf -rf ./node_modules/.cache && rimraf -rf ./reports", "start": "npm run build && npm run serve", "start:modern": "npm run build:modern && npm run serve", "build": "npm run build:browser && npm run build:package", "build:modern": "npm run build:browser:modern && npm run build:package:modern", "build:browser": "npm run build:integration:all", "build:browser:modern": "npm run build:integration:all:modern", "build:npm": "exit 0", "build:npm:modern": "exit 0", "build:package": "exit 0", "build:package:modern": "exit 0", "test": "nx test --maxWorkers=50%", "test:ci": "nx test --configuration=ci --runInBand --maxWorkers=1 --forceExit", "check:lint": "nx lint", "check:lint:ci": "nx lint --configuration=ci", "check:size:build": "npm run build:browser && npm run build:browser:modern && npm run build:package && npm run build:package:modern", "check:size": "npm run check:size:build && size-limit", "check:size:json": "size-limit --json", "check:circular": "madge --circular --extensions js,ts src || exit 0", "check:support": "NODE_ENV=production npx browserslist --mobile-to-desktop", "check:support:modern": "NODE_ENV=modern npx browserslist --mobile-to-desktop", "check:duplicates": "jscpd src", "check:security": "npm audit --recursive --audit-level=high", "serve": "npx serve ./dist -p 3005 --cors", "package": "exit 0", "release": "exit 0", "build:integration": "rollup -c --environment PROD_DEBUG,ENV:prod,UGLIFY", "build:integration:modern": "BROWSERSLIST_ENV=modern rollup -c --environment PROD_DEBUG,ENV:prod,UGLIFY", "build:integration:bundle-size": "VISUALIZER=true npm run build:integration", "build:integration:cli": "rollup -c --environment VERSION:$npm_package_version,PROD_DEBUG,ENV:prod,UGLIFY,INTG_NAME:$npm_config_intg", "build:integration:bundle-size:cli": "rollup -c --environment VERSION:$npm_package_version,VISUALIZER:true,PROD_DEBUG,ENV:prod,UGLIFY,INTG_NAME:$npm_config_intg", "build:integration:all": "node ./scripts/integrationBuildScript.mjs", "build:integration:all:modern": "BROWSERSLIST_ENV=modern npm run build:integration:all", "build:integration:all:bundle-size": "VISUALIZER=true npm run build:integration:all", "build:integration:all:bundle-size:modern": "VISUALIZER=true npm run build:integration:all:modern"}, "dependencies": {"@rudderstack/analytics-js-common": "*", "@ndhoule/each": "2.0.1", "crypto-js": "4.2.0", "is": "3.3.0", "md5": "2.3.0", "obj-case": "0.2.1", "on-body": "0.0.1", "component-each": "0.2.6", "@ndhoule/extend": "2.0.0", "@lukeed/uuid": "2.0.1", "get-value": "4.0.1", "ramda": "0.31.3"}, "devDependencies": {}, "overrides": {}, "browserslist": {"production": ["defaults", "Edge >= 80", "Firefox >= 47", "IE >= 11", "Chrome >= 54", "Safari >= 7", "Opera >= 43"], "modern": ["defaults", "supports es6-module-dynamic-import"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 edge version", "last 1 safari version"]}}