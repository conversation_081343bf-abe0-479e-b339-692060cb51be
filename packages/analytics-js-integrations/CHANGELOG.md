# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [3.14.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.14.2...@rudderstack/analytics-js-integrations@3.14.3) (2025-06-20)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.21.0`
## [3.14.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.14.1...@rudderstack/analytics-js-integrations@3.14.2) (2025-06-11)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.20.0`
## [3.14.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.14.0...@rudderstack/analytics-js-integrations@3.14.1) (2025-05-26)


### Bug Fixes

* use klaviyo push function to send events ([#2190](https://github.com/rudderlabs/rudder-sdk-js/issues/2190)) ([7691d9d](https://github.com/rudderlabs/rudder-sdk-js/commit/7691d9d3ecdf7f35b0719291518e57535d1151e4))

## [3.14.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.13.2...@rudderstack/analytics-js-integrations@3.14.0) (2025-05-09)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.19.0`

### Features

* onboarding userpilot integration ([#2103](https://github.com/rudderlabs/rudder-sdk-js/issues/2103)) ([9065069](https://github.com/rudderlabs/rudder-sdk-js/commit/90650693cd477ff3987174f8f6ec5972b034f2cb))

## [3.13.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.13.1...@rudderstack/analytics-js-integrations@3.13.2) (2025-04-25)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.18.0`

### Bug Fixes

* add validation for non array products ([#2182](https://github.com/rudderlabs/rudder-sdk-js/issues/2182)) ([2294bdd](https://github.com/rudderlabs/rudder-sdk-js/commit/2294bdde8c967295e18533a2bd20e17231ae4768))
* added mixpanel missing config properties ([#2125](https://github.com/rudderlabs/rudder-sdk-js/issues/2125)) ([fe1ba3e](https://github.com/rudderlabs/rudder-sdk-js/commit/fe1ba3e8b48d1c0bf8b04c1d1c6aa77d7f78bafd))
* update clevertap loading js to https ([#2169](https://github.com/rudderlabs/rudder-sdk-js/issues/2169)) ([497ad22](https://github.com/rudderlabs/rudder-sdk-js/commit/497ad22d9e690f489b59527723699fd78ca56ce6))

## [3.13.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.13.0...@rudderstack/analytics-js-integrations@3.13.1) (2025-03-03)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.2`
## [3.13.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.12.5...@rudderstack/analytics-js-integrations@3.13.0) (2025-02-21)


### Features

* add support for Google Tag Manager environment config ([#2045](https://github.com/rudderlabs/rudder-sdk-js/issues/2045)) ([148a22a](https://github.com/rudderlabs/rudder-sdk-js/commit/148a22ab5c2b6e97cf9b33c2ed5ab79124c37e59))


### Bug Fixes

* update matomo native sdk loader with correct setTracerUrl ([#2033](https://github.com/rudderlabs/rudder-sdk-js/issues/2033)) ([f0cf52a](https://github.com/rudderlabs/rudder-sdk-js/commit/f0cf52a554f7b059c3a1c3a8df92910e4a6e4d2d))

## [3.12.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.12.4...@rudderstack/analytics-js-integrations@3.12.5) (2025-02-20)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.1`
## [3.12.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.12.3...@rudderstack/analytics-js-integrations@3.12.4) (2025-02-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.0`
## [3.12.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.12.2...@rudderstack/analytics-js-integrations@3.12.3) (2025-01-31)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.16.0`
## [3.12.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.12.1...@rudderstack/analytics-js-integrations@3.12.2) (2025-01-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.15.0`
## [3.12.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.12.0...@rudderstack/analytics-js-integrations@3.12.1) (2025-01-22)


### Bug Fixes

* braze anonymousId tracking with alias details ([#1994](https://github.com/rudderlabs/rudder-sdk-js/issues/1994)) ([7215304](https://github.com/rudderlabs/rudder-sdk-js/commit/721530493d559ba89d2bfe1156a916a8885bb34c))

## [3.12.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.15...@rudderstack/analytics-js-integrations@3.12.0) (2025-01-17)


### Features

* braze sdk metadata ([#1949](https://github.com/rudderlabs/rudder-sdk-js/issues/1949)) ([81fe85a](https://github.com/rudderlabs/rudder-sdk-js/commit/81fe85a0eeb9405c0ec820dfe73a0ef5c0cc014b))


### Bug Fixes

* resolved minor bugsnag issue in googleAds ([#1993](https://github.com/rudderlabs/rudder-sdk-js/issues/1993)) ([0dfb893](https://github.com/rudderlabs/rudder-sdk-js/commit/0dfb893a78894d6d04b8fc0b4a0a3c8595f69e01))

## [3.11.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.14...@rudderstack/analytics-js-integrations@3.11.15) (2025-01-03)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.15`

### Bug Fixes

* microsoft clarity load and handle promise for identify ([#1964](https://github.com/rudderlabs/rudder-sdk-js/issues/1964)) ([11e890e](https://github.com/rudderlabs/rudder-sdk-js/commit/11e890ecff15b6206e197c32f3ab611b338ddfec))

## [3.11.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.13...@rudderstack/analytics-js-integrations@3.11.14) (2024-12-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.14`

### Bug Fixes

* remove circular dependency in packages ([#1973](https://github.com/rudderlabs/rudder-sdk-js/issues/1973)) ([e525496](https://github.com/rudderlabs/rudder-sdk-js/commit/e5254964310c2c73baaf4d0655c3e4025c5e7d2b))

## [3.11.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.12...@rudderstack/analytics-js-integrations@3.11.13) (2024-12-09)


### Bug Fixes

* revert ms-clarity identify promise handling ([aa92760](https://github.com/rudderlabs/rudder-sdk-js/commit/aa92760087c22c61c4b6e703a2759cfc46f5576b))

## [3.11.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.11...@rudderstack/analytics-js-integrations@3.11.12) (2024-12-06)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.13`

### Bug Fixes

* microsoft clarity identify error handling ([#1948](https://github.com/rudderlabs/rudder-sdk-js/issues/1948)) ([33ac767](https://github.com/rudderlabs/rudder-sdk-js/commit/33ac7677c8717ac3ce45d34b7efae720b95b1432))

## [3.11.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.10...@rudderstack/analytics-js-integrations@3.11.11) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.12`
## [3.11.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.9...@rudderstack/analytics-js-integrations@3.11.10) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.11`
## [3.11.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.8...@rudderstack/analytics-js-integrations@3.11.9) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.10`
## [3.11.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.7...@rudderstack/analytics-js-integrations@3.11.8) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.9`
## [3.11.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.6...@rudderstack/analytics-js-integrations@3.11.7) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.8`
## [3.11.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.5...@rudderstack/analytics-js-integrations@3.11.6) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.7`
## [3.11.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.4...@rudderstack/analytics-js-integrations@3.11.5) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.6`
## [3.11.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.3...@rudderstack/analytics-js-integrations@3.11.4) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.5`
## [3.11.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.2...@rudderstack/analytics-js-integrations@3.11.3) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.4`
## [3.11.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.1...@rudderstack/analytics-js-integrations@3.11.2) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.3`
## [3.11.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.11.0...@rudderstack/analytics-js-integrations@3.11.1) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.2`

### Bug Fixes

* age format snapPixel ([#1933](https://github.com/rudderlabs/rudder-sdk-js/issues/1933)) ([c9248cb](https://github.com/rudderlabs/rudder-sdk-js/commit/c9248cb2d33eb9bf1aabe70271e966c81674d3c4))

## [3.11.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.10.6...@rudderstack/analytics-js-integrations@3.11.0) (2024-11-21)


### Features

* snap extra parametrs pass ([#1921](https://github.com/rudderlabs/rudder-sdk-js/issues/1921)) ([0998d08](https://github.com/rudderlabs/rudder-sdk-js/commit/0998d081da3cfaaf0e999335384969ab77565230))


### Bug Fixes

* adroll bugsnag issue ([#1929](https://github.com/rudderlabs/rudder-sdk-js/issues/1929)) ([18589f2](https://github.com/rudderlabs/rudder-sdk-js/commit/18589f2367038cc8c144d07f7dc38abbf10c5a08))
* ga4 allow zero ([#1915](https://github.com/rudderlabs/rudder-sdk-js/issues/1915)) ([9ebbab9](https://github.com/rudderlabs/rudder-sdk-js/commit/9ebbab92cfdaf12067138e065d726da312c507a5))
* googleAds bugsnag issue ([#1930](https://github.com/rudderlabs/rudder-sdk-js/issues/1930)) ([4c8a652](https://github.com/rudderlabs/rudder-sdk-js/commit/4c8a652276882ab3dd982976763bff8c58e5db3d))

## [3.10.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.10.5...@rudderstack/analytics-js-integrations@3.10.6) (2024-11-19)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.1`
## [3.10.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.10.4...@rudderstack/analytics-js-integrations@3.10.5) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.0`
## [3.10.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.10.3...@rudderstack/analytics-js-integrations@3.10.4) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.13.0`
## [3.10.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.10.2...@rudderstack/analytics-js-integrations@3.10.3) (2024-11-12)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.12.1`
## [3.10.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.10.1...@rudderstack/analytics-js-integrations@3.10.2) (2024-11-08)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.12.0`
## [3.10.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.10.0...@rudderstack/analytics-js-integrations@3.10.1) (2024-11-07)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.11.1`
## [3.10.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.9.3...@rudderstack/analytics-js-integrations@3.10.0) (2024-10-25)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.11.0`

### Features

* add session replay for mixpanel ([#1899](https://github.com/rudderlabs/rudder-sdk-js/issues/1899)) ([bfa9d0a](https://github.com/rudderlabs/rudder-sdk-js/commit/bfa9d0a2c8e9e821b32da0defa5c779e090bd106))
* gainsight PX destination ([#1852](https://github.com/rudderlabs/rudder-sdk-js/issues/1852)) ([#1889](https://github.com/rudderlabs/rudder-sdk-js/issues/1889)) ([3a705f0](https://github.com/rudderlabs/rudder-sdk-js/commit/3a705f063bcae99c7964495ff83ad9ce8d4eb5a3))

## [3.9.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.9.2...@rudderstack/analytics-js-integrations@3.9.3) (2024-10-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.10.0`
## [3.9.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.9.1...@rudderstack/analytics-js-integrations@3.9.2) (2024-10-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.5`
## [3.9.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.9.0...@rudderstack/analytics-js-integrations@3.9.1) (2024-10-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.4`
## [3.9.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.8.4...@rudderstack/analytics-js-integrations@3.9.0) (2024-10-14)


### Features

* add support for group in appcues ([#1877](https://github.com/rudderlabs/rudder-sdk-js/issues/1877)) ([d01522a](https://github.com/rudderlabs/rudder-sdk-js/commit/d01522a3b7786c282ade9a4890ac1b3c81090691))
* posthog person profile option ([#1885](https://github.com/rudderlabs/rudder-sdk-js/issues/1885)) ([f9a3f8d](https://github.com/rudderlabs/rudder-sdk-js/commit/f9a3f8d5cc12b40f3d1316e2a4a7f68b210ffe8b))

## [3.8.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.8.3...@rudderstack/analytics-js-integrations@3.8.4) (2024-09-27)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.3`

### Bug Fixes

* userId in braze hyrbrid ([#1835](https://github.com/rudderlabs/rudder-sdk-js/issues/1835)) ([71a6a30](https://github.com/rudderlabs/rudder-sdk-js/commit/71a6a30a3e0a3be7422a66a063ed6b1816e886a7))

## [3.8.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.8.2...@rudderstack/analytics-js-integrations@3.8.3) (2024-09-13)


### Bug Fixes

* update google ads page events ([#1832](https://github.com/rudderlabs/rudder-sdk-js/issues/1832)) ([1ad0fec](https://github.com/rudderlabs/rudder-sdk-js/commit/1ad0fec470259cd81643dffa6ece1f9972549775))

## [3.8.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.8.1...@rudderstack/analytics-js-integrations@3.8.2) (2024-09-12)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.2`
## [3.8.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.8.0...@rudderstack/analytics-js-integrations@3.8.1) (2024-08-28)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.1`
## [3.8.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.7.0...@rudderstack/analytics-js-integrations@3.8.0) (2024-08-16)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.0`

### Features

* onboarded XPixel Integration ([#1783](https://github.com/rudderlabs/rudder-sdk-js/issues/1783)) ([cf9b8cc](https://github.com/rudderlabs/rudder-sdk-js/commit/cf9b8cc41de341be781fae3108e4a07f2b553dda))

## [3.7.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.6.0...@rudderstack/analytics-js-integrations@3.7.0) (2024-08-02)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.8.1`

### Features

* criteo add support for multiple hash methods ([#1812](https://github.com/rudderlabs/rudder-sdk-js/issues/1812)) ([23a0e37](https://github.com/rudderlabs/rudder-sdk-js/commit/23a0e37d274647cb3e2505694522080d7f5b98f3))


### Bug Fixes

* npm sanity suites ([#1810](https://github.com/rudderlabs/rudder-sdk-js/issues/1810)) ([22e43da](https://github.com/rudderlabs/rudder-sdk-js/commit/22e43da01f750a5cb23a2fce50de3744c54a197e))

## [3.6.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.5.0...@rudderstack/analytics-js-integrations@3.6.0) (2024-08-01)


### Features

* **integrations:** feature for server side container url support ga4 ([cfbea6c](https://github.com/rudderlabs/rudder-sdk-js/commit/cfbea6c615e3ef63e06c0098ccdb19c70572c22a))

## [3.5.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.4.5...@rudderstack/analytics-js-integrations@3.5.0) (2024-07-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.4.5`

### Features

* **analytics-js-integrations:** onboard ga4 v2 hybrid mode ([#1802](https://github.com/rudderlabs/rudder-sdk-js/issues/1802)) ([2c8c3be](https://github.com/rudderlabs/rudder-sdk-js/commit/2c8c3bea8ada300c62729eb114dbe8ff84ae9269))

## [3.4.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.4.4...@rudderstack/analytics-js-integrations@3.4.5) (2024-07-19)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.4.4`

### Bug Fixes

* event API overloads ([#1782](https://github.com/rudderlabs/rudder-sdk-js/issues/1782)) ([02c5b47](https://github.com/rudderlabs/rudder-sdk-js/commit/02c5b47d0a83250fb5180e9ed467a92361663dab))

## [3.4.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.4.3...@rudderstack/analytics-js-integrations@3.4.4) (2024-07-17)


### Bug Fixes

* upgrade braze version from 5.0 to 5.3 ([#1784](https://github.com/rudderlabs/rudder-sdk-js/issues/1784)) ([60a3111](https://github.com/rudderlabs/rudder-sdk-js/commit/60a31116f4401f1c0cb3e82cf55f94420474429a))

## [3.4.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.4.2...@rudderstack/analytics-js-integrations@3.4.3) (2024-07-05)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.4.2`

### Bug Fixes

* package lint issues ([#1773](https://github.com/rudderlabs/rudder-sdk-js/issues/1773)) ([8e45d05](https://github.com/rudderlabs/rudder-sdk-js/commit/8e45d052bd6366d647d06226aa89b1fa2e512f9d))

## [3.4.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.4.1...@rudderstack/analytics-js-integrations@3.4.2) (2024-07-04)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.4.1`
## [3.4.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.4.0...@rudderstack/analytics-js-integrations@3.4.1) (2024-06-27)


### Bug Fixes

* remove pollyfill url from command bar destination ([#1768](https://github.com/rudderlabs/rudder-sdk-js/issues/1768)) ([a98e714](https://github.com/rudderlabs/rudder-sdk-js/commit/a98e71485570e09214d2a59384f41c17ec79f37a))

## [3.4.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.3.1...@rudderstack/analytics-js-integrations@3.4.0) (2024-06-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.3.1`

### Features

* add counting method from config for dcm floodlight ([#1731](https://github.com/rudderlabs/rudder-sdk-js/issues/1731)) ([951d48f](https://github.com/rudderlabs/rudder-sdk-js/commit/951d48fd853b368c4c90c88a33db230b59dc7241))


### Bug Fixes

* improve flushing events on page leave ([#1754](https://github.com/rudderlabs/rudder-sdk-js/issues/1754)) ([1be420f](https://github.com/rudderlabs/rudder-sdk-js/commit/1be420fae16b68629789d2ba37e16e6a6e00017c))

## [3.3.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.3.0...@rudderstack/analytics-js-integrations@3.3.1) (2024-06-07)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.3.0`

### Bug Fixes

* add hashing to user traits of reddit_pixel   ([#1728](https://github.com/rudderlabs/rudder-sdk-js/issues/1728)) ([f6f377d](https://github.com/rudderlabs/rudder-sdk-js/commit/f6f377d71566784c0460d7e02e877cd584886ec5))
* url validation ([#1730](https://github.com/rudderlabs/rudder-sdk-js/issues/1730)) ([3a3e105](https://github.com/rudderlabs/rudder-sdk-js/commit/3a3e1057f2db91ef5cbf652a664db9443fee9843))

## [3.3.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.2.0...@rudderstack/analytics-js-integrations@3.3.0) (2024-05-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.2.0`

### Features

* google ads identify call support for enhanced conversion ([#1710](https://github.com/rudderlabs/rudder-sdk-js/issues/1710)) ([efa70f8](https://github.com/rudderlabs/rudder-sdk-js/commit/efa70f8c219e913913cd481da78edbd1dbe20f50))
* gtm messageId support for track and page ([#1709](https://github.com/rudderlabs/rudder-sdk-js/issues/1709)) ([90df013](https://github.com/rudderlabs/rudder-sdk-js/commit/90df013a028bfac6d8a065d99eb37bdeb3b9156c))
* introduce a new config for if hashing is required ([#1725](https://github.com/rudderlabs/rudder-sdk-js/issues/1725)) ([8c76f90](https://github.com/rudderlabs/rudder-sdk-js/commit/8c76f901b3bf865de50f02c6911707e5d1e6101c))
* onboard bingads enhanced conversions ([#1715](https://github.com/rudderlabs/rudder-sdk-js/issues/1715)) ([e7e27be](https://github.com/rudderlabs/rudder-sdk-js/commit/e7e27be27727271afa52e1d4965776291aaf381e))
* set server side cookies ([#1649](https://github.com/rudderlabs/rudder-sdk-js/issues/1649)) ([8b8ac8f](https://github.com/rudderlabs/rudder-sdk-js/commit/8b8ac8fb2b7fe0903fa383cfcd0388fe3022330c))


### Bug Fixes

* add hashing to user traits of reddit_pixel ([#1728](https://github.com/rudderlabs/rudder-sdk-js/issues/1728)) ([f6f377d](https://github.com/rudderlabs/rudder-sdk-js/commit/f6f377d71566784c0460d7e02e877cd584886ec5))
* update reddit_pixel identify call ([#1717](https://github.com/rudderlabs/rudder-sdk-js/issues/1717)) ([acc21f2](https://github.com/rudderlabs/rudder-sdk-js/commit/acc21f26f891bd4985520d32c0cb6d3b54378cb0))
* user sessions behavior ([#1708](https://github.com/rudderlabs/rudder-sdk-js/issues/1708)) ([84e7174](https://github.com/rudderlabs/rudder-sdk-js/commit/84e71744612c8345dc22b8cb0c9362d104eb35e9))

## [3.2.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.1.0...@rudderstack/analytics-js-integrations@3.2.0) (2024-05-10)


### Features

* **criteo:** add support of sha256 hashing method for email ([#1680](https://github.com/rudderlabs/rudder-sdk-js/issues/1680)) ([66e0122](https://github.com/rudderlabs/rudder-sdk-js/commit/66e0122d2bcdb536ab0d69745e05d49806a5348b))

## [3.1.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.4...@rudderstack/analytics-js-integrations@3.1.0) (2024-04-26)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.4`

### Features

* add autoConfig support in FBPixel, add tests ([#1702](https://github.com/rudderlabs/rudder-sdk-js/issues/1702)) ([607c381](https://github.com/rudderlabs/rudder-sdk-js/commit/607c3815128a606efe7cee3c867028a62f1f19c9))
* added custom domain support in ga4 ([#1697](https://github.com/rudderlabs/rudder-sdk-js/issues/1697)) ([3543cc1](https://github.com/rudderlabs/rudder-sdk-js/commit/3543cc1a1f6dc770381c0a6be75646b183f63afa))
* supporting add to cart for criteo ([#1696](https://github.com/rudderlabs/rudder-sdk-js/issues/1696)) ([bb7e1df](https://github.com/rudderlabs/rudder-sdk-js/commit/bb7e1df9a1e5a1ffe4e8a81c3d9fdf18d9ef2744))

## [3.0.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.3...@rudderstack/analytics-js-integrations@3.0.4) (2024-04-12)


### Bug Fixes

* making the error message as warn ([#1686](https://github.com/rudderlabs/rudder-sdk-js/issues/1686)) ([56e34be](https://github.com/rudderlabs/rudder-sdk-js/commit/56e34be3575f66531bfe4910ff4aa970ed2ee208))

## [3.0.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.2...@rudderstack/analytics-js-integrations@3.0.3) (2024-04-02)


### Bug Fixes

* ninetailed: modify page support ([#1677](https://github.com/rudderlabs/rudder-sdk-js/issues/1677)) ([e45ef06](https://github.com/rudderlabs/rudder-sdk-js/commit/e45ef06186cb4dc1d0568f2b39d8e786d278390a))
* updated isLoaded and isReady conditions for mixpanel ([#1650](https://github.com/rudderlabs/rudder-sdk-js/issues/1650)) ([80c76a1](https://github.com/rudderlabs/rudder-sdk-js/commit/80c76a1f7f27a93930a6146730ac94d945b19b25))

## [3.0.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.1...@rudderstack/analytics-js-integrations@3.0.2) (2024-03-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.1`
## [3.0.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.26...@rudderstack/analytics-js-integrations@3.0.1) (2024-03-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.0`
## [3.0.0-beta.26](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.25...@rudderstack/analytics-js-integrations@3.0.0-beta.26) (2024-03-18)


### Bug Fixes

* ninetailed sample app path ([#1634](https://github.com/rudderlabs/rudder-sdk-js/issues/1634)) ([8433366](https://github.com/rudderlabs/rudder-sdk-js/commit/8433366041736407ae7c0015a0afc2a0c47fcbf1))

## [3.0.0-beta.25](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.24...@rudderstack/analytics-js-integrations@3.0.0-beta.25) (2024-03-01)


### Features

* add ecom spec support to google ads via config ([#1611](https://github.com/rudderlabs/rudder-sdk-js/issues/1611)) ([31f8ef6](https://github.com/rudderlabs/rudder-sdk-js/commit/31f8ef6d73280245a54c225d2b577ac40e97e271))
* add support of custom page event name in mixpanel ([#1622](https://github.com/rudderlabs/rudder-sdk-js/issues/1622)) ([ce97106](https://github.com/rudderlabs/rudder-sdk-js/commit/ce971064dd046e012ec3018780bff7894bb0d877))
* klaviyo profile mapping ([#1621](https://github.com/rudderlabs/rudder-sdk-js/issues/1621)) ([d5e7ae6](https://github.com/rudderlabs/rudder-sdk-js/commit/d5e7ae6a823a5724652ebf1f28df25eccd72a13a))
* onboard new destination ninetailed ([#1617](https://github.com/rudderlabs/rudder-sdk-js/issues/1617)) ([080155a](https://github.com/rudderlabs/rudder-sdk-js/commit/080155a74655aeb4b413cc1a90cfa6d66ce3dfbb))
* update content_type mapping logic for fb pixel ([#1628](https://github.com/rudderlabs/rudder-sdk-js/issues/1628)) ([d81adde](https://github.com/rudderlabs/rudder-sdk-js/commit/d81addeba308992fbc933213282653b5845749b9))

## [3.0.0-beta.24](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.23...@rudderstack/analytics-js-integrations@3.0.0-beta.24) (2024-02-16)


### Features

* onboard new destination commandbar ([#1610](https://github.com/rudderlabs/rudder-sdk-js/issues/1610)) ([a034c21](https://github.com/rudderlabs/rudder-sdk-js/commit/a034c21929bd1d7bdc8c6d27d3f92b2d3c421ae3))


### Bug Fixes

* replace lodash.pick with ramda to avoid vulnerabilities ([#1615](https://github.com/rudderlabs/rudder-sdk-js/issues/1615)) ([af9fc16](https://github.com/rudderlabs/rudder-sdk-js/commit/af9fc164612ab9c18656b016a32cc83bf43d9f8f))

## [3.0.0-beta.23](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.22...@rudderstack/analytics-js-integrations@3.0.0-beta.23) (2024-02-08)


### Bug Fixes

* reddit pixel isLoaded ([#1607](https://github.com/rudderlabs/rudder-sdk-js/issues/1607)) ([da40a76](https://github.com/rudderlabs/rudder-sdk-js/commit/da40a76758d7c36b88018afd13dc99ebea359e19))

## [3.0.0-beta.22](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.21...@rudderstack/analytics-js-integrations@3.0.0-beta.22) (2024-02-02)


### Bug Fixes

* appcues to support flattening obj/array for identify ([#1598](https://github.com/rudderlabs/rudder-sdk-js/issues/1598)) ([25b4924](https://github.com/rudderlabs/rudder-sdk-js/commit/25b492436d4de5d6364507b0e728722a340591d4))
* integrations bugsnag alerts ([#1596](https://github.com/rudderlabs/rudder-sdk-js/issues/1596)) ([a5a1c0b](https://github.com/rudderlabs/rudder-sdk-js/commit/a5a1c0bf155e91062fec6b2b77131d066cb961c2))

## [3.0.0-beta.21](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.20...@rudderstack/analytics-js-integrations@3.0.0-beta.21) (2024-01-19)


### Features

* adding proxy server url for amplitude ([#1590](https://github.com/rudderlabs/rudder-sdk-js/issues/1590)) ([988a146](https://github.com/rudderlabs/rudder-sdk-js/commit/988a146037e9929259acf94000951edd546df91e))
* onboard new destination spotify pixel ([#1567](https://github.com/rudderlabs/rudder-sdk-js/issues/1567)) ([92de4e0](https://github.com/rudderlabs/rudder-sdk-js/commit/92de4e01b6da989126061844c3f8f8ebe05caaf8))


### Bug Fixes

* bugsnag alert for google ads ([#1576](https://github.com/rudderlabs/rudder-sdk-js/issues/1576)) ([42e9fd3](https://github.com/rudderlabs/rudder-sdk-js/commit/42e9fd32e33fe7331e83bfbebb050d090315dae2))
* ga4 page call mappings ([#1579](https://github.com/rudderlabs/rudder-sdk-js/issues/1579)) ([6deb94c](https://github.com/rudderlabs/rudder-sdk-js/commit/6deb94ce2e367750ed8406732f979a6f5082e24e))
* **googleads:** added validation to discard event if event name is no event name is present ([#1570](https://github.com/rudderlabs/rudder-sdk-js/issues/1570)) ([3bd4eb6](https://github.com/rudderlabs/rudder-sdk-js/commit/3bd4eb65016c72ddd9f84a0d56363dd7df2ca8d9))

## [3.0.0-beta.20](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.19...@rudderstack/analytics-js-integrations@3.0.0-beta.20) (2024-01-08)


### Features

* amplitude add support for unset ([#1562](https://github.com/rudderlabs/rudder-sdk-js/issues/1562)) ([22b79cd](https://github.com/rudderlabs/rudder-sdk-js/commit/22b79cd51e5c17c8eb26c5bdfd9f06e1decf18bd))


### Bug Fixes

* **analytics-js-integrations:** remove updating user_properties as part of loadScript call for ga4 ([#1527](https://github.com/rudderlabs/rudder-sdk-js/issues/1527)) ([8667d17](https://github.com/rudderlabs/rudder-sdk-js/commit/8667d1789b56852d332bd54af2489b7d7e5909ff))
* **analytics-js-integrations:** resolved tech debt items ([#1523](https://github.com/rudderlabs/rudder-sdk-js/issues/1523)) ([6924f6c](https://github.com/rudderlabs/rudder-sdk-js/commit/6924f6c1e49336e6b8dbc604bfd8cf5d9322944a))
* **clevertap:** region undefined issue ([#1557](https://github.com/rudderlabs/rudder-sdk-js/issues/1557)) ([9f1fc0d](https://github.com/rudderlabs/rudder-sdk-js/commit/9f1fc0d1939e4abc439c8d02dd790745f1efe519))
* infinite recursion error by tracking visited nodes ([#1541](https://github.com/rudderlabs/rudder-sdk-js/issues/1541)) ([d006eb7](https://github.com/rudderlabs/rudder-sdk-js/commit/d006eb708b7bacfdb2571bbb4841a0f1f2e67bc3))
* tiktok add missing field brand ([#1561](https://github.com/rudderlabs/rudder-sdk-js/issues/1561)) ([dd08664](https://github.com/rudderlabs/rudder-sdk-js/commit/dd086640a05da480d579b1580aad110b14c6165d))
* tiktok remove lowercasing for custom events ([#1558](https://github.com/rudderlabs/rudder-sdk-js/issues/1558)) ([09e00f6](https://github.com/rudderlabs/rudder-sdk-js/commit/09e00f668240a9443115a24f86d3f68f91efb95c))


### Reverts

* Revert "fix: tiktok add missing field brand" (#1571) ([5d11ebb](https://github.com/rudderlabs/rudder-sdk-js/commit/5d11ebbd44b50839b727a6ae028ae5b6f95bd2d1)), closes [#1571](https://github.com/rudderlabs/rudder-sdk-js/issues/1571) [#1561](https://github.com/rudderlabs/rudder-sdk-js/issues/1561)

## [3.0.0-beta.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.18...@rudderstack/analytics-js-integrations@3.0.0-beta.19) (2023-12-14)


### Bug Fixes

* **bing_ads:** name and display name import ([#1553](https://github.com/rudderlabs/rudder-sdk-js/issues/1553)) ([371e4b4](https://github.com/rudderlabs/rudder-sdk-js/commit/371e4b419c306d79cec728dd3533c2aaec7f2bed))

## [3.0.0-beta.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.17...@rudderstack/analytics-js-integrations@3.0.0-beta.18) (2023-12-13)


### Features

* tiktok_ads: add support for custom events ([#1542](https://github.com/rudderlabs/rudder-sdk-js/issues/1542)) ([98bcdf7](https://github.com/rudderlabs/rudder-sdk-js/commit/98bcdf7aaaec1ed4512352d8fb8982312c1fcef3))
* updated logger utility across all integrations ([#1434](https://github.com/rudderlabs/rudder-sdk-js/issues/1434)) ([1e23c30](https://github.com/rudderlabs/rudder-sdk-js/commit/1e23c30e219108ac7671f85913dab5ce6f33ed56))


### Bug Fixes

* add check to disallow track events without event name ([#1538](https://github.com/rudderlabs/rudder-sdk-js/issues/1538)) ([c9c128a](https://github.com/rudderlabs/rudder-sdk-js/commit/c9c128a9eb45070d0e5dcd41594b8aea0ffadd90))

## [3.0.0-beta.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.16...@rudderstack/analytics-js-integrations@3.0.0-beta.17) (2023-12-01)


### Features

* adding set_once feature for Mixpanel ([#1497](https://github.com/rudderlabs/rudder-sdk-js/issues/1497)) ([60f9f6f](https://github.com/rudderlabs/rudder-sdk-js/commit/60f9f6f657121131e8eab4cbdcafefa00c11cc9a))
* **analytics-js-loading-scripts:** add loading snippet version in event context ([#1483](https://github.com/rudderlabs/rudder-sdk-js/issues/1483)) ([4873cbc](https://github.com/rudderlabs/rudder-sdk-js/commit/4873cbc183879c0c1825cf939a53b6cf570cdf4e))

## [3.0.0-beta.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.15...@rudderstack/analytics-js-integrations@3.0.0-beta.16) (2023-11-13)


### Features

* add support for EU residency server for Amplitude web device mode ([#1490](https://github.com/rudderlabs/rudder-sdk-js/issues/1490)) ([8e844bd](https://github.com/rudderlabs/rudder-sdk-js/commit/8e844bdf9f7a8ea971c4c86453b4ca2261a7ed7e))
* **analytics-js-integrations:** add in-app message in customerIo ([#1476](https://github.com/rudderlabs/rudder-sdk-js/issues/1476)) ([812542e](https://github.com/rudderlabs/rudder-sdk-js/commit/812542e5e5f0438f9a452cd5b913917fad14c889))
* braze upgrade v5 ([#1481](https://github.com/rudderlabs/rudder-sdk-js/issues/1481)) ([c81544e](https://github.com/rudderlabs/rudder-sdk-js/commit/c81544e75eaf5d2590aab019485b39b42dc677df))
* hybrid mode braze ([#1480](https://github.com/rudderlabs/rudder-sdk-js/issues/1480)) ([8b9ab0c](https://github.com/rudderlabs/rudder-sdk-js/commit/8b9ab0c3dd2411903418c35e76e1bfb1a735545c))
* onboard sprig destination ([#1491](https://github.com/rudderlabs/rudder-sdk-js/issues/1491)) ([2fb7d5c](https://github.com/rudderlabs/rudder-sdk-js/commit/2fb7d5c98cb7a73d692b1d56bd55dd4e6cc08b2b))
* update tests and send delivery_category as part of contents for Purchase event ([#1503](https://github.com/rudderlabs/rudder-sdk-js/issues/1503)) ([028ac3c](https://github.com/rudderlabs/rudder-sdk-js/commit/028ac3cdff2276e2e7f041af37e28f436c9b7460))
* vwo integration update ([#1472](https://github.com/rudderlabs/rudder-sdk-js/issues/1472)) ([068e6f6](https://github.com/rudderlabs/rudder-sdk-js/commit/068e6f63e1f453113cd1fa43a10410023b7d05d0))


### Bug Fixes

* remove unused newCustomer config ([#1482](https://github.com/rudderlabs/rudder-sdk-js/issues/1482)) ([39ded6e](https://github.com/rudderlabs/rudder-sdk-js/commit/39ded6efa3516eec1d3fe6a2aba430336baed47c))

## [3.0.0-beta.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.14...@rudderstack/analytics-js-integrations@3.0.0-beta.15) (2023-10-27)


### Features

* matomo: support premise version ([#1461](https://github.com/rudderlabs/rudder-sdk-js/issues/1461)) ([29ab5f7](https://github.com/rudderlabs/rudder-sdk-js/commit/29ab5f70d30d0ed11b3d5fbe519c624ce21787c9))


### Bug Fixes

* amplitude: configs not reffered ([#1470](https://github.com/rudderlabs/rudder-sdk-js/issues/1470)) ([7e456f0](https://github.com/rudderlabs/rudder-sdk-js/commit/7e456f0a8466d5bb4af950439581adccccb00153))
* **monorepo:** update vulnerable dependencies ([#1457](https://github.com/rudderlabs/rudder-sdk-js/issues/1457)) ([7a4bc4c](https://github.com/rudderlabs/rudder-sdk-js/commit/7a4bc4cc641e4fff2a8f561ce6fd67d16c0cd5a0))
* upgrade vulnerable cryptoJS dependency, rolup to v4 & NX to v17 ([#1471](https://github.com/rudderlabs/rudder-sdk-js/issues/1471)) ([b2bb21c](https://github.com/rudderlabs/rudder-sdk-js/commit/b2bb21cb3f618f6c86f593d1706abe9e6349066d))

## [3.0.0-beta.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.13...@rudderstack/analytics-js-integrations@3.0.0-beta.14) (2023-10-16)


### Features

* add new customer acquisition reporting to google ads ([#1432](https://github.com/rudderlabs/rudder-sdk-js/issues/1432)) ([5bf9eed](https://github.com/rudderlabs/rudder-sdk-js/commit/5bf9eed34e3709255411de09c319da4074ae9535))
* **analytics-js-service-worker:** deprecate service worker export of rudder-sdk-js package  in favor of the new standalone package([#1437](https://github.com/rudderlabs/rudder-sdk-js/issues/1437)) ([1797d3e](https://github.com/rudderlabs/rudder-sdk-js/commit/****************************************))


### Bug Fixes

* gracefully handling state property for pages where optimizely is not loaded ([#1441](https://github.com/rudderlabs/rudder-sdk-js/issues/1441)) ([8725cc6](https://github.com/rudderlabs/rudder-sdk-js/commit/8725cc60c35c80803eaf08df562c413bcdb21882))


### Reverts

* Revert "chore: remove duplicate config named eventWhiteList" (#1452) ([80f397f](https://github.com/rudderlabs/rudder-sdk-js/commit/80f397fe6b5dc1668b7e8e1b04df1ae1f7b5febf)), closes [#1452](https://github.com/rudderlabs/rudder-sdk-js/issues/1452) [#1430](https://github.com/rudderlabs/rudder-sdk-js/issues/1430)

## [3.0.0-beta.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.12...@rudderstack/analytics-js-integrations@3.0.0-beta.13) (2023-10-02)


### Features

* **Amplitude:** update amplitude SDK  ([#1413](https://github.com/rudderlabs/rudder-sdk-js/issues/1413)) ([6636770](https://github.com/rudderlabs/rudder-sdk-js/commit/6636770be8d1406f6d8d5f6621f81d97056a3852)), closes [#1228](https://github.com/rudderlabs/rudder-sdk-js/issues/1228)


### Bug Fixes

* **ga4:** override pii property values to null ([#1420](https://github.com/rudderlabs/rudder-sdk-js/issues/1420)) ([3697f6d](https://github.com/rudderlabs/rudder-sdk-js/commit/3697f6d145ad7670ab2123c7e7ad863f741d8116))
* **ga4:** remove sendUserTraitsAsPartOfInIt field ([#1419](https://github.com/rudderlabs/rudder-sdk-js/issues/1419)) ([033efeb](https://github.com/rudderlabs/rudder-sdk-js/commit/033efebf3d50e75883349612048bed7d8d4f854b))

## [3.0.0-beta.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.11...@rudderstack/analytics-js-integrations@3.0.0-beta.12) (2023-09-29)


### Features

* **analytics-js-integrations:** ga4 enhancements ([#1417](https://github.com/rudderlabs/rudder-sdk-js/issues/1417)) ([1012bd7](https://github.com/rudderlabs/rudder-sdk-js/commit/1012bd72611b6c3c329baa324d4b42192dc447e9))

## [3.0.0-beta.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.10...@rudderstack/analytics-js-integrations@3.0.0-beta.11) (2023-09-26)


### Features

* **analytics-js-integrations:** add support of ignore_dnt to mixpanel ([#1390](https://github.com/rudderlabs/rudder-sdk-js/issues/1390)) ([9050b43](https://github.com/rudderlabs/rudder-sdk-js/commit/9050b43e0623a66727d40f71f8dd87ac026ab2ce))

## [3.0.0-beta.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.9...@rudderstack/analytics-js-integrations@3.0.0-beta.10) (2023-09-20)


### Bug Fixes

* **analytics-js-integrations:** path to moengage name import ([25b9ec7](https://github.com/rudderlabs/rudder-sdk-js/commit/25b9ec73331b57d41b97a6a760419b8cf9e822e7))

## [3.0.0-beta.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.8...@rudderstack/analytics-js-integrations@3.0.0-beta.9) (2023-09-20)


### Bug Fixes

* **analytics-js-integrations:** make wishlistevent not required ([e9aaebc](https://github.com/rudderlabs/rudder-sdk-js/commit/e9aaebc425ece021746d7bb2ce7e71d6524edd12))

## [3.0.0-beta.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.7...@rudderstack/analytics-js-integrations@3.0.0-beta.8) (2023-09-18)


### Features

* deprecate support of common names for integrations ([#1374](https://github.com/rudderlabs/rudder-sdk-js/issues/1374)) ([f1d097d](https://github.com/rudderlabs/rudder-sdk-js/commit/f1d097d9976f6c9d2ad0f1d81d469148f8c7c197))

## [3.0.0-beta.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.6...@rudderstack/analytics-js-integrations@3.0.0-beta.7) (2023-09-14)

# [3.0.0-beta.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.5...@rudderstack/analytics-js-integrations@3.0.0-beta.6) (2023-08-30)


### Bug Fixes

* use utility to determine destination specific integration options ([#1330](https://github.com/rudderlabs/rudder-sdk-js/issues/1330)) ([e4fce0d](https://github.com/rudderlabs/rudder-sdk-js/commit/e4fce0dd4b77c5b15459594bab3c9874a5549010))





# [3.0.0-beta.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.4...@rudderstack/analytics-js-integrations@3.0.0-beta.5) (2023-08-21)

**Note:** Version bump only for package @rudderstack/analytics-js-integrations





# [3.0.0-beta.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.3...@rudderstack/analytics-js-integrations@3.0.0-beta.4) (2023-08-17)


### Bug Fixes

* **analytics-js-integrations:** fix rollup alias config ([e8960dc](https://github.com/rudderlabs/rudder-sdk-js/commit/e8960dc65bc29f70e5d04c3ed89bce3fb4b6f369))





# [3.0.0-beta.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-integrations@3.0.0-beta.2...@rudderstack/analytics-js-integrations@3.0.0-beta.3) (2023-08-10)

**Note:** Version bump only for package @rudderstack/analytics-js-integrations





# 3.0.0-beta.2 (2023-08-09)


### Bug Fixes

* config url deduction ([#1282](https://github.com/rudderlabs/rudder-sdk-js/issues/1282)) ([658dc24](https://github.com/rudderlabs/rudder-sdk-js/commit/658dc24e077035898871888bfd4c72e88f16deb2))
* use destination display name throughout the app ([#1269](https://github.com/rudderlabs/rudder-sdk-js/issues/1269)) ([6e6a18c](https://github.com/rudderlabs/rudder-sdk-js/commit/6e6a18c5248654963130e24d94191350292a5f58))
