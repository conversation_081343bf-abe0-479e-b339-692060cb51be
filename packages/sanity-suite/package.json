{"name": "@rudderstack/analytics-js-sanity-suite", "version": "3.3.0", "private": true, "description": "Sanity suite for testing JS SDK package", "main": "./dist/v3/cdn/testBook.js", "scripts": {"clean": "rimraf -rf ./dist && rimraf -rf ./node_modules/.cache && rimraf -rf ./reports", "start": "npm run build:sdk && npm run build:sdk:integrations && npm run start:local", "start:modern": "npm run build:sdk:modern && npm run build:sdk:integrations:modern && npm run build:sdk:plugins && npm run start:local:modern", "start:v1.1": "npm run build:sdk:v1.1 && npm run build:sdk:integrations && npm run start:local:v1.1", "start:v1.1:modern": "npm run build:sdk:v1.1:modern && npm run build:sdk:integrations:modern && npm run start:local:v1.1:modern", "build": "rollup -c --environment DISTRIBUTION_TYPE:local", "build:sdk:v1.1": "cd ../analytics-v1.1 && npm run build:browser", "build:sdk:v1.1:modern": "cd ../analytics-v1.1 && npm run build:browser:modern", "build:sdk:integrations": "cd ../analytics-js-integrations && npm run build:browser", "build:sdk:integrations:modern": "cd ../analytics-js-integrations && npm run build:browser:modern", "build:sdk": "cd ../analytics-js && npm run build:browser", "build:sdk:modern": "cd ../analytics-js && npm run build:browser:modern", "build:sdk:plugins": "cd ../analytics-js-plugins && npm run build:browser:modern", "build:browser:modern": "exit 0", "build:browser": "exit 0", "build:npm:modern": "exit 0", "build:package": "exit 0", "build:package:modern": "exit 0", "test": "nx test --maxWorkers=50%", "test:ci": "nx test --configuration=ci --runInBand --maxWorkers=1 --forceExit", "check:lint": "nx lint", "check:lint:ci": "nx lint --configuration=ci", "check:circular": "madge --circular --extensions js,ts src || exit 0", "check:support": "NODE_ENV=production npx browserslist --mobile-to-desktop", "check:support:modern": "NODE_ENV=modern npx browserslist --mobile-to-desktop", "check:duplicates": "jscpd src/testBook", "check:security": "npm audit --recursive --audit-level=high", "build:modern": "exit 0", "package": "exit 0", "release": "exit 0", "build:sdk:start": "npm run build:sdk && npm run start:local", "build:sdk:start:modern": "npm run build:sdk:modern && npm run start:local:modern", "build:sdk:start:v1.1": "npm run build:sdk:v1.1 && npm run start:local:v1.1", "build:sdk:start:v1.1:modern": "npm run build:sdk:v1.1:modern && npm run start:local:v1.1:modern", "start:local": "DEV_SERVER=true npm run build:local -- --watch", "start:local:modern": "BUILD_TYPE=modern DEV_SERVER=true npm run build:local -- --watch", "start:local:v1.1": "DEV_SERVER=true npm run build:local:v1.1 -- --watch", "start:local:v1.1:modern": "BUILD_TYPE=modern DEV_SERVER=true npm run build:local:v1.1 -- --watch", "start:cdn": "DEV_SERVER=true npm run build:cdn -- --watch", "start:cdn:v1.1": "DEV_SERVER=true npm run build:cdn:v1.1 -- --watch", "start:npm:v1.1": "DEV_SERVER=true npm run build:npm:v1.1 -- --watch", "start:npm": "DEV_SERVER=true npm run build:npm -- --watch", "start:npm:bundled": "DEV_SERVER=true npm run build:npm:bundled -- --watch", "build:cdn:v1.1": "rollup -c --environment DISTRIBUTION_TYPE:cdn,SDK_VERSION:v1.1", "build:cdn": "rollup -c --environment DISTRIBUTION_TYPE:cdn,SDK_VERSION:v3", "build:local:v1.1": "rollup -c --environment DISTRIBUTION_TYPE:local,SDK_VERSION:v1.1", "build:local": "rollup -c --environment DISTRIBUTION_TYPE:local,SDK_VERSION:v3", "build:npm:v1.1": "rollup -c --environment DISTRIBUTION_TYPE:npm,SDK_VERSION:v1.1,BUILD_TYPE:modern", "build:npm": "rollup -c --environment DISTRIBUTION_TYPE:npm,SDK_VERSION:v3,BUILD_TYPE:modern", "build:npm:bundled": "rollup -c --environment DISTRIBUTION_TYPE:npm_bundled,SDK_VERSION:v3,BUILD_TYPE:modern", "build:all": "npm run build:cdn:v1.1 && npm run build:cdn && npm run build:npm:v1.1 && npm run build:npm && rimraf -rf ./dist/dts"}, "author": "RudderStack", "license": "Elastic-2.0", "dependencies": {"@rudderstack/analytics-js": "*", "rudder-sdk-js": "*", "deep-object-diff": "1.1.9", "object-path": "0.11.8", "ramda": "0.31.3"}, "devDependencies": {}, "overrides": {}, "browserslist": {"production": ["defaults", "Edge >= 80", "Firefox >= 47", "IE >= 11", "Chrome >= 54", "Safari >= 7", "Opera >= 43"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 edge version", "last 1 safari version"]}}