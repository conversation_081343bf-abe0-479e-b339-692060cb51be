# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [3.3.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.14...@rudderstack/analytics-js-sanity-suite@3.3.0) (2025-06-20)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.21.0`

### Features

* **@rudderstack/analytics-js-sanity-suite:** create html file to host all sanity suites ([2ca5280](https://github.com/rudderlabs/rudder-sdk-js/commit/2ca528099a86daa9bf6fd36df31f49db97a8df42))


### Bug Fixes

* **@rudderstack/analytics-js-sanity-suite:** templated urls ([9d73956](https://github.com/rudderlabs/rudder-sdk-js/commit/9d73956055f8a68239cb824341e3ed9f8e601d92))
* address ai bot review comments ([0e73528](https://github.com/rudderlabs/rudder-sdk-js/commit/0e73528e5f9faed01a10a1d5597f248459f68811))

## [3.2.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.13...@rudderstack/analytics-js-sanity-suite@3.2.14) (2025-06-12)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.20.1`
## [3.2.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.12...@rudderstack/analytics-js-sanity-suite@3.2.13) (2025-06-11)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.20.0`
## [3.2.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.11...@rudderstack/analytics-js-sanity-suite@3.2.12) (2025-06-03)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.19.0`
## [3.2.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.10...@rudderstack/analytics-js-sanity-suite@3.2.11) (2025-05-26)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.18.2`
## [3.2.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.9...@rudderstack/analytics-js-sanity-suite@3.2.10) (2025-05-14)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.18.1`
## [3.2.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.8...@rudderstack/analytics-js-sanity-suite@3.2.9) (2025-05-09)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.18.0`
## [3.2.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.7...@rudderstack/analytics-js-sanity-suite@3.2.8) (2025-04-25)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.17.0`
* `rudder-sdk-js` updated to version `2.51.1`
## [3.2.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.6...@rudderstack/analytics-js-sanity-suite@3.2.7) (2025-03-03)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.16.1`
## [3.2.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.5...@rudderstack/analytics-js-sanity-suite@3.2.6) (2025-03-03)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.16.0`
* `rudder-sdk-js` updated to version `2.51.0`
## [3.2.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.4...@rudderstack/analytics-js-sanity-suite@3.2.5) (2025-02-24)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.15.2`
## [3.2.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.3...@rudderstack/analytics-js-sanity-suite@3.2.4) (2025-02-20)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.15.1`
* `rudder-sdk-js` updated to version `2.50.1`

### Bug Fixes

* sdk instrumentation in example apps loading snippet ([#2051](https://github.com/rudderlabs/rudder-sdk-js/issues/2051)) ([55f69b2](https://github.com/rudderlabs/rudder-sdk-js/commit/55f69b221d25633b2abc6d50d72919176498e1ad))
* sdk loading snippet to reduce ambiguity in updating url ([#2048](https://github.com/rudderlabs/rudder-sdk-js/issues/2048)) ([843d944](https://github.com/rudderlabs/rudder-sdk-js/commit/843d944f2d63ee414cbcf9d7c991ba97567cdac3))

## [3.2.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.2...@rudderstack/analytics-js-sanity-suite@3.2.3) (2025-02-17)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.15.0`
* `rudder-sdk-js` updated to version `2.50.0`
## [3.2.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.1...@rudderstack/analytics-js-sanity-suite@3.2.2) (2025-01-31)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.14.0`
* `rudder-sdk-js` updated to version `2.49.0`
## [3.2.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.2.0...@rudderstack/analytics-js-sanity-suite@3.2.1) (2025-01-28)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.13.0`
## [3.2.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.51...@rudderstack/analytics-js-sanity-suite@3.2.0) (2025-01-24)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.12.0`

### Features

* lock plugins and integrations version by default ([#1956](https://github.com/rudderlabs/rudder-sdk-js/issues/1956)) ([45e716e](https://github.com/rudderlabs/rudder-sdk-js/commit/45e716e6df3d6e665c25aa907531adb746961d50))

## [3.1.51](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.50...@rudderstack/analytics-js-sanity-suite@3.1.51) (2025-01-03)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.17`
## [3.1.50](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.49...@rudderstack/analytics-js-sanity-suite@3.1.50) (2024-12-17)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.16`
## [3.1.49](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.48...@rudderstack/analytics-js-sanity-suite@3.1.49) (2024-12-06)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.15`
## [3.1.48](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.47...@rudderstack/analytics-js-sanity-suite@3.1.48) (2024-11-30)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.14`

### Bug Fixes

* preload events not processed with detached load call ([#1953](https://github.com/rudderlabs/rudder-sdk-js/issues/1953)) ([6b0f66f](https://github.com/rudderlabs/rudder-sdk-js/commit/6b0f66f61745542f2b01c02c99b7514fd468db80))

## [3.1.47](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.46...@rudderstack/analytics-js-sanity-suite@3.1.47) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.13`
## [3.1.46](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.45...@rudderstack/analytics-js-sanity-suite@3.1.46) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.12`
## [3.1.45](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.44...@rudderstack/analytics-js-sanity-suite@3.1.45) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.11`
## [3.1.44](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.43...@rudderstack/analytics-js-sanity-suite@3.1.44) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.10`
## [3.1.43](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.42...@rudderstack/analytics-js-sanity-suite@3.1.43) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.9`
## [3.1.42](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.41...@rudderstack/analytics-js-sanity-suite@3.1.42) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.8`
## [3.1.41](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.40...@rudderstack/analytics-js-sanity-suite@3.1.41) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.7`
## [3.1.40](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.39...@rudderstack/analytics-js-sanity-suite@3.1.40) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.6`
## [3.1.39](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.38...@rudderstack/analytics-js-sanity-suite@3.1.39) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.5`
## [3.1.38](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.37...@rudderstack/analytics-js-sanity-suite@3.1.38) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.4`
## [3.1.37](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.36...@rudderstack/analytics-js-sanity-suite@3.1.37) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.3`
## [3.1.36](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.35...@rudderstack/analytics-js-sanity-suite@3.1.36) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.2`
## [3.1.35](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.34...@rudderstack/analytics-js-sanity-suite@3.1.35) (2024-11-19)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.1`
## [3.1.34](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.33...@rudderstack/analytics-js-sanity-suite@3.1.34) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.0`
## [3.1.33](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.32...@rudderstack/analytics-js-sanity-suite@3.1.33) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.10.2`
## [3.1.32](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.31...@rudderstack/analytics-js-sanity-suite@3.1.32) (2024-11-12)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.10.1`
## [3.1.31](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.30...@rudderstack/analytics-js-sanity-suite@3.1.31) (2024-11-08)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.10.0`
## [3.1.30](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.29...@rudderstack/analytics-js-sanity-suite@3.1.30) (2024-11-07)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.9.1`
## [3.1.29](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.28...@rudderstack/analytics-js-sanity-suite@3.1.29) (2024-10-25)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.9.0`
## [3.1.28](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.27...@rudderstack/analytics-js-sanity-suite@3.1.28) (2024-10-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.8.0`
## [3.1.27](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.26...@rudderstack/analytics-js-sanity-suite@3.1.27) (2024-10-18)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.19`
## [3.1.26](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.25...@rudderstack/analytics-js-sanity-suite@3.1.26) (2024-10-17)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.18`
## [3.1.25](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.24...@rudderstack/analytics-js-sanity-suite@3.1.25) (2024-10-11)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.17`
## [3.1.24](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.23...@rudderstack/analytics-js-sanity-suite@3.1.24) (2024-10-10)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.16`

### Bug Fixes

* source config fixture and local storage issue ([cbf0833](https://github.com/rudderlabs/rudder-sdk-js/commit/cbf08337f2a4dfdb0566ef60ef0dcf4c53ce8e00))

## [3.1.23](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.22...@rudderstack/analytics-js-sanity-suite@3.1.23) (2024-10-03)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.15`
## [3.1.22](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.21...@rudderstack/analytics-js-sanity-suite@3.1.22) (2024-09-27)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.14`
* `rudder-sdk-js` updated to version `2.48.19`

### Bug Fixes

* upgrade all packages to latest to fix vulnerabilities ([#1867](https://github.com/rudderlabs/rudder-sdk-js/issues/1867)) ([389348c](https://github.com/rudderlabs/rudder-sdk-js/commit/389348cfa61f2111c5ac4f9e2bad5851a466484d))

## [3.1.21](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.20...@rudderstack/analytics-js-sanity-suite@3.1.21) (2024-09-20)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.13`
## [3.1.20](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.19...@rudderstack/analytics-js-sanity-suite@3.1.20) (2024-09-17)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.13`

### Bug Fixes

* sdk loading snippet and sanity suite ([#1853](https://github.com/rudderlabs/rudder-sdk-js/issues/1853)) ([d531f14](https://github.com/rudderlabs/rudder-sdk-js/commit/d531f142f9f9f17f3f675962835531d02b687844))

## [3.1.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.18...@rudderstack/analytics-js-sanity-suite@3.1.19) (2024-09-12)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.12`
* `rudder-sdk-js` updated to version `2.48.18`
## [3.1.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.17...@rudderstack/analytics-js-sanity-suite@3.1.18) (2024-08-30)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.11`
## [3.1.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.16...@rudderstack/analytics-js-sanity-suite@3.1.17) (2024-08-28)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.10`
## [3.1.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.15...@rudderstack/analytics-js-sanity-suite@3.1.16) (2024-08-16)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.9`
## [3.1.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.14...@rudderstack/analytics-js-sanity-suite@3.1.15) (2024-08-16)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.8`
## [3.1.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.13...@rudderstack/analytics-js-sanity-suite@3.1.14) (2024-08-02)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.7`
* `rudder-sdk-js` updated to version `2.48.15`

### Bug Fixes

* npm sanity suites ([#1810](https://github.com/rudderlabs/rudder-sdk-js/issues/1810)) ([22e43da](https://github.com/rudderlabs/rudder-sdk-js/commit/22e43da01f750a5cb23a2fce50de3744c54a197e))

## [3.1.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.12...@rudderstack/analytics-js-sanity-suite@3.1.13) (2024-07-24)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.12`
## [3.1.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.11...@rudderstack/analytics-js-sanity-suite@3.1.12) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.11`
## [3.1.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.10...@rudderstack/analytics-js-sanity-suite@3.1.11) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.10`
## [3.1.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.9...@rudderstack/analytics-js-sanity-suite@3.1.10) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.9`
## [3.1.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.8...@rudderstack/analytics-js-sanity-suite@3.1.9) (2024-07-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.8`
## [3.1.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.7...@rudderstack/analytics-js-sanity-suite@3.1.8) (2024-07-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.7`
## [3.1.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.6...@rudderstack/analytics-js-sanity-suite@3.1.7) (2024-07-19)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.6`
* `rudder-sdk-js` updated to version `3.1.6`

### Bug Fixes

* event API overloads ([#1782](https://github.com/rudderlabs/rudder-sdk-js/issues/1782)) ([02c5b47](https://github.com/rudderlabs/rudder-sdk-js/commit/02c5b47d0a83250fb5180e9ed467a92361663dab))

## [3.1.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.5...@rudderstack/analytics-js-sanity-suite@3.1.6) (2024-07-05)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.5`
* `rudder-sdk-js` updated to version `3.1.5`

### Bug Fixes

* package lint issues ([#1773](https://github.com/rudderlabs/rudder-sdk-js/issues/1773)) ([8e45d05](https://github.com/rudderlabs/rudder-sdk-js/commit/8e45d052bd6366d647d06226aa89b1fa2e512f9d))

## [3.1.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.4...@rudderstack/analytics-js-sanity-suite@3.1.5) (2024-07-04)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.4`
## [3.1.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.3...@rudderstack/analytics-js-sanity-suite@3.1.4) (2024-07-01)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.3`
## [3.1.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.2...@rudderstack/analytics-js-sanity-suite@3.1.3) (2024-06-25)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.2`
## [3.1.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.1...@rudderstack/analytics-js-sanity-suite@3.1.2) (2024-06-25)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.1`

### Bug Fixes

* **analytics-js-sanity-suite:** avoid using iterators and force trigger mutation observer ([bbc66e4](https://github.com/rudderlabs/rudder-sdk-js/commit/bbc66e4d4f75b0c52bb8391cd6775b3d84d573ee))

## [3.1.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.1.0...@rudderstack/analytics-js-sanity-suite@3.1.1) (2024-06-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.0`

### Bug Fixes

* improve flushing events on page leave ([#1754](https://github.com/rudderlabs/rudder-sdk-js/issues/1754)) ([1be420f](https://github.com/rudderlabs/rudder-sdk-js/commit/1be420fae16b68629789d2ba37e16e6a6e00017c))
* remove data residency feature ([#1748](https://github.com/rudderlabs/rudder-sdk-js/issues/1748)) ([870a7ec](https://github.com/rudderlabs/rudder-sdk-js/commit/870a7ecf3cd251d88c207d9815c2f16c6e9a6883))

## [3.1.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.10...@rudderstack/analytics-js-sanity-suite@3.1.0) (2024-06-07)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.10`
* `rudder-sdk-js` updated to version `3.0.10`

### Features

* add install type to context ([#1740](https://github.com/rudderlabs/rudder-sdk-js/issues/1740)) ([3d25b65](https://github.com/rudderlabs/rudder-sdk-js/commit/3d25b654a70b0f39c412e80465e29e2bdb578aa7))


### Bug Fixes

* improve sdk loading snippet ([#1745](https://github.com/rudderlabs/rudder-sdk-js/issues/1745)) ([d4e0f66](https://github.com/rudderlabs/rudder-sdk-js/commit/d4e0f663a4d0cdb55563ed380166d55e99cf3fc8))
* update source config responses ([#1726](https://github.com/rudderlabs/rudder-sdk-js/issues/1726)) ([3a92202](https://github.com/rudderlabs/rudder-sdk-js/commit/3a92202564a1b514a7a0aa9c5c981c1dbe030284))
* url validation ([#1730](https://github.com/rudderlabs/rudder-sdk-js/issues/1730)) ([3a3e105](https://github.com/rudderlabs/rudder-sdk-js/commit/3a3e1057f2db91ef5cbf652a664db9443fee9843))

## [3.0.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.9...@rudderstack/analytics-js-sanity-suite@3.0.10) (2024-06-04)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.9`
## [3.0.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.8...@rudderstack/analytics-js-sanity-suite@3.0.9) (2024-05-29)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.8`
## [3.0.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.7...@rudderstack/analytics-js-sanity-suite@3.0.8) (2024-05-24)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.7`

### Bug Fixes

* user sessions behavior ([#1708](https://github.com/rudderlabs/rudder-sdk-js/issues/1708)) ([84e7174](https://github.com/rudderlabs/rudder-sdk-js/commit/84e71744612c8345dc22b8cb0c9362d104eb35e9))

## [3.0.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.6...@rudderstack/analytics-js-sanity-suite@3.0.7) (2024-05-10)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.6`
## [3.0.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.5...@rudderstack/analytics-js-sanity-suite@3.0.6) (2024-04-26)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.5`
## [3.0.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.4...@rudderstack/analytics-js-sanity-suite@3.0.5) (2024-04-12)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.4`

### Bug Fixes

* remove unnecessary window globals declarations ([#1687](https://github.com/rudderlabs/rudder-sdk-js/issues/1687)) ([09e5ab8](https://github.com/rudderlabs/rudder-sdk-js/commit/09e5ab89965a0b0dc5070891288a08358c103c0e))

## [3.0.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.3...@rudderstack/analytics-js-sanity-suite@3.0.4) (2024-03-26)

### Dependency Updates

* `rudder-sdk-js` updated to version `3.0.3`
## [3.0.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.2...@rudderstack/analytics-js-sanity-suite@3.0.3) (2024-03-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.2`
## [3.0.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.1...@rudderstack/analytics-js-sanity-suite@3.0.2) (2024-03-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.1`
* `rudder-sdk-js` updated to version `3.0.1`

### Bug Fixes

* replace polyfillio with fastly ([#1664](https://github.com/rudderlabs/rudder-sdk-js/issues/1664)) ([24d3a0b](https://github.com/rudderlabs/rudder-sdk-js/commit/24d3a0b383f58b79fc1970d0c74761de30bb3f4a))
* type issues ([#1663](https://github.com/rudderlabs/rudder-sdk-js/issues/1663)) ([1f114a1](https://github.com/rudderlabs/rudder-sdk-js/commit/1f114a19ac14ffd9af6ae876a54d4d19afd80d65))

## [3.0.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.25...@rudderstack/analytics-js-sanity-suite@3.0.1) (2024-03-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.0.0`
* `rudder-sdk-js` updated to version `3.0.0`
## [3.0.0-beta.25](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.24...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.25) (2024-03-18)

## [3.0.0-beta.24](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.23...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.24) (2024-03-01)


### Bug Fixes

* **analytics-js-loading-scripts:** add version in polyfill io url ([#1630](https://github.com/rudderlabs/rudder-sdk-js/issues/1630)) ([3e315a6](https://github.com/rudderlabs/rudder-sdk-js/commit/3e315a6555871ef3cadb93236191a38bc21a2973))

## [3.0.0-beta.23](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.22...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.23) (2024-02-16)

## [3.0.0-beta.22](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.21...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.22) (2024-02-02)


### Features

* **analytics-js:** auto expose new instance to window.rudderanalytics for npm usage ([#1599](https://github.com/rudderlabs/rudder-sdk-js/issues/1599)) ([27000ec](https://github.com/rudderlabs/rudder-sdk-js/commit/27000ec81f2d221ddaf206ceb0ed87ae2a8fc4e5))


### Bug Fixes

* **analytics-js-sanity-suite:** sort sourceConfig response destinations ([dab1928](https://github.com/rudderlabs/rudder-sdk-js/commit/dab1928ea4bb91d86999c65965e0b4a87f87a0b4))
* **sanity-suite:** add new destination config  properties in ignore list ([b935fd7](https://github.com/rudderlabs/rudder-sdk-js/commit/b935fd70565b492e53807a384f39882a3261bcf1))

## [3.0.0-beta.21](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.20...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.21) (2024-01-19)


### Features

* **analytics-v1.1:** add lock integrations version as query param in source config url ([#1584](https://github.com/rudderlabs/rudder-sdk-js/issues/1584)) ([4163514](https://github.com/rudderlabs/rudder-sdk-js/commit/4163514b968fb33945e8b75c1c4f9a10dd4677fc))


### Bug Fixes

* **analytics-js-sanity-suite:** update sourceConfig fixtures ([#1577](https://github.com/rudderlabs/rudder-sdk-js/issues/1577)) ([955afa6](https://github.com/rudderlabs/rudder-sdk-js/commit/955afa609d1586ed0154273e334220ccdd819a1f))

## [3.0.0-beta.20](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.19...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.20) (2024-01-08)


### Features

* remove support for category names in onetrust plugin ([#1556](https://github.com/rudderlabs/rudder-sdk-js/issues/1556)) ([2977c19](https://github.com/rudderlabs/rudder-sdk-js/commit/2977c194ec6ef877547687f3f48a161c69dace3c))

## [3.0.0-beta.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.18...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.19) (2023-12-13)


### Features

* **analytics-js:** add additional consent management fields ([#1534](https://github.com/rudderlabs/rudder-sdk-js/issues/1534)) ([1e5d26f](https://github.com/rudderlabs/rudder-sdk-js/commit/1e5d26f89cc1151b40f6202d69086514607dc10c))

## [3.0.0-beta.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.17...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.18) (2023-12-06)

## [3.0.0-beta.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.16...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.17) (2023-12-01)


### Features

* **analytics-js-loading-scripts:** add loading snippet version in event context ([#1483](https://github.com/rudderlabs/rudder-sdk-js/issues/1483)) ([4873cbc](https://github.com/rudderlabs/rudder-sdk-js/commit/4873cbc183879c0c1825cf939a53b6cf570cdf4e))
* **analytics-js-sanity-suite:** add IS_DEV_TESTBOOK to allow dev env tests only ([dfbe496](https://github.com/rudderlabs/rudder-sdk-js/commit/dfbe4967ef6b5b0b650054743b0e7e03bb667ed4))
* **analytics-js-sanity-suite:** maintenance cleanup, new manual load page with ui ([#1519](https://github.com/rudderlabs/rudder-sdk-js/issues/1519)) ([0bb5ac7](https://github.com/rudderlabs/rudder-sdk-js/commit/0bb5ac7e845efc412502b67d2517ed928a3b4115))

## [3.0.0-beta.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.15...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.16) (2023-11-13)


### Features

* buffer consent method invocations ([#1501](https://github.com/rudderlabs/rudder-sdk-js/issues/1501)) ([70f6f64](https://github.com/rudderlabs/rudder-sdk-js/commit/70f6f64500b6b08bbb657498ec0e1fdecf72b82c))
* reinitialize persistent data from consent options ([#1465](https://github.com/rudderlabs/rudder-sdk-js/issues/1465)) ([43f30b7](https://github.com/rudderlabs/rudder-sdk-js/commit/43f30b7296ae9a0862810fd0b3c520e8bddf614c))


### Bug Fixes

* gcm qa fixes ([#1499](https://github.com/rudderlabs/rudder-sdk-js/issues/1499)) ([84ca784](https://github.com/rudderlabs/rudder-sdk-js/commit/84ca784e6848e7fd8988c4a93db46d4636f2f89c))

## [3.0.0-beta.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.14...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.15) (2023-10-27)


### Features

* consent api ([#1458](https://github.com/rudderlabs/rudder-sdk-js/issues/1458)) ([216b405](https://github.com/rudderlabs/rudder-sdk-js/commit/216b405f7c319d5ff2d799d2e3a5efe2ee4a03af))
* remove preload buffer for segment AnonymousId TC ([#1450](https://github.com/rudderlabs/rudder-sdk-js/issues/1450)) ([4ed8aff](https://github.com/rudderlabs/rudder-sdk-js/commit/4ed8afff1a155c9a37223202ccfcd213ef9c0d9a))


### Bug Fixes

* **analytics-js:** correct declared global extended type ([#1460](https://github.com/rudderlabs/rudder-sdk-js/issues/1460)) ([3f15290](https://github.com/rudderlabs/rudder-sdk-js/commit/3f1529037ba0541391b5f8033e37f867fdd7931c))
* **analytics-js:** fix remote import error when npm package is bundled with webpack ([#1466](https://github.com/rudderlabs/rudder-sdk-js/issues/1466)) ([3a818ac](https://github.com/rudderlabs/rudder-sdk-js/commit/3a818accd24e6b3667c75a6b60fb12aba36bdf7e))
* **monorepo:** update vulnerable dependencies ([#1457](https://github.com/rudderlabs/rudder-sdk-js/issues/1457)) ([7a4bc4c](https://github.com/rudderlabs/rudder-sdk-js/commit/7a4bc4cc641e4fff2a8f561ce6fd67d16c0cd5a0))
* upgrade vulnerable cryptoJS dependency, rolup to v4 & NX to v17 ([#1471](https://github.com/rudderlabs/rudder-sdk-js/issues/1471)) ([b2bb21c](https://github.com/rudderlabs/rudder-sdk-js/commit/b2bb21cb3f618f6c86f593d1706abe9e6349066d))

## [3.0.0-beta.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.13...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.14) (2023-10-16)


### Features

* **analytics-js-service-worker:** deprecate service worker export of rudder-sdk-js package  in favor of the new standalone package([#1437](https://github.com/rudderlabs/rudder-sdk-js/issues/1437)) ([1797d3e](https://github.com/rudderlabs/rudder-sdk-js/commit/****************************************))


### Bug Fixes

* **analytics-js-loading-scripts:** add globalThis polyfill for safari ([#1446](https://github.com/rudderlabs/rudder-sdk-js/issues/1446)) ([bf111f8](https://github.com/rudderlabs/rudder-sdk-js/commit/bf111f8fc24fe75d183ea4924423e3c560ce457d))
* **analytics-js-sanity-suite:** ignore more properties in API checks ([09eba42](https://github.com/rudderlabs/rudder-sdk-js/commit/09eba42f73e09a0f6a30526bb2682f863aecaad9))
* **analytics-js-sanity-suite:** remove the sticky header ([093bd5f](https://github.com/rudderlabs/rudder-sdk-js/commit/093bd5f56614a9de43245fa1dd6162c94fc09c34))
* **analytics-js:** add global definitions extended window type ([#1445](https://github.com/rudderlabs/rudder-sdk-js/issues/1445)) ([b995635](https://github.com/rudderlabs/rudder-sdk-js/commit/b995635a7a3979173d35b34fa32b41b4429b166f))

## [3.0.0-beta.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.12...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.13) (2023-10-02)

## [3.0.0-beta.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.11...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.12) (2023-09-26)

## [3.0.0-beta.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.10...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.11) (2023-09-26)

## [3.0.0-beta.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.9...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.10) (2023-09-20)

## [3.0.0-beta.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.8...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.9) (2023-09-19)

## [3.0.0-beta.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.7...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.8) (2023-09-18)

## [3.0.0-beta.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.6...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.7) (2023-09-14)

# [3.0.0-beta.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.5...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.6) (2023-08-30)


### Features

* add batching support to xhr plugin ([#1301](https://github.com/rudderlabs/rudder-sdk-js/issues/1301)) ([0421663](https://github.com/rudderlabs/rudder-sdk-js/commit/04216637a00dc5339cf466a586137415b46b6b49))
* add callback for polyfill load ([#1335](https://github.com/rudderlabs/rudder-sdk-js/issues/1335)) ([6ba9329](https://github.com/rudderlabs/rudder-sdk-js/commit/6ba932918dd03c110c92cd5837a2f8ca0f9cf192))





# [3.0.0-beta.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.4...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.5) (2023-08-21)


### Bug Fixes

* **analytics-js:** update context page details in every event creation ([#1317](https://github.com/rudderlabs/rudder-sdk-js/issues/1317)) ([45c2300](https://github.com/rudderlabs/rudder-sdk-js/commit/45c230094aceb8176d92e7958fcb6910ebc61248))





# [3.0.0-beta.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.3...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.4) (2023-08-17)

**Note:** Version bump only for package @rudderstack/analytics-js-sanity-suite





# [3.0.0-beta.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-sanity-suite@3.0.0-beta.2...@rudderstack/analytics-js-sanity-suite@3.0.0-beta.3) (2023-08-10)

**Note:** Version bump only for package @rudderstack/analytics-js-sanity-suite





# 3.0.0-beta.2 (2023-08-09)
