{"source": {"id": "2L8FlAECp1sEUP7NVilWLTeA2wp", "name": "JS SDK Sanity Suite Prod", "writeKey": "2L8Fl7ryPss3Zku133Pj5ox7NeP", "config": {"statsCollection": {"errors": {"enabled": true}, "metrics": {"enabled": false}}}, "dataplanes": {}, "enabled": true, "workspaceId": "2L8FgwnU8Q4I4nQbdqmin8uP5n8", "destinations": [{"id": "2M2I7gZ9L474wHE0mzMdIo84gIK", "name": "GTM Web SDK Team Sanity Suite", "enabled": true, "config": {"containerID": "GTM-W2NFJCG", "serverUrl": "", "blacklistedEvents": [], "whitelistedEvents": [], "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "consentManagement": [{"provider": "oneTrust", "consents": []}], "eventFilteringOption": "disable"}, "destinationDefinitionId": "1X6kXapk8Sf8yZTJn1dYZO3OXFt", "destinationDefinition": {"name": "GTM", "displayName": "Google Tag Manager"}, "updatedAt": "2024-10-01T10:20:52.025Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2M5amK3AVQ31yy9uxD8Usk0y34Q", "name": "Google Ads for SDK Team Sanity Suite", "enabled": true, "config": {"conversionID": "AW-11099263080", "eventMappingFromConfig": [{"from": "", "to": ""}], "defaultPageConversion": "", "sendPageView": true, "conversionLinker": true, "disableAdPersonalization": false, "blacklistedEvents": [], "whitelistedEvents": [], "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "eventFilteringOption": "disable", "dynamicRemarketing": {}, "trackConversions": true, "enableConversionEventsFiltering": false, "trackDynamicRemarketing": false, "allowEnhancedConversions": false, "enableConversionLabel": false, "consentManagement": [{"provider": "oneTrust", "consents": []}]}, "destinationDefinitionId": "1X6kXNpFwP2NmnjWGuVWb8qoJ8L", "destinationDefinition": {"name": "GOOGLEADS", "displayName": "Google Ads"}, "updatedAt": "2024-10-01T09:56:28.946Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2MBKOwDUcq0pKzHDNWL4NZH1nvW", "name": "<PERSON><PERSON> Prod for SDK Team Sanity Suite", "enabled": true, "config": {"siteID": "3382269", "blacklistedEvents": [], "whitelistedEvents": [], "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "eventFilteringOption": "disable", "consentManagement": [{"provider": "oneTrust", "consents": []}]}, "destinationDefinitionId": "1SxbQXdfQ2NzIdqNO3GceshF4V0", "destinationDefinition": {"name": "HOTJAR", "displayName": "<PERSON><PERSON>"}, "updatedAt": "2024-10-01T10:20:16.906Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2L8KyN0Fo6CmtL1jZ7Rccas42ry", "name": "Amplitude for SDK Team Sanity Suite", "enabled": true, "config": {"apiKey": "1fb2ce7eab72a20dad598f3236ee130c", "groupTypeTrait": "", "groupValueTrait": "", "trackAllPages": false, "trackCategorizedPages": true, "trackNamedPages": true, "traitsToIncrement": [{"traits": ""}], "traitsToSetOnce": [{"traits": ""}], "traitsToAppend": [{"traits": ""}], "traitsToPrepend": [{"traits": ""}], "trackProductsOnce": false, "trackRevenuePerProduct": false, "preferAnonymousIdForDeviceId": false, "versionName": "", "blacklistedEvents": [{"eventName": ""}], "whitelistedEvents": [{"eventName": "AllowListed_DenyListed_Event_Name"}, {"eventName": "AllowListed_Event_Name"}], "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "eventFilteringOption": "whitelistedEvents", "mapDeviceBrand": false, "residencyServer": "standard", "userProvidedPageEventString": "", "useUserDefinedPageEventName": false, "attribution": false, "eventUploadThreshold": "30", "eventUploadPeriodMillis": "1000", "trackNewCampaigns": true, "userProvidedScreenEventString": "", "useUserDefinedScreenEventName": false}, "destinationDefinitionId": "1QGzO4fWSyq3lsyFHf4eQAMDSr9", "destinationDefinition": {"name": "AM", "displayName": "Amplitude"}, "updatedAt": "2024-09-18T11:17:44.316Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2LoXypOO5xdh7R5xJynzIyWeYJf", "name": "Braze for SDK Team Sanity Suite", "enabled": true, "config": {"appKey": "6121fa4a-9fe9-4341-a4f5-641e3ba888e6", "dataCenter": "US-03", "trackAnonymousUser": false, "enableSubscriptionGroupInGroupCall": false, "enableBrazeLogging": false, "enableNestedArrayOperations": false, "supportDedup": false, "blacklistedEvents": [{"eventName": ""}], "whitelistedEvents": [{"eventName": ""}], "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "eventFilteringOption": "disable", "connectionMode": "device"}, "destinationDefinitionId": "1XQoHKJnI6Uf67wN20RlvAQSUB9", "destinationDefinition": {"name": "BRAZE", "displayName": "Braze"}, "updatedAt": "2024-09-19T10:57:33.488Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2LoUwsaZMiRBsdhwc3zXcNk4oOw", "name": "GA SDK Team Sanity Suite", "enabled": true, "config": {"trackingID": "UA-257384451-1", "doubleClick": false, "enhancedLinkAttribution": false, "includeSearch": false, "trackCategorizedPages": true, "trackNamedPages": true, "useRichEventNames": false, "sampleRate": "100", "siteSpeedSampleRate": "1", "dimensions": [{"from": "", "to": ""}], "resetCustomDimensionsOnPage": [], "setAllMappedProps": true, "anonymizeIp": false, "domain": "auto", "enhancedEcommerce": false, "nonInteraction": false, "optimize": "", "sendUserId": false, "useGoogleAmpClientId": false, "namedTracker": false, "blacklistedEvents": [{"eventName": "DenyListed_Event_Name"}, {"eventName": "AllowListed_DenyListed_Event_Name"}], "whitelistedEvents": [{"eventName": ""}], "oneTrustCookieCategories": [{"oneTrustCookieCategory": "C0001"}, {"oneTrustCookieCategory": "C0003"}], "ketchConsentPurposes": [{"purpose": "analytics"}, {"purpose": "another_purpose_id"}], "consentManagement": [{"provider": "oneTrust", "consents": [{"consent": "C0001"}, {"consent": "C0003"}]}, {"provider": "ketch", "consents": [{"consent": "analytics"}, {"consent": "another_purpose_id"}]}], "eventFilteringOption": "blacklistedEvents"}, "destinationDefinitionId": "1QGzNyNSA4alXixnhDcq4C10ac2", "destinationDefinition": {"name": "GA", "displayName": "Google Analytics"}, "updatedAt": "2024-10-01T09:57:51.189Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2LoR1TbVG2bcISXvy7DamldfkgO", "name": "GA4 for SDK Team Sanity Suite", "enabled": true, "config": {"debugView": true, "measurementId": "G-SC6JGSYH6H", "connectionMode": "hybrid", "capturePageView": "rs", "whitelistedEvents": [{"eventName": ""}], "blacklistedEvents": [{"eventName": ""}], "useNativeSDKToSend": false, "eventFilteringOption": "disable", "extendPageViewParams": true, "piiPropertiesToIgnore": [{"piiProperty": ""}], "overrideClientAndSessionId": false, "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "ketchConsentPurposes": [{"purpose": "analytics"}], "consentManagement": [{"provider": "oneTrust", "consents": []}, {"provider": "ketch", "consents": [{"consent": "analytics"}]}], "sdkBaseUrl": "https://www.googletagmanager.com", "serverContainerUrl": ""}, "destinationDefinitionId": "1mQ0yXGAQM08MTdVxws7ENIPjYS", "destinationDefinition": {"name": "GA4", "displayName": "Google Analytics 4 (GA4)"}, "updatedAt": "2024-10-01T10:07:57.063Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2M5dOW6kipDywRHHLJy8blGeaIM", "name": "FullStory Prod for SDK Team Sanity Suite", "enabled": true, "config": {"fs_debug_mode": true, "fs_org": "o-1HJK2Z-na1", "fs_host": "", "blacklistedEvents": [], "whitelistedEvents": [], "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "eventFilteringOption": "disable", "consentManagement": [{"provider": "oneTrust", "consents": []}]}, "destinationDefinitionId": "1aIY8UDEW1vZJ6n21ZO9BmRHgAp", "destinationDefinition": {"name": "FULLSTORY", "displayName": "Fullstory"}, "updatedAt": "2024-10-01T10:13:31.740Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2M5beBXLTjTto62OdCBwFNejrkY", "name": "PostHog Prod for SDK Team Sanity Suite", "enabled": true, "config": {"teamApiKey": "phc_joJ0tlU8zo8XDxkjBRuOpcsikDGDzWBGFXto0bkyoRw", "yourInstance": "", "autocapture": false, "capturePageView": false, "disableSessionRecording": false, "propertyBlackList": [], "blacklistedEvents": [], "whitelistedEvents": [], "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "enableLocalStoragePersistence": false, "eventFilteringOption": "disable", "useV2Group": false, "consentManagement": [{"provider": "oneTrust", "consents": []}]}, "destinationDefinitionId": "1kFwq1W6mIA0COSvz7Zmqsn1DFB", "destinationDefinition": {"name": "POSTHOG", "displayName": "PostHog"}, "updatedAt": "2024-10-01T10:19:05.596Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}, {"id": "2MB1bEpAABLRC9oI5G3IXrrBTos", "name": "Facebook Pixel Prod for SDK Team Sanity Suite", "enabled": true, "config": {"blacklistPiiProperties": [{"blacklistPiiProperties": "", "blacklistPiiHash": false}], "pixelId": "3626159724376295", "eventsToEvents": [{"from": "", "to": ""}], "valueFieldIdentifier": "properties.price", "advancedMapping": false, "whitelistPiiProperties": [], "blacklistedEvents": [], "whitelistedEvents": [], "oneTrustCookieCategories": [{"oneTrustCookieCategory": ""}], "ketchConsentPurposes": [{"purpose": ""}], "consentManagement": [{"provider": "oneTrust", "consents": []}, {"provider": "ketch", "consents": [{"consent": ""}]}], "eventFilteringOption": "disable", "useUpdatedMapping": false}, "destinationDefinitionId": "1aIXpsUVMU38nBFIE4QTiZu9Vxo", "destinationDefinition": {"name": "FACEBOOK_PIXEL", "displayName": "Facebook Pixel"}, "updatedAt": "2024-10-01T10:17:50.293Z", "shouldApplyDeviceModeTransformation": false, "propagateEventsUntransformedOnError": false}], "updatedAt": "2023-12-01T16:12:28.443Z"}, "updatedAt": "2024-02-26T21:10:27.357Z", "consentManagementMetadata": {"providers": [{"provider": "oneTrust", "resolutionStrategy": "and"}, {"provider": "ketch", "resolutionStrategy": "or"}, {"provider": "<PERSON>uben<PERSON>", "resolutionStrategy": "or"}]}}