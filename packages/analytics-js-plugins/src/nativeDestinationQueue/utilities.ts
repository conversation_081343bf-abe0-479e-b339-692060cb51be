/* eslint-disable @typescript-eslint/no-unused-vars */
import type { DestinationsQueueOpts } from '@rudderstack/analytics-js-common/types/LoadOptions';
import type { Destination } from '@rudderstack/analytics-js-common/types/Destination';
import type { RudderEvent } from '@rudderstack/analytics-js-common/types/Event';
import type { <PERSON><PERSON><PERSON>r<PERSON>and<PERSON> } from '@rudderstack/analytics-js-common/types/ErrorHandler';
import type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';
import type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';
import type { RudderEventType } from '../types/plugins';
import { DEFAULT_QUEUE_OPTIONS, NATIVE_DESTINATION_QUEUE_PLUGIN } from './constants';
import { INTEGRATION_EVENT_FORWARDING_ERROR } from './logMessages';
import { mergeDeepRight } from '../shared-chunks/common';

const getNormalizedQueueOptions = (queueOpts: DestinationsQueueOpts): DestinationsQueueOpts =>
  mergeDeepRight(DEFAULT_QUEUE_OPTIONS, queueOpts);

const isValidEventName = (eventName: Nullable<string>) =>
  eventName && typeof eventName === 'string';

const isEventDenyListed = (
  eventType: RudderEventType,
  eventName: Nullable<string>,
  dest: Destination,
) => {
  if (eventType !== 'track') {
    return false;
  }

  const { blacklistedEvents, whitelistedEvents, eventFilteringOption } = dest.config;

  switch (eventFilteringOption) {
    // Blacklist is chosen for filtering events
    case 'blacklistedEvents': {
      if (!isValidEventName(eventName)) {
        return false;
      }
      const trimmedEventName = (eventName as string).trim();
      if (Array.isArray(blacklistedEvents)) {
        return blacklistedEvents.some(eventObj => eventObj.eventName.trim() === trimmedEventName);
      }
      return false;
    }

    // Whitelist is chosen for filtering events
    case 'whitelistedEvents': {
      if (!isValidEventName(eventName)) {
        return true;
      }
      const trimmedEventName = (eventName as string).trim();
      if (Array.isArray(whitelistedEvents)) {
        return !whitelistedEvents.some(eventObj => eventObj.eventName.trim() === trimmedEventName);
      }
      return true;
    }

    case 'disable':
    default:
      return false;
  }
};

const sendEventToDestination = (
  item: RudderEvent,
  dest: Destination,
  errorHandler?: IErrorHandler,
  logger?: ILogger,
) => {
  const methodName = item.type.toString();
  try {
    // Destinations expect the event to be wrapped under the `message` key
    // This will remain until we update the destinations to accept the event directly
    dest.instance?.[methodName]?.({ message: item });
  } catch (err) {
    errorHandler?.onError({
      error: err,
      context: NATIVE_DESTINATION_QUEUE_PLUGIN,
      customMessage: INTEGRATION_EVENT_FORWARDING_ERROR(dest.userFriendlyId),
      groupingHash: INTEGRATION_EVENT_FORWARDING_ERROR(dest.displayName),
    });
  }
};

/**
 * A function to check if device mode transformation should be applied for a destination.
 * @param dest Destination object
 * @returns Boolean indicating whether the transformation should be applied
 */
const shouldApplyTransformation = (dest: Destination): boolean => {
  return dest.shouldApplyDeviceModeTransformation && !dest.cloned;
};

export {
  getNormalizedQueueOptions,
  isEventDenyListed,
  sendEventToDestination,
  shouldApplyTransformation,
};
