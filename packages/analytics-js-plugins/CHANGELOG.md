# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [3.10.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.10.1...@rudderstack/analytics-js-plugins@3.10.2) (2025-06-20)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.21.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.27`
## [3.10.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.10.0...@rudderstack/analytics-js-plugins@3.10.1) (2025-06-12)


### Bug Fixes

* filter disable destinations ([#2286](https://github.com/rudderlabs/rudder-sdk-js/issues/2286)) ([7ca8398](https://github.com/rudderlabs/rudder-sdk-js/commit/7ca8398cb288360e1dca49c6266c04d58ce88e05))

## [3.10.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.9.0...@rudderstack/analytics-js-plugins@3.10.0) (2025-06-11)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.20.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.26`

### Features

* add support to dynamically override destinations status ([#2266](https://github.com/rudderlabs/rudder-sdk-js/issues/2266)) ([5af2f22](https://github.com/rudderlabs/rudder-sdk-js/commit/5af2f22ebdcac4eb04d57ecb51efa427607bc849))
* disable dmt for cloned destination ([#2277](https://github.com/rudderlabs/rudder-sdk-js/issues/2277)) ([6fed4f6](https://github.com/rudderlabs/rudder-sdk-js/commit/6fed4f6a7bf338e6040e2292e965acc06fa3c1a6))
* dynamically clone destinations ([#2276](https://github.com/rudderlabs/rudder-sdk-js/issues/2276)) ([f136454](https://github.com/rudderlabs/rudder-sdk-js/commit/f1364541743b15d240ceed6d8f403c23b6984086))
* enable destination config override ([#2267](https://github.com/rudderlabs/rudder-sdk-js/issues/2267)) ([c570106](https://github.com/rudderlabs/rudder-sdk-js/commit/c5701065133d3fbddcff9072950ce935ef69e38a))
* enhance retry headers with RSA-prefixed naming ([#2279](https://github.com/rudderlabs/rudder-sdk-js/issues/2279)) ([c25b2bc](https://github.com/rudderlabs/rudder-sdk-js/commit/c25b2bc5bb4b5b41469065138eef88c2fa21a460))
* set proper grouping hash for all errors ([#2246](https://github.com/rudderlabs/rudder-sdk-js/issues/2246)) ([430c497](https://github.com/rudderlabs/rudder-sdk-js/commit/430c49782b95bf3e8de1f6a62b442b363208a66b))

## [3.9.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.8.4...@rudderstack/analytics-js-plugins@3.9.0) (2025-06-03)


### Features

* change log level to warning for retryable event delivery failures ([#2268](https://github.com/rudderlabs/rudder-sdk-js/issues/2268)) ([98490e4](https://github.com/rudderlabs/rudder-sdk-js/commit/98490e4e303d293d2f19675e07f49b398fcfc30d))
* retry delivery failures in beacon plugin when page is unloaded ([#2269](https://github.com/rudderlabs/rudder-sdk-js/issues/2269)) ([cec81f3](https://github.com/rudderlabs/rudder-sdk-js/commit/cec81f3d2aca443f6d2c209941dd28fffd65888c))

## [3.8.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.8.3...@rudderstack/analytics-js-plugins@3.8.4) (2025-05-26)


### Bug Fixes

* store events in in-progress queue even when the page is unloaded ([#2245](https://github.com/rudderlabs/rudder-sdk-js/issues/2245)) ([8f978b5](https://github.com/rudderlabs/rudder-sdk-js/commit/8f978b5d58c63747dda04df75ed05a2709bec11a))

## [3.8.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.8.2...@rudderstack/analytics-js-plugins@3.8.3) (2025-05-14)


### Bug Fixes

* store events in in-progress queue even when the page is unloaded ([#2245](https://github.com/rudderlabs/rudder-sdk-js/issues/2245)) ([843db54](https://github.com/rudderlabs/rudder-sdk-js/commit/843db54ac126b7cd7c0d22a46ee40f4b5d7cd6b4))

## [3.8.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.8.1...@rudderstack/analytics-js-plugins@3.8.2) (2025-05-09)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.19.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.25`
## [3.8.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.8.0...@rudderstack/analytics-js-plugins@3.8.1) (2025-04-25)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.18.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.24`

### Bug Fixes

* recursively migrate persisted entries ([#2187](https://github.com/rudderlabs/rudder-sdk-js/issues/2187)) ([3dd07ea](https://github.com/rudderlabs/rudder-sdk-js/commit/3dd07ea1bde4655124fc02850a022bcb550b8c07))
* rename view id to visit id ([#2086](https://github.com/rudderlabs/rudder-sdk-js/issues/2086)) ([51c8dd9](https://github.com/rudderlabs/rudder-sdk-js/commit/51c8dd94b2e25f42a116cb72d209d41729c165c0))

## [3.8.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.7.2...@rudderstack/analytics-js-plugins@3.8.0) (2025-03-03)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.2`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.23`

### Features

* add events processing and retry headers ([#2066](https://github.com/rudderlabs/rudder-sdk-js/issues/2066)) ([3113030](https://github.com/rudderlabs/rudder-sdk-js/commit/311303098d053fd0ce87d9bf8393a800112958ae))


### Bug Fixes

* handle edge cases in retry queue ([#2074](https://github.com/rudderlabs/rudder-sdk-js/issues/2074)) ([f9263b2](https://github.com/rudderlabs/rudder-sdk-js/commit/f9263b24170680023dfa1687c778b97557ef5e1b))

## [3.7.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.7.1...@rudderstack/analytics-js-plugins@3.7.2) (2025-02-20)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.1`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.22`

### Bug Fixes

* retry status code logic and error messages ([#2050](https://github.com/rudderlabs/rudder-sdk-js/issues/2050)) ([28fd410](https://github.com/rudderlabs/rudder-sdk-js/commit/28fd410f90fe2c0e5c9071d7151ac2e297340573))

## [3.7.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.7.0...@rudderstack/analytics-js-plugins@3.7.1) (2025-02-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.21`
## [3.7.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.21...@rudderstack/analytics-js-plugins@3.7.0) (2025-01-31)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.16.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.20`

### Features

* move error reporting functionality to the core module ([#2011](https://github.com/rudderlabs/rudder-sdk-js/issues/2011)) ([78c50c7](https://github.com/rudderlabs/rudder-sdk-js/commit/78c50c7a6e4169560f3182be93148f4512d313ca)), closes [#2001](https://github.com/rudderlabs/rudder-sdk-js/issues/2001) [#2002](https://github.com/rudderlabs/rudder-sdk-js/issues/2002) [#2005](https://github.com/rudderlabs/rudder-sdk-js/issues/2005) [#2006](https://github.com/rudderlabs/rudder-sdk-js/issues/2006) [#2007](https://github.com/rudderlabs/rudder-sdk-js/issues/2007)

## [3.6.22](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.21...@rudderstack/analytics-js-plugins@3.6.22) (2025-01-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.15.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.19`
## [3.6.21](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.20...@rudderstack/analytics-js-plugins@3.6.21) (2025-01-03)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.15`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.18`
## [3.6.20](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.19...@rudderstack/analytics-js-plugins@3.6.20) (2024-12-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.14`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.17`

### Bug Fixes

* remove circular dependency in packages ([#1973](https://github.com/rudderlabs/rudder-sdk-js/issues/1973)) ([e525496](https://github.com/rudderlabs/rudder-sdk-js/commit/e5254964310c2c73baaf4d0655c3e4025c5e7d2b))
* separator and make changes in bugsnag plugin ([b69347c](https://github.com/rudderlabs/rudder-sdk-js/commit/b69347cd9bbf3a395b2f557f8219287900ceca5a))

## [3.6.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.18...@rudderstack/analytics-js-plugins@3.6.19) (2024-12-13)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.15`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.16`
## [3.6.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.17...@rudderstack/analytics-js-plugins@3.6.18) (2024-12-06)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.13`
* `@rudderstack/analytics-js` updated to version `3.11.14`
## [3.6.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.16...@rudderstack/analytics-js-plugins@3.6.17) (2024-11-30)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.13`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.15`
## [3.6.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.15...@rudderstack/analytics-js-plugins@3.6.16) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.12`
* `@rudderstack/analytics-js` updated to version `3.11.12`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.14`
## [3.6.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.14...@rudderstack/analytics-js-plugins@3.6.15) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.11`
* `@rudderstack/analytics-js` updated to version `3.11.11`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.13`
## [3.6.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.13...@rudderstack/analytics-js-plugins@3.6.14) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.10`
* `@rudderstack/analytics-js` updated to version `3.11.10`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.12`

### Bug Fixes

* restore data sanitization changes but avoid using api overloads ([d0913ae](https://github.com/rudderlabs/rudder-sdk-js/commit/d0913ae32a8c63def26c081c7570a9960dcd1ebf))

## [3.6.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.12...@rudderstack/analytics-js-plugins@3.6.13) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.9`
* `@rudderstack/analytics-js` updated to version `3.11.9`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.11`

### Bug Fixes

* reorganize common imports in plugins ([bc569ba](https://github.com/rudderlabs/rudder-sdk-js/commit/bc569baa4ae2211696934d489a370e2e9ca80521))
* restore data sanitization changes ([2a13e7c](https://github.com/rudderlabs/rudder-sdk-js/commit/2a13e7c463b2d480f6d9a23f32abe4e56f6557d4))

## [3.6.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.11...@rudderstack/analytics-js-plugins@3.6.12) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.8`
* `@rudderstack/analytics-js` updated to version `3.11.8`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.10`

### Bug Fixes

* sanitize data directly in plugins ([d8cc780](https://github.com/rudderlabs/rudder-sdk-js/commit/d8cc7808e21baeb26782596efb542713bd38a09f))

## [3.6.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.10...@rudderstack/analytics-js-plugins@3.6.11) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.7`
* `@rudderstack/analytics-js` updated to version `3.11.7`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.9`
## [3.6.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.9...@rudderstack/analytics-js-plugins@3.6.10) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.6`
* `@rudderstack/analytics-js` updated to version `3.11.6`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.8`
## [3.6.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.8...@rudderstack/analytics-js-plugins@3.6.9) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.5`
* `@rudderstack/analytics-js` updated to version `3.11.5`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.7`
## [3.6.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.7...@rudderstack/analytics-js-plugins@3.6.8) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.4`
* `@rudderstack/analytics-js` updated to version `3.11.4`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.6`
## [3.6.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.6...@rudderstack/analytics-js-plugins@3.6.7) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.3`
* `@rudderstack/analytics-js` updated to version `3.11.3`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.5`
## [3.6.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.5...@rudderstack/analytics-js-plugins@3.6.6) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.2`
## [3.6.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.4...@rudderstack/analytics-js-plugins@3.6.5) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.11.2`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.4`

### Bug Fixes

* exclude auth token ([15dc64c](https://github.com/rudderlabs/rudder-sdk-js/commit/15dc64ccfbac66fa33046abd88cb72c9b0ccb147))

## [3.6.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.3...@rudderstack/analytics-js-plugins@3.6.4) (2024-11-19)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.1`
* `@rudderstack/analytics-js` updated to version `3.11.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.3`
## [3.6.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.2...@rudderstack/analytics-js-plugins@3.6.3) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.0`
* `@rudderstack/analytics-js` updated to version `3.10.2`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.2`
## [3.6.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.1...@rudderstack/analytics-js-plugins@3.6.2) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.13.0`
* `@rudderstack/analytics-js` updated to version `3.10.1`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.1`
## [3.6.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.6.0...@rudderstack/analytics-js-plugins@3.6.1) (2024-11-12)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.12.1`
* `@rudderstack/analytics-js` updated to version `3.10.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.4.0`

### Bug Fixes

* batch entry retry ([#1918](https://github.com/rudderlabs/rudder-sdk-js/issues/1918)) ([ff346b8](https://github.com/rudderlabs/rudder-sdk-js/commit/ff346b867335750d7b428cab9c650a4d9dbfde57))
* revert sanitization changes ([#1916](https://github.com/rudderlabs/rudder-sdk-js/issues/1916)) ([890fb7b](https://github.com/rudderlabs/rudder-sdk-js/commit/890fb7b615535992290f5008b93d77b540c03955)), closes [#1907](https://github.com/rudderlabs/rudder-sdk-js/issues/1907) [#1902](https://github.com/rudderlabs/rudder-sdk-js/issues/1902)

## [3.6.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.5.2...@rudderstack/analytics-js-plugins@3.6.0) (2024-11-08)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.12.0`
* `@rudderstack/analytics-js` updated to version `3.9.1`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.14`

### Features

* sanitize input data ([#1902](https://github.com/rudderlabs/rudder-sdk-js/issues/1902)) ([b71c44a](https://github.com/rudderlabs/rudder-sdk-js/commit/b71c44ae61f6c35cadc6523b918e1a574e32bc23))

## [3.5.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.5.1...@rudderstack/analytics-js-plugins@3.5.2) (2024-11-07)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.11.1`
* `@rudderstack/analytics-js` updated to version `3.9.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.13`
## [3.5.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.5.0...@rudderstack/analytics-js-plugins@3.5.1) (2024-10-25)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.11.0`
* `@rudderstack/analytics-js` updated to version `3.8.0`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.12`
## [3.5.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.19...@rudderstack/analytics-js-plugins@3.5.0) (2024-10-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.10.0`
* `@rudderstack/analytics-js` updated to version `3.7.19`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.11`

### Features

* iubenda consent manager plugin ([#1809](https://github.com/rudderlabs/rudder-sdk-js/issues/1809)) ([7ea300c](https://github.com/rudderlabs/rudder-sdk-js/commit/7ea300c61ead9cc094c3f1985e0ef3165b0fcb59))

## [3.4.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.18...@rudderstack/analytics-js-plugins@3.4.19) (2024-10-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.5`
* `@rudderstack/analytics-js` updated to version `3.7.18`
## [3.4.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.17...@rudderstack/analytics-js-plugins@3.4.18) (2024-10-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.4`
## [3.4.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.16...@rudderstack/analytics-js-plugins@3.4.17) (2024-10-11)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.17`

### Bug Fixes

* global integrations object is not used for preloaded events ([#1881](https://github.com/rudderlabs/rudder-sdk-js/issues/1881)) ([2776f07](https://github.com/rudderlabs/rudder-sdk-js/commit/2776f07e0b0142e05bd4bd3dc053c484c8ecf8a0))

## [3.4.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.15...@rudderstack/analytics-js-plugins@3.4.16) (2024-10-10)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.16`

### Bug Fixes

* avoid using requestAnimationFrame for polling ([#1878](https://github.com/rudderlabs/rudder-sdk-js/issues/1878)) ([1aca5a0](https://github.com/rudderlabs/rudder-sdk-js/commit/1aca5a0351b44d0dd1d2b91fa7475f86f330ba3f))

## [3.4.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.14...@rudderstack/analytics-js-plugins@3.4.15) (2024-10-03)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.14`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.9`
## [3.4.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.13...@rudderstack/analytics-js-plugins@3.4.14) (2024-09-27)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.3`
* `@rudderstack/analytics-js` updated to version `3.7.13`

### Bug Fixes

* filter non error type errors ([#1865](https://github.com/rudderlabs/rudder-sdk-js/issues/1865)) ([060f66c](https://github.com/rudderlabs/rudder-sdk-js/commit/060f66cbf33500d6e0ee47c788ed687f4f619a57))
* upgrade all packages to latest to fix vulnerabilities ([#1867](https://github.com/rudderlabs/rudder-sdk-js/issues/1867)) ([389348c](https://github.com/rudderlabs/rudder-sdk-js/commit/389348cfa61f2111c5ac4f9e2bad5851a466484d))

## [3.4.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.12...@rudderstack/analytics-js-plugins@3.4.13) (2024-09-17)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.13`
## [3.4.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.11...@rudderstack/analytics-js-plugins@3.4.12) (2024-09-12)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.2`
* `@rudderstack/analytics-js` updated to version `3.7.11`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.8`
## [3.4.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.10...@rudderstack/analytics-js-plugins@3.4.11) (2024-08-30)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.11`
## [3.4.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.9...@rudderstack/analytics-js-plugins@3.4.10) (2024-08-28)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.1`
* `@rudderstack/analytics-js` updated to version `3.7.9`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.7`

### Bug Fixes

* handle blur and focus events to detect page leave ([#1837](https://github.com/rudderlabs/rudder-sdk-js/issues/1837)) ([57e735c](https://github.com/rudderlabs/rudder-sdk-js/commit/57e735ced4fb51ec895fbb196b1b879996cc10dd))

## [3.4.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.8...@rudderstack/analytics-js-plugins@3.4.9) (2024-08-16)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.0`
* `@rudderstack/analytics-js` updated to version `3.7.8`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.6`
## [3.4.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.7...@rudderstack/analytics-js-plugins@3.4.8) (2024-08-16)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.7.7`
## [3.4.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.6...@rudderstack/analytics-js-plugins@3.4.7) (2024-08-02)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.8.1`
* `@rudderstack/analytics-js` updated to version `3.7.6`
* `@rudderstack/analytics-js-cookies` updated to version `0.3.5`

### Bug Fixes

* error filtering of non-errors ([#1811](https://github.com/rudderlabs/rudder-sdk-js/issues/1811)) ([7b83e16](https://github.com/rudderlabs/rudder-sdk-js/commit/7b83e1661b1e0ce0b6b5ae45d3a2e08db97ddcb3))
* npm sanity suites ([#1810](https://github.com/rudderlabs/rudder-sdk-js/issues/1810)) ([22e43da](https://github.com/rudderlabs/rudder-sdk-js/commit/22e43da01f750a5cb23a2fce50de3744c54a197e))

## [3.4.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.5...@rudderstack/analytics-js-plugins@3.4.6) (2024-07-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.4.5`
* `@rudderstack/analytics-js-cookies` updated to version `3.4.5`
## [3.4.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.4...@rudderstack/analytics-js-plugins@3.4.5) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.4.4`
## [3.4.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.3...@rudderstack/analytics-js-plugins@3.4.4) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.4.3`
## [3.4.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.2...@rudderstack/analytics-js-plugins@3.4.3) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.4.2`
## [3.4.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.1...@rudderstack/analytics-js-plugins@3.4.2) (2024-07-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.4.1`
## [3.4.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.4.0...@rudderstack/analytics-js-plugins@3.4.1) (2024-07-22)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.4.0`
## [3.4.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.3.2...@rudderstack/analytics-js-plugins@3.4.0) (2024-07-19)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.3.2`
* `@rudderstack/analytics-js` updated to version `3.3.2`
* `@rudderstack/analytics-js-cookies` updated to version `3.3.2`

### Features

* error reporting plugin ([#1601](https://github.com/rudderlabs/rudder-sdk-js/issues/1601)) ([1f2629e](https://github.com/rudderlabs/rudder-sdk-js/commit/1f2629e594740763ce9bd54a21213b92d80ae085))


### Bug Fixes

* event API overloads ([#1782](https://github.com/rudderlabs/rudder-sdk-js/issues/1782)) ([02c5b47](https://github.com/rudderlabs/rudder-sdk-js/commit/02c5b47d0a83250fb5180e9ed467a92361663dab))

## [3.3.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.3.1...@rudderstack/analytics-js-plugins@3.3.2) (2024-07-05)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.3.1`
* `@rudderstack/analytics-js` updated to version `3.3.1`
* `@rudderstack/analytics-js-cookies` updated to version `3.3.1`

### Bug Fixes

* package lint issues ([#1773](https://github.com/rudderlabs/rudder-sdk-js/issues/1773)) ([8e45d05](https://github.com/rudderlabs/rudder-sdk-js/commit/8e45d052bd6366d647d06226aa89b1fa2e512f9d))

## [3.3.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.3.0...@rudderstack/analytics-js-plugins@3.3.1) (2024-07-04)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.3.0`
* `@rudderstack/analytics-js` updated to version `3.3.0`
* `@rudderstack/analytics-js-cookies` updated to version `3.3.0`
## [3.3.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.2.1...@rudderstack/analytics-js-plugins@3.3.0) (2024-07-01)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.2.1`
* `@rudderstack/analytics-js-cookies` updated to version `3.2.1`

### Features

* expose exclusive cookies functions for server and browser environments ([#1774](https://github.com/rudderlabs/rudder-sdk-js/issues/1774)) ([428e5cd](https://github.com/rudderlabs/rudder-sdk-js/commit/428e5cd96c389ab0944fa9255d6d68c56c71908c))

## [3.2.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.2.0...@rudderstack/analytics-js-plugins@3.2.1) (2024-06-25)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.2.0`
* `@rudderstack/analytics-js-cookies` updated to version `3.2.0`
## [3.2.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.1.1...@rudderstack/analytics-js-plugins@3.2.0) (2024-06-25)

### Dependency Updates

* `@rudderstack/analytics-js` updated to version `3.1.1`
* `@rudderstack/analytics-js-cookies` updated to version `3.1.1`

### Features

* create new package for cookie utilities ([#1759](https://github.com/rudderlabs/rudder-sdk-js/issues/1759)) ([92826a4](https://github.com/rudderlabs/rudder-sdk-js/commit/92826a4f2e87dd2d0d755016592e36e4708f34d4))

## [3.1.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.1.0...@rudderstack/analytics-js-plugins@3.1.1) (2024-06-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.1.0`
* `@rudderstack/analytics-js` updated to version `3.1.0`

### Bug Fixes

* add state metadata even for unhandled errors ([#1755](https://github.com/rudderlabs/rudder-sdk-js/issues/1755)) ([66fc415](https://github.com/rudderlabs/rudder-sdk-js/commit/66fc415474ffccd684972b47f9926ab87c0a514c))
* improve flushing events on page leave ([#1754](https://github.com/rudderlabs/rudder-sdk-js/issues/1754)) ([1be420f](https://github.com/rudderlabs/rudder-sdk-js/commit/1be420fae16b68629789d2ba37e16e6a6e00017c))

## [3.1.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.6...@rudderstack/analytics-js-plugins@3.1.0) (2024-06-07)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.6`

### Features

* add install type to context ([#1740](https://github.com/rudderlabs/rudder-sdk-js/issues/1740)) ([3d25b65](https://github.com/rudderlabs/rudder-sdk-js/commit/3d25b654a70b0f39c412e80465e29e2bdb578aa7))


### Bug Fixes

* url validation ([#1730](https://github.com/rudderlabs/rudder-sdk-js/issues/1730)) ([3a3e105](https://github.com/rudderlabs/rudder-sdk-js/commit/3a3e1057f2db91ef5cbf652a664db9443fee9843))

## [3.0.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.5...@rudderstack/analytics-js-plugins@3.0.6) (2024-05-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.5`

### Bug Fixes

* user sessions behavior ([#1708](https://github.com/rudderlabs/rudder-sdk-js/issues/1708)) ([84e7174](https://github.com/rudderlabs/rudder-sdk-js/commit/84e71744612c8345dc22b8cb0c9362d104eb35e9))

## [3.0.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.4...@rudderstack/analytics-js-plugins@3.0.5) (2024-05-10)


### Bug Fixes

* update dmt plugin to use activeDataplaneUrl state value ([#1711](https://github.com/rudderlabs/rudder-sdk-js/issues/1711)) ([f57bdae](https://github.com/rudderlabs/rudder-sdk-js/commit/f57bdae1a92de456e469e321b792ed2d11a9ea25))

## [3.0.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.3...@rudderstack/analytics-js-plugins@3.0.4) (2024-04-26)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.3`
## [3.0.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.2...@rudderstack/analytics-js-plugins@3.0.3) (2024-03-22)


### Bug Fixes

* **analytics-js-plugins:** add backward compatibility for storage api ([#1669](https://github.com/rudderlabs/rudder-sdk-js/issues/1669)) ([83eef03](https://github.com/rudderlabs/rudder-sdk-js/commit/83eef031152cc954ae4bb692bf6ddb73b5b170c8))

## [3.0.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.1...@rudderstack/analytics-js-plugins@3.0.2) (2024-03-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.1`

### Bug Fixes

* optimize localstorage transactions ([#1651](https://github.com/rudderlabs/rudder-sdk-js/issues/1651)) ([1289217](https://github.com/rudderlabs/rudder-sdk-js/commit/12892176578dd3628fded2311ea2548e3ff5802c))

## [3.0.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.21...@rudderstack/analytics-js-plugins@3.0.1) (2024-03-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.0`
## [3.0.0-beta.21](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.20...@rudderstack/analytics-js-plugins@3.0.0-beta.21) (2024-03-18)

## [3.0.0-beta.20](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.19...@rudderstack/analytics-js-plugins@3.0.0-beta.20) (2024-03-01)

## [3.0.0-beta.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.18...@rudderstack/analytics-js-plugins@3.0.0-beta.19) (2024-02-16)


### Bug Fixes

* replace lodash.pick with ramda to avoid vulnerabilities ([#1615](https://github.com/rudderlabs/rudder-sdk-js/issues/1615)) ([af9fc16](https://github.com/rudderlabs/rudder-sdk-js/commit/af9fc164612ab9c18656b016a32cc83bf43d9f8f))

## [3.0.0-beta.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.17...@rudderstack/analytics-js-plugins@3.0.0-beta.18) (2024-02-02)

## [3.0.0-beta.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.16...@rudderstack/analytics-js-plugins@3.0.0-beta.17) (2024-01-19)

## [3.0.0-beta.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.15...@rudderstack/analytics-js-plugins@3.0.0-beta.16) (2024-01-08)


### Features

* increased request timeout value ([#1551](https://github.com/rudderlabs/rudder-sdk-js/issues/1551)) ([6e6b2f4](https://github.com/rudderlabs/rudder-sdk-js/commit/6e6b2f423fe1cfecda054d393424b7f523e80821))
* remove support for category names in onetrust plugin ([#1556](https://github.com/rudderlabs/rudder-sdk-js/issues/1556)) ([2977c19](https://github.com/rudderlabs/rudder-sdk-js/commit/2977c194ec6ef877547687f3f48a161c69dace3c))


### Bug Fixes

* enhance error handling ([#1568](https://github.com/rudderlabs/rudder-sdk-js/issues/1568)) ([0f08ba4](https://github.com/rudderlabs/rudder-sdk-js/commit/0f08ba4009de27647e2220aa2e56e30d5a99e902))
* localstorage retry patch and upgrade packages ([#1573](https://github.com/rudderlabs/rudder-sdk-js/issues/1573)) ([b14d027](https://github.com/rudderlabs/rudder-sdk-js/commit/b14d0276cc7dedf87062530eab404f7a924fecf7))

## [3.0.0-beta.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.14...@rudderstack/analytics-js-plugins@3.0.0-beta.15) (2023-12-13)


### Features

* introduced root level sentat for batch request ([#1531](https://github.com/rudderlabs/rudder-sdk-js/issues/1531)) ([24297f2](https://github.com/rudderlabs/rudder-sdk-js/commit/24297f2e48205ec4b8d780ff148d2d143a9f4eb0))


### Bug Fixes

* **analytics-js:** new export that excludes all third party host scripts ([#1533](https://github.com/rudderlabs/rudder-sdk-js/issues/1533)) ([999d8fa](https://github.com/rudderlabs/rudder-sdk-js/commit/999d8fa0262e7cd7b021404ec28a99fc64dd323d))

## [3.0.0-beta.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.13...@rudderstack/analytics-js-plugins@3.0.0-beta.14) (2023-12-01)


### Features

* **analytics-js-loading-scripts:** add loading snippet version in event context ([#1483](https://github.com/rudderlabs/rudder-sdk-js/issues/1483)) ([4873cbc](https://github.com/rudderlabs/rudder-sdk-js/commit/4873cbc183879c0c1825cf939a53b6cf570cdf4e))

## [3.0.0-beta.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.12...@rudderstack/analytics-js-plugins@3.0.0-beta.13) (2023-11-13)


### Features

* consent api wrap up ([#1477](https://github.com/rudderlabs/rudder-sdk-js/issues/1477)) ([edc78ac](https://github.com/rudderlabs/rudder-sdk-js/commit/edc78ac54235aabfc8d1d78f961fbd650a3b7c73))
* reinitialize persistent data from consent options ([#1465](https://github.com/rudderlabs/rudder-sdk-js/issues/1465)) ([43f30b7](https://github.com/rudderlabs/rudder-sdk-js/commit/43f30b7296ae9a0862810fd0b3c520e8bddf614c))


### Bug Fixes

* gcm post consent issues ([#1507](https://github.com/rudderlabs/rudder-sdk-js/issues/1507)) ([6241d3d](https://github.com/rudderlabs/rudder-sdk-js/commit/6241d3da28edc9b5f478774a763226d9d72f7dcc))

## [3.0.0-beta.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.11...@rudderstack/analytics-js-plugins@3.0.0-beta.12) (2023-10-27)


### Features

* alter life cycle for pre consent ([#1411](https://github.com/rudderlabs/rudder-sdk-js/issues/1411)) ([60ec092](https://github.com/rudderlabs/rudder-sdk-js/commit/60ec0924a1229678fb16d76a34a494c40a622a11))
* consent api ([#1458](https://github.com/rudderlabs/rudder-sdk-js/issues/1458)) ([216b405](https://github.com/rudderlabs/rudder-sdk-js/commit/216b405f7c319d5ff2d799d2e3a5efe2ee4a03af))


### Bug Fixes

* **analytics-js:** fix remote import error when npm package is bundled with webpack ([#1466](https://github.com/rudderlabs/rudder-sdk-js/issues/1466)) ([3a818ac](https://github.com/rudderlabs/rudder-sdk-js/commit/3a818accd24e6b3667c75a6b60fb12aba36bdf7e))
* **monorepo:** update vulnerable dependencies ([#1457](https://github.com/rudderlabs/rudder-sdk-js/issues/1457)) ([7a4bc4c](https://github.com/rudderlabs/rudder-sdk-js/commit/7a4bc4cc641e4fff2a8f561ce6fd67d16c0cd5a0))
* upgrade vulnerable cryptoJS dependency, rolup to v4 & NX to v17 ([#1471](https://github.com/rudderlabs/rudder-sdk-js/issues/1471)) ([b2bb21c](https://github.com/rudderlabs/rudder-sdk-js/commit/b2bb21cb3f618f6c86f593d1706abe9e6349066d))

## [3.0.0-beta.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.10...@rudderstack/analytics-js-plugins@3.0.0-beta.11) (2023-10-16)


### Features

* **analytics-js-service-worker:** deprecate service worker export of rudder-sdk-js package  in favor of the new standalone package([#1437](https://github.com/rudderlabs/rudder-sdk-js/issues/1437)) ([1797d3e](https://github.com/rudderlabs/rudder-sdk-js/commit/****************************************))
* dmt plugin for v3 ([#1412](https://github.com/rudderlabs/rudder-sdk-js/issues/1412)) ([97ee68a](https://github.com/rudderlabs/rudder-sdk-js/commit/97ee68a27daa5ce8c3a098cdc84c4ee7981f1149))


### Bug Fixes

* **analytics-js:** add global definitions extended window type ([#1445](https://github.com/rudderlabs/rudder-sdk-js/issues/1445)) ([b995635](https://github.com/rudderlabs/rudder-sdk-js/commit/b995635a7a3979173d35b34fa32b41b4429b166f))
* identify traits type ([#1427](https://github.com/rudderlabs/rudder-sdk-js/issues/1427)) ([a58c919](https://github.com/rudderlabs/rudder-sdk-js/commit/a58c919ca36fc4e14d134455a08fe0e35f3e66ce))

## [3.0.0-beta.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.9...@rudderstack/analytics-js-plugins@3.0.0-beta.10) (2023-09-26)


### Features

* optimize plugin chunks ([#1379](https://github.com/rudderlabs/rudder-sdk-js/issues/1379)) ([5acfa4d](https://github.com/rudderlabs/rudder-sdk-js/commit/5acfa4d61d85e01c44252749074021d0a782b59e))
* simplify queue batching options ([#1400](https://github.com/rudderlabs/rudder-sdk-js/issues/1400)) ([9c41cd1](https://github.com/rudderlabs/rudder-sdk-js/commit/9c41cd19a2dc1ca98a12afabf088ef6d230f4fee))

## [3.0.0-beta.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.8...@rudderstack/analytics-js-plugins@3.0.0-beta.9) (2023-09-20)

## [3.0.0-beta.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.7...@rudderstack/analytics-js-plugins@3.0.0-beta.8) (2023-09-18)


### Features

* deprecate support of common names for integrations ([#1374](https://github.com/rudderlabs/rudder-sdk-js/issues/1374)) ([f1d097d](https://github.com/rudderlabs/rudder-sdk-js/commit/f1d097d9976f6c9d2ad0f1d81d469148f8c7c197))

## [3.0.0-beta.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.6...@rudderstack/analytics-js-plugins@3.0.0-beta.7) (2023-09-14)


### Features

* granular control of persisted data storing with auto migration ([#1329](https://github.com/rudderlabs/rudder-sdk-js/issues/1329)) ([b709edc](https://github.com/rudderlabs/rudder-sdk-js/commit/b709edcbf9314d26fb9cd0af5fa8790330853d9c))

# [3.0.0-beta.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.5...@rudderstack/analytics-js-plugins@3.0.0-beta.6) (2023-08-30)


### Bug Fixes

* stop sending network breadcrumbs to bugsnag ([#1334](https://github.com/rudderlabs/rudder-sdk-js/issues/1334)) ([28a9be8](https://github.com/rudderlabs/rudder-sdk-js/commit/28a9be896963f47fb07bfd9ae3164f5132975948))


### Features

* add batching support to xhr plugin ([#1301](https://github.com/rudderlabs/rudder-sdk-js/issues/1301)) ([0421663](https://github.com/rudderlabs/rudder-sdk-js/commit/04216637a00dc5339cf466a586137415b46b6b49))





# [3.0.0-beta.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.4...@rudderstack/analytics-js-plugins@3.0.0-beta.5) (2023-08-21)

**Note:** Version bump only for package @rudderstack/analytics-js-plugins





# [3.0.0-beta.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.3...@rudderstack/analytics-js-plugins@3.0.0-beta.4) (2023-08-17)

**Note:** Version bump only for package @rudderstack/analytics-js-plugins





# [3.0.0-beta.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-plugins@3.0.0-beta.2...@rudderstack/analytics-js-plugins@3.0.0-beta.3) (2023-08-10)


### Bug Fixes

* integrations data override to match with v1.1 ([#1293](https://github.com/rudderlabs/rudder-sdk-js/issues/1293)) ([f68f138](https://github.com/rudderlabs/rudder-sdk-js/commit/f68f138513301d19f7be7bfff474ff8011a2935c))





# 3.0.0-beta.2 (2023-08-09)


### Bug Fixes

* **analytics-js:** add new flag in errorHandler, add state reset method ([#998](https://github.com/rudderlabs/rudder-sdk-js/issues/998)) ([4c76315](https://github.com/rudderlabs/rudder-sdk-js/commit/4c76315481793cd29ae4dc9b249a0684df2540d4))
* **analytics-js:** fix issue with proxy dataplane url with trailing slash ([ef992a6](https://github.com/rudderlabs/rudder-sdk-js/commit/ef992a664171e58bc60628221cbfad73f2830e2c))
* **analytics-js:** fix issues with tracking methods overloads ([#1164](https://github.com/rudderlabs/rudder-sdk-js/issues/1164)) ([718f9a9](https://github.com/rudderlabs/rudder-sdk-js/commit/718f9a9bf9e24fa203cfe9cec835528c91ed955f))
* **analytics-js:** support imports in SSR & reduce shared bundles code ([#1135](https://github.com/rudderlabs/rudder-sdk-js/issues/1135)) ([29d1d75](https://github.com/rudderlabs/rudder-sdk-js/commit/29d1d75325c732d62b9926d3848c0b1b2e566c85))
* config url deduction ([#1282](https://github.com/rudderlabs/rudder-sdk-js/issues/1282)) ([658dc24](https://github.com/rudderlabs/rudder-sdk-js/commit/658dc24e077035898871888bfd4c72e88f16deb2))
* error handler when destination ready check times out ([#1225](https://github.com/rudderlabs/rudder-sdk-js/issues/1225)) ([6056063](https://github.com/rudderlabs/rudder-sdk-js/commit/60560630522ee589539b52aec030cc9dbfda5988))
* error reporting ([#1285](https://github.com/rudderlabs/rudder-sdk-js/issues/1285)) ([1b9324e](https://github.com/rudderlabs/rudder-sdk-js/commit/1b9324e0be38eecbc25cb08be7650d8c1e474d35))
* event filtering for empty or non-string event names  ([#1156](https://github.com/rudderlabs/rudder-sdk-js/issues/1156)) ([4f71f08](https://github.com/rudderlabs/rudder-sdk-js/commit/4f71f088d265ceddb3a0dd73832918508cf58da1))
* issues post sanity checks, tidy up code structure, add uaCH, npm packaging ([#1132](https://github.com/rudderlabs/rudder-sdk-js/issues/1132)) ([0fa64c1](https://github.com/rudderlabs/rudder-sdk-js/commit/0fa64c1bb277cbd20b0d7c984347e5fe52e4d4fe))
* **monorepo:** expose one trust plugin in remores app ([0dd710a](https://github.com/rudderlabs/rudder-sdk-js/commit/0dd710a6cb47b9dd33f62e9eaaf0002ab131083e))
* native destinations queue options ([#1209](https://github.com/rudderlabs/rudder-sdk-js/issues/1209)) ([0341fc8](https://github.com/rudderlabs/rudder-sdk-js/commit/0341fc8a35433209a402f497cd92865bcec9f20f))
* normalize all error messages ([#1191](https://github.com/rudderlabs/rudder-sdk-js/issues/1191)) ([b45f3f3](https://github.com/rudderlabs/rudder-sdk-js/commit/b45f3f324afd2df6e806a586fe7d281392b03d79))
* use destination display name throughout the app ([#1269](https://github.com/rudderlabs/rudder-sdk-js/issues/1269)) ([6e6a18c](https://github.com/rudderlabs/rudder-sdk-js/commit/6e6a18c5248654963130e24d94191350292a5f58))
* use globalThis instead of global ([a4ba5dd](https://github.com/rudderlabs/rudder-sdk-js/commit/a4ba5dd894e4acbff690e5a9940c3e88b3bd7d8b))
* xhr queue plugin retry mechanism ([#1171](https://github.com/rudderlabs/rudder-sdk-js/issues/1171)) ([6d8d2b9](https://github.com/rudderlabs/rudder-sdk-js/commit/6d8d2b9db554459061995494de0b42c1f35b3bb6))


### Features

* add application state to bugsnag metadata ([#1168](https://github.com/rudderlabs/rudder-sdk-js/issues/1168)) ([7273e3a](https://github.com/rudderlabs/rudder-sdk-js/commit/7273e3af6683165c3c33265c64db6fb28a3ff5e5))
* add validations for load options ([#1277](https://github.com/rudderlabs/rudder-sdk-js/issues/1277)) ([1a276bf](https://github.com/rudderlabs/rudder-sdk-js/commit/1a276bf99471790080bc74f3e126e208cb416eaf))
* **analytics-js-plugins:** add google linker plugin, use new state ([b3d5cf3](https://github.com/rudderlabs/rudder-sdk-js/commit/b3d5cf388b39c9ca3777918ed0a0412bfb19321b))
* **analytics-js-plugins:** add store encrypt remote plugin ([0a2ec6c](https://github.com/rudderlabs/rudder-sdk-js/commit/0a2ec6c9861647d99b1a4ee1391826c285cd9865))
* **analytics-js-plugins:** new beacon queue plugin ([#1173](https://github.com/rudderlabs/rudder-sdk-js/issues/1173)) ([9e4602b](https://github.com/rudderlabs/rudder-sdk-js/commit/9e4602b67c7ce1345023388e09c3701820f71091))
* **analytics-js:** add external source loader, fix async tests, cleanup ([8ba7bdf](https://github.com/rudderlabs/rudder-sdk-js/commit/8ba7bdf260a6771bf4cfc154b9f84ab61846a622))
* **analytics-js:** add global state initial structure ([f636227](https://github.com/rudderlabs/rudder-sdk-js/commit/f636227e0094a4a3f0bfdc17d52c4731ab17e20c))
* **analytics-js:** add more state slices ([#973](https://github.com/rudderlabs/rudder-sdk-js/issues/973)) ([7c1e627](https://github.com/rudderlabs/rudder-sdk-js/commit/7c1e6275ad9eeec2ccdd4a100b085437f78a2603))
* bugsnag plugin ([#1159](https://github.com/rudderlabs/rudder-sdk-js/issues/1159)) ([c59cfd9](https://github.com/rudderlabs/rudder-sdk-js/commit/c59cfd9e6e4160e4759695dddf527bfc512f119e))
* configurable storage type ([#1258](https://github.com/rudderlabs/rudder-sdk-js/issues/1258)) ([08e3616](https://github.com/rudderlabs/rudder-sdk-js/commit/08e3616bece2ad3df1c533833b344a9c811e70fe))
* consent manager plugin ([#1096](https://github.com/rudderlabs/rudder-sdk-js/issues/1096)) ([7af1cce](https://github.com/rudderlabs/rudder-sdk-js/commit/7af1ccec03997cd55ce70aa1e4afba05b22da264))
* create bundling and packaging for v3 ([#1098](https://github.com/rudderlabs/rudder-sdk-js/issues/1098)) ([3f14bbe](https://github.com/rudderlabs/rudder-sdk-js/commit/3f14bbe8d9d6af62d4366873c59c9c21df704675))
* dataplane events queue ([#1088](https://github.com/rudderlabs/rudder-sdk-js/issues/1088)) ([17f45bc](https://github.com/rudderlabs/rudder-sdk-js/commit/17f45bc1a57f37edee56808aa1f337deef208528))
* events repository ([#1063](https://github.com/rudderlabs/rudder-sdk-js/issues/1063)) ([8a92dcb](https://github.com/rudderlabs/rudder-sdk-js/commit/8a92dcb14311b3537d391375fc0ed34433b5afe7))
* hybrid mode ([#1147](https://github.com/rudderlabs/rudder-sdk-js/issues/1147)) ([e623214](https://github.com/rudderlabs/rudder-sdk-js/commit/e6232145818032aa6e33130511b1e1d41d4a293b))
* improve adblocker detection ([#1176](https://github.com/rudderlabs/rudder-sdk-js/issues/1176)) ([6fb57ef](https://github.com/rudderlabs/rudder-sdk-js/commit/6fb57ef40c4ea73cb9d1c01844458702e2819ebc))
* improve destination loader logic ([#1263](https://github.com/rudderlabs/rudder-sdk-js/issues/1263)) ([c154155](https://github.com/rudderlabs/rudder-sdk-js/commit/c154155102f22ac17c6f82b8869b85000a5cc69d))
* ketch consent manager plugin ([#1210](https://github.com/rudderlabs/rudder-sdk-js/issues/1210)) ([75d4588](https://github.com/rudderlabs/rudder-sdk-js/commit/75d4588481e3fe86bad804162663f332ce2f895d))
* log messages language dictionary ([#1206](https://github.com/rudderlabs/rudder-sdk-js/issues/1206)) ([77a867e](https://github.com/rudderlabs/rudder-sdk-js/commit/77a867e9c109122a9223293cb5af25f1ccb48ecc))
* native destinations events queue ([#1127](https://github.com/rudderlabs/rudder-sdk-js/issues/1127)) ([ead338c](https://github.com/rudderlabs/rudder-sdk-js/commit/ead338cb5a45c7d109428259459892ff896a0ccb))
* plugins manager & capabilities manager ([#1062](https://github.com/rudderlabs/rudder-sdk-js/issues/1062)) ([9d03bbd](https://github.com/rudderlabs/rudder-sdk-js/commit/9d03bbdea3bf2658f56580aa9bb8df2af9baf9a0))
* refactor apis ([#1240](https://github.com/rudderlabs/rudder-sdk-js/issues/1240)) ([4f25a03](https://github.com/rudderlabs/rudder-sdk-js/commit/4f25a0377ef438a4e4b5dcad6f2504ec5da5f7a3))
* remove crypto based encryption for persistent data ([#1197](https://github.com/rudderlabs/rudder-sdk-js/issues/1197)) ([187b701](https://github.com/rudderlabs/rudder-sdk-js/commit/187b7016e75f092c54698fe7fe3652656943e35f))
* rename sdk file name ([#1190](https://github.com/rudderlabs/rudder-sdk-js/issues/1190)) ([0167e38](https://github.com/rudderlabs/rudder-sdk-js/commit/0167e384a05e1fa33b3da3b940f3952ee06ef21e))
* user session manager ([#1013](https://github.com/rudderlabs/rudder-sdk-js/issues/1013)) ([450cce0](https://github.com/rudderlabs/rudder-sdk-js/commit/450cce03bf09a5c3f3d93b6a6083173ddb6309d7))





# Change Log
