{"name": "@rudderstack/analytics-js-plugins", "version": "3.10.2", "private": true, "description": "RudderStack JavaScript SDK plugins", "main": "dist/npm/modern/cjs/index.cjs", "module": "dist/npm/modern/esm/index.mjs", "type": "module", "exports": {".": {"types": "./dist/npm/index.d.mts", "require": {"types": "./dist/npm/index.d.cts", "default": "./dist/npm/modern/cjs/index.cjs"}, "import": {"types": "./dist/npm/index.d.mts", "default": "./dist/npm/modern/esm/index.mjs"}}, "./legacy": {"types": "./dist/npm/index.d.mts", "require": {"types": "./dist/npm/index.d.cts", "default": "./dist/npm/legacy/cjs/index.cjs"}, "import": {"types": "./dist/npm/index.d.mts", "default": "./dist/npm/legacy/esm/index.mjs"}}}, "types": "./dist/npm/index.d.mts", "typesVersions": {"*": {"*": ["./dist/npm/index.d.mts"]}}, "publishConfig": {}, "files": ["dist/npm", "LICENSE.md", "README.md", "CHANGELOG.md"], "keywords": ["analytics", "rudder"], "author": "RudderStack", "license": "Elastic-2.0", "repository": {"type": "git", "url": "git+https://github.com/rudderlabs/rudder-sdk-js.git", "directory": "packages/analytics-js-plugins"}, "bugs": {"url": "https://github.com/rudderlabs/rudder-sdk-js/issues"}, "homepage": "https://github.com/rudderlabs/rudder-sdk-js/blob/main/packages/analytics-js-plugins/README.md", "scripts": {"clean": "rimraf -rf ./dist && rimraf -rf ./node_modules/.cache && rimraf -rf ./reports", "start": "rollup -c --watch --environment DEV_SERVER,PROD_DEBUG", "start:modern": "BROWSERSLIST_ENV=modern npm run start", "build": "npm run build:browser && npm run build:package", "build:modern": "npm run build:browser:modern && npm run build:package:modern", "build:browser": "rollup -c --environment VERSION:$npm_package_version,UGLIFY,PROD_DEBUG,ENV:prod", "build:browser:modern": "BROWSERSLIST_ENV=modern npm run build:browser", "build:npm": "exit 0", "build:npm:modern": "BROWSERSLIST_ENV=modern npm run build:npm", "build:package": "npm run build:npm", "build:package:modern": "npm run build:npm:modern", "test": "nx test --maxWorkers=50%", "test:ci": "nx test --configuration=ci --runInBand --maxWorkers=1 --forceExit", "check:lint": "nx lint", "check:lint:ci": "nx lint --configuration=ci", "check:size:build": "npm run build:browser && npm run build:browser:modern && npm run build:package && npm run build:package:modern", "check:size": "npm run check:size:build && size-limit", "check:size:json": "size-limit --json", "check:circular": "madge --circular --extensions js,ts src || exit 0", "check:support": "NODE_ENV=production npx browserslist --mobile-to-desktop", "check:support:modern": "NODE_ENV=modern npx browserslist --mobile-to-desktop", "check:duplicates": "jscpd src", "check:security": "npm audit --recursive --audit-level=high", "package": "npm pack", "release": "npm publish"}, "dependencies": {"@rudderstack/analytics-js-cookies": "*", "@rudderstack/analytics-js-common": "*", "ramda": "0.31.3"}, "devDependencies": {}, "overrides": {}, "browserslist": {"production": ["defaults", "Edge >= 80", "Firefox >= 47", "IE >= 11", "Chrome >= 54", "Safari >= 7", "Opera >= 43"], "modern": ["defaults", "supports es6-module-dynamic-import"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 edge version", "last 1 safari version"]}}