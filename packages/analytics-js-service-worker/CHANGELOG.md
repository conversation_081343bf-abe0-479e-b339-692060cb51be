# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [3.2.27](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.26...@rudderstack/analytics-js-service-worker@3.2.27) (2025-06-20)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.21.0`
## [3.2.26](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.25...@rudderstack/analytics-js-service-worker@3.2.26) (2025-06-11)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.20.0`
## [3.2.25](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.24...@rudderstack/analytics-js-service-worker@3.2.25) (2025-05-09)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.19.0`
## [3.2.24](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.23...@rudderstack/analytics-js-service-worker@3.2.24) (2025-04-25)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.18.0`
## [3.2.23](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.22...@rudderstack/analytics-js-service-worker@3.2.23) (2025-03-03)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.2`
## [3.2.22](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.21...@rudderstack/analytics-js-service-worker@3.2.22) (2025-02-20)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.1`
## [3.2.21](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.20...@rudderstack/analytics-js-service-worker@3.2.21) (2025-02-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.17.0`
## [3.2.20](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.19...@rudderstack/analytics-js-service-worker@3.2.20) (2025-01-31)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.16.0`
## [3.2.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.18...@rudderstack/analytics-js-service-worker@3.2.19) (2025-01-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.15.0`
## [3.2.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.17...@rudderstack/analytics-js-service-worker@3.2.18) (2025-01-03)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.15`

### Bug Fixes

* vulnerabilities in dependencies ([#1965](https://github.com/rudderlabs/rudder-sdk-js/issues/1965)) ([61e1e6e](https://github.com/rudderlabs/rudder-sdk-js/commit/61e1e6e272d40796f56cffe118b387f22b14f620))

## [3.2.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.16...@rudderstack/analytics-js-service-worker@3.2.17) (2024-12-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.14`
## [3.2.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.15...@rudderstack/analytics-js-service-worker@3.2.16) (2024-12-06)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.13`
## [3.2.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.14...@rudderstack/analytics-js-service-worker@3.2.15) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.12`
## [3.2.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.13...@rudderstack/analytics-js-service-worker@3.2.14) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.11`
## [3.2.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.12...@rudderstack/analytics-js-service-worker@3.2.13) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.10`
## [3.2.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.11...@rudderstack/analytics-js-service-worker@3.2.12) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.9`
## [3.2.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.10...@rudderstack/analytics-js-service-worker@3.2.11) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.8`
## [3.2.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.9...@rudderstack/analytics-js-service-worker@3.2.10) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.7`
## [3.2.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.8...@rudderstack/analytics-js-service-worker@3.2.9) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.6`
## [3.2.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.7...@rudderstack/analytics-js-service-worker@3.2.8) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.5`
## [3.2.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.6...@rudderstack/analytics-js-service-worker@3.2.7) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.4`
## [3.2.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.5...@rudderstack/analytics-js-service-worker@3.2.6) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.3`
## [3.2.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.4...@rudderstack/analytics-js-service-worker@3.2.5) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.2`
## [3.2.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.3...@rudderstack/analytics-js-service-worker@3.2.4) (2024-11-19)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.1`
## [3.2.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.2...@rudderstack/analytics-js-service-worker@3.2.3) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.14.0`
## [3.2.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.1...@rudderstack/analytics-js-service-worker@3.2.2) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.13.0`
## [3.2.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.2.0...@rudderstack/analytics-js-service-worker@3.2.1) (2024-11-12)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.12.1`
## [3.2.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.17...@rudderstack/analytics-js-service-worker@3.2.0) (2024-11-08)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.12.0`

### Features

* add date datatype ([#1906](https://github.com/rudderlabs/rudder-sdk-js/issues/1906)) ([9c207f1](https://github.com/rudderlabs/rudder-sdk-js/commit/9c207f19f34b998cdb15b34eed3f435daff86dfd))

## [3.1.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.16...@rudderstack/analytics-js-service-worker@3.1.17) (2024-11-07)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.11.1`
## [3.1.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.15...@rudderstack/analytics-js-service-worker@3.1.16) (2024-10-25)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.11.0`
## [3.1.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.14...@rudderstack/analytics-js-service-worker@3.1.15) (2024-10-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.10.0`
## [3.1.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.13...@rudderstack/analytics-js-service-worker@3.1.14) (2024-10-18)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.5`
## [3.1.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.12...@rudderstack/analytics-js-service-worker@3.1.13) (2024-10-17)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.4`
## [3.1.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.11...@rudderstack/analytics-js-service-worker@3.1.12) (2024-09-27)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.3`

### Bug Fixes

* upgrade all packages to latest to fix vulnerabilities ([#1867](https://github.com/rudderlabs/rudder-sdk-js/issues/1867)) ([389348c](https://github.com/rudderlabs/rudder-sdk-js/commit/389348cfa61f2111c5ac4f9e2bad5851a466484d))

## [3.1.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.10...@rudderstack/analytics-js-service-worker@3.1.11) (2024-09-12)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.2`
## [3.1.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.9...@rudderstack/analytics-js-service-worker@3.1.10) (2024-08-28)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.1`

### Bug Fixes

* handle blur and focus events to detect page leave ([#1837](https://github.com/rudderlabs/rudder-sdk-js/issues/1837)) ([57e735c](https://github.com/rudderlabs/rudder-sdk-js/commit/57e735ced4fb51ec895fbb196b1b879996cc10dd))

## [3.1.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.8...@rudderstack/analytics-js-service-worker@3.1.9) (2024-08-16)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.9.0`
## [3.1.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.7...@rudderstack/analytics-js-service-worker@3.1.8) (2024-08-16)


### Bug Fixes

* upgrade axios to a safe version ([#1827](https://github.com/rudderlabs/rudder-sdk-js/issues/1827)) ([bafe698](https://github.com/rudderlabs/rudder-sdk-js/commit/bafe6983c8e4db9e8055a96b8a7ab50c738b056d))

## [3.1.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.6...@rudderstack/analytics-js-service-worker@3.1.7) (2024-08-02)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.8.1`
## [3.1.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.5...@rudderstack/analytics-js-service-worker@3.1.6) (2024-07-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.1.5`
## [3.1.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.4...@rudderstack/analytics-js-service-worker@3.1.5) (2024-07-19)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.1.4`

### Bug Fixes

* event API overloads ([#1782](https://github.com/rudderlabs/rudder-sdk-js/issues/1782)) ([02c5b47](https://github.com/rudderlabs/rudder-sdk-js/commit/02c5b47d0a83250fb5180e9ed467a92361663dab))

## [3.1.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.3...@rudderstack/analytics-js-service-worker@3.1.4) (2024-07-05)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.1.3`

### Bug Fixes

* package lint issues ([#1773](https://github.com/rudderlabs/rudder-sdk-js/issues/1773)) ([8e45d05](https://github.com/rudderlabs/rudder-sdk-js/commit/8e45d052bd6366d647d06226aa89b1fa2e512f9d))

## [3.1.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.2...@rudderstack/analytics-js-service-worker@3.1.3) (2024-07-04)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.1.2`
## [3.1.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.1...@rudderstack/analytics-js-service-worker@3.1.2) (2024-06-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.1.1`

### Bug Fixes

* improve flushing events on page leave ([#1754](https://github.com/rudderlabs/rudder-sdk-js/issues/1754)) ([1be420f](https://github.com/rudderlabs/rudder-sdk-js/commit/1be420fae16b68629789d2ba37e16e6a6e00017c))

## [3.1.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.1.0...@rudderstack/analytics-js-service-worker@3.1.1) (2024-06-07)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.1.0`

### Bug Fixes

* url validation ([#1730](https://github.com/rudderlabs/rudder-sdk-js/issues/1730)) ([3a3e105](https://github.com/rudderlabs/rudder-sdk-js/commit/3a3e1057f2db91ef5cbf652a664db9443fee9843))

## [3.1.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.6...@rudderstack/analytics-js-service-worker@3.1.0) (2024-05-24)


### Features

* **analytics-js-service-worker:** auto append batch endpoint ([#1718](https://github.com/rudderlabs/rudder-sdk-js/issues/1718)) ([3e853a5](https://github.com/rudderlabs/rudder-sdk-js/commit/3e853a5c8a2f9dcab614ad6d5e6c9a54f3f61a57))


### Bug Fixes

* user sessions behavior ([#1708](https://github.com/rudderlabs/rudder-sdk-js/issues/1708)) ([84e7174](https://github.com/rudderlabs/rudder-sdk-js/commit/84e71744612c8345dc22b8cb0c9362d104eb35e9))

## [3.0.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.5...@rudderstack/analytics-js-service-worker@3.0.6) (2024-03-21)

## [3.0.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.4...@rudderstack/analytics-js-service-worker@3.0.5) (2024-03-01)


### Bug Fixes

* **analytics-js-service-worker:** update component-type to latest with TS support ([#1627](https://github.com/rudderlabs/rudder-sdk-js/issues/1627)) ([4e1d279](https://github.com/rudderlabs/rudder-sdk-js/commit/4e1d279587099aa6f56e622cd6a20cef45ec9703))

## [3.0.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.3...@rudderstack/analytics-js-service-worker@3.0.4) (2024-01-19)


### Bug Fixes

* **monorepo:** update vulnerable dependencies ([7f34e86](https://github.com/rudderlabs/rudder-sdk-js/commit/7f34e861a45526553b6cee32aef7f4e2756c9554))

## [3.0.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.2...@rudderstack/analytics-js-service-worker@3.0.3) (2024-01-08)


### Bug Fixes

* localstorage retry patch and upgrade packages ([#1573](https://github.com/rudderlabs/rudder-sdk-js/issues/1573)) ([b14d027](https://github.com/rudderlabs/rudder-sdk-js/commit/b14d0276cc7dedf87062530eab404f7a924fecf7))

## [3.0.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.1...@rudderstack/analytics-js-service-worker@3.0.2) (2023-11-13)


### Bug Fixes

* **analytics-js-service-worker:** update axios to non vulnerable version ([#1492](https://github.com/rudderlabs/rudder-sdk-js/issues/1492)) ([8ff50ac](https://github.com/rudderlabs/rudder-sdk-js/commit/8ff50acd0e2b8482c082151f61547de16d9bb494))

## [3.0.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0...@rudderstack/analytics-js-service-worker@3.0.1) (2023-10-27)


### Bug Fixes

* **monorepo:** update vulnerable dependencies ([#1457](https://github.com/rudderlabs/rudder-sdk-js/issues/1457)) ([7a4bc4c](https://github.com/rudderlabs/rudder-sdk-js/commit/7a4bc4cc641e4fff2a8f561ce6fd67d16c0cd5a0))
* upgrade vulnerable cryptoJS dependency, rolup to v4 & NX to v17 ([#1471](https://github.com/rudderlabs/rudder-sdk-js/issues/1471)) ([b2bb21c](https://github.com/rudderlabs/rudder-sdk-js/commit/b2bb21cb3f618f6c86f593d1706abe9e6349066d))

## [3.0.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.10...@rudderstack/analytics-js-service-worker@3.0.0) (2023-10-16)


### Features

* **analytics-js-service-worker:** deprecate service worker export of rudder-sdk-js package  in favor of the new standalone package([#1437](https://github.com/rudderlabs/rudder-sdk-js/issues/1437)) ([1797d3e](https://github.com/rudderlabs/rudder-sdk-js/commit/****************************************))

## [3.0.0-beta.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.9...@rudderstack/analytics-js-service-worker@3.0.0-beta.10) (2023-09-26)

## [3.0.0-beta.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.8...@rudderstack/analytics-js-service-worker@3.0.0-beta.9) (2023-09-20)

## [3.0.0-beta.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.7...@rudderstack/analytics-js-service-worker@3.0.0-beta.8) (2023-09-18)


### Features

* deprecate support of common names for integrations ([#1374](https://github.com/rudderlabs/rudder-sdk-js/issues/1374)) ([f1d097d](https://github.com/rudderlabs/rudder-sdk-js/commit/f1d097d9976f6c9d2ad0f1d81d469148f8c7c197))

## [3.0.0-beta.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.6...@rudderstack/analytics-js-service-worker@3.0.0-beta.7) (2023-09-14)


### Bug Fixes

* **analytics-js-service-worker:** stop sending requests for flush with empty batch ([8b0aa11](https://github.com/rudderlabs/rudder-sdk-js/commit/8b0aa11c4706823fc9808bf1b2b471ac80a178e8))

# [3.0.0-beta.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.5...@rudderstack/analytics-js-service-worker@3.0.0-beta.6) (2023-08-30)

**Note:** Version bump only for package @rudderstack/analytics-js-service-worker





# [3.0.0-beta.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.4...@rudderstack/analytics-js-service-worker@3.0.0-beta.5) (2023-08-21)

**Note:** Version bump only for package @rudderstack/analytics-js-service-worker





# [3.0.0-beta.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.3...@rudderstack/analytics-js-service-worker@3.0.0-beta.4) (2023-08-17)

**Note:** Version bump only for package @rudderstack/analytics-js-service-worker





# [3.0.0-beta.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js-service-worker@3.0.0-beta.2...@rudderstack/analytics-js-service-worker@3.0.0-beta.3) (2023-08-10)

**Note:** Version bump only for package @rudderstack/analytics-js-service-worker





# 3.0.0-beta.2 (2023-08-09)
