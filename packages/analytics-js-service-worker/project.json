{"name": "@rudderstack/analytics-js-service-worker", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/analytics-js-service-worker/src", "projectType": "library", "tags": ["type:sdk", "scope:service-worker"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/analytics-js-service-worker/jest.config.mjs", "passWithNoTests": true, "codeCoverage": true, "watchAll": false, "forceExit": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"hasTypeAwareRules": true, "lintFilePatterns": ["packages/analytics-js-service-worker/src/**/*.{ts,js}", "packages/analytics-js-service-worker/{package,project}.json"]}, "configurations": {"ci": {"force": true, "outputFile": "packages/analytics-js-service-worker/reports/eslint.json", "format": "json"}}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventionalcommits", "tagPrefix": "{projectName}@", "trackDeps": true}}, "github": {"executor": "@jscutlery/semver:github", "options": {"tag": "@rudderstack/analytics-js-service-worker@3.2.27", "title": "rudderstack/analytics-js-service-worker@3.2.27", "discussion-category": "rudderstack/analytics-js-service-worker@3.2.27", "notesFile": "./packages/analytics-js-service-worker/CHANGELOG_LATEST.md"}}}}