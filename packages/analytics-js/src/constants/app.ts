const APP_NAME = 'RudderLabs JavaScript SDK';
const APP_VERSION = '__PACKAGE_VERSION__';
const APP_NAMESPACE = 'com.rudderlabs.javascript';
const MODULE_TYPE = '__MODULE_TYPE__';
const IS_LEGACY_BUILD = __IS_LEGACY_BUILD__;
const ADBLOCK_PAGE_CATEGORY = 'RudderJS-Initiated';
const ADBLOCK_PAGE_NAME = 'ad-block page request';
const ADBLOCK_PAGE_PATH = '/ad-blocked';
const GLOBAL_PRELOAD_BUFFER = 'preloadedEventsBuffer';

const CONSENT_TRACK_EVENT_NAME = 'Consent Management Interaction';

export {
  APP_NAME,
  APP_VERSION,
  APP_NAMESPACE,
  MODULE_TYPE,
  IS_LEGACY_BUILD,
  ADBLOCK_PAGE_CATEGORY,
  ADBLOCK_PAGE_NAME,
  ADBLOCK_PAGE_PATH,
  GLOBAL_PRELOAD_BUFFER,
  CONSENT_TRACK_EVENT_NAME,
};
