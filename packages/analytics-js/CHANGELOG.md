# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [3.21.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.20.1...@rudderstack/analytics-js@3.21.0) (2025-06-20)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.27`
* `@rudderstack/analytics-js-common` updated to version `3.21.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.10.2`

### Features

* **analytics-js:** reduce error noise from CSP/adblocker ([#2296](https://github.com/rudderlabs/rudder-sdk-js/issues/2296)) ([c187816](https://github.com/rudderlabs/rudder-sdk-js/commit/c187816b2cfafb706670824001140e464e3d90d8))

## [3.20.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.20.0...@rudderstack/analytics-js@3.20.1) (2025-06-12)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.10.1`
## [3.20.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.19.0...@rudderstack/analytics-js@3.20.0) (2025-06-11)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.26`
* `@rudderstack/analytics-js-common` updated to version `3.20.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.10.0`

### Features

* add support to dynamically override destinations status ([#2266](https://github.com/rudderlabs/rudder-sdk-js/issues/2266)) ([5af2f22](https://github.com/rudderlabs/rudder-sdk-js/commit/5af2f22ebdcac4eb04d57ecb51efa427607bc849))
* dynamically clone destinations ([#2276](https://github.com/rudderlabs/rudder-sdk-js/issues/2276)) ([f136454](https://github.com/rudderlabs/rudder-sdk-js/commit/f1364541743b15d240ceed6d8f403c23b6984086))
* enable destination config override ([#2267](https://github.com/rudderlabs/rudder-sdk-js/issues/2267)) ([c570106](https://github.com/rudderlabs/rudder-sdk-js/commit/c5701065133d3fbddcff9072950ce935ef69e38a))
* enhance retry headers with RSA-prefixed naming ([#2279](https://github.com/rudderlabs/rudder-sdk-js/issues/2279)) ([c25b2bc](https://github.com/rudderlabs/rudder-sdk-js/commit/c25b2bc5bb4b5b41469065138eef88c2fa21a460))
* set proper grouping hash for all errors ([#2246](https://github.com/rudderlabs/rudder-sdk-js/issues/2246)) ([430c497](https://github.com/rudderlabs/rudder-sdk-js/commit/430c49782b95bf3e8de1f6a62b442b363208a66b))

## [3.19.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.18.2...@rudderstack/analytics-js@3.19.0) (2025-06-03)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.9.0`

### Features

* retry delivery failures in beacon plugin when page is unloaded ([#2269](https://github.com/rudderlabs/rudder-sdk-js/issues/2269)) ([cec81f3](https://github.com/rudderlabs/rudder-sdk-js/commit/cec81f3d2aca443f6d2c209941dd28fffd65888c))

## [3.18.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.18.1...@rudderstack/analytics-js@3.18.2) (2025-05-26)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.8.4`

### Bug Fixes

* store events in in-progress queue even when the page is unloaded ([#2245](https://github.com/rudderlabs/rudder-sdk-js/issues/2245)) ([8f978b5](https://github.com/rudderlabs/rudder-sdk-js/commit/8f978b5d58c63747dda04df75ed05a2709bec11a))

## [3.18.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.18.0...@rudderstack/analytics-js@3.18.1) (2025-05-14)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.8.3`

### Bug Fixes

* store events in in-progress queue even when the page is unloaded ([#2245](https://github.com/rudderlabs/rudder-sdk-js/issues/2245)) ([843db54](https://github.com/rudderlabs/rudder-sdk-js/commit/843db54ac126b7cd7c0d22a46ee40f4b5d7cd6b4))

## [3.18.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.17.0...@rudderstack/analytics-js@3.18.0) (2025-05-09)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.25`
* `@rudderstack/analytics-js-common` updated to version `3.19.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.8.2`

### Features

* group errors by message ([#2229](https://github.com/rudderlabs/rudder-sdk-js/issues/2229)) ([b448874](https://github.com/rudderlabs/rudder-sdk-js/commit/b448874fc39972576ebaf4d30f0bbd4883f69b7e))
* user session cut off ([#2209](https://github.com/rudderlabs/rudder-sdk-js/issues/2209)) ([8b7bcfd](https://github.com/rudderlabs/rudder-sdk-js/commit/8b7bcfd70155beb6f162a3b8ceec5735b67cce10))


### Bug Fixes

* load api options boolean inputs normalization ([#2236](https://github.com/rudderlabs/rudder-sdk-js/issues/2236)) ([4c3532c](https://github.com/rudderlabs/rudder-sdk-js/commit/4c3532c9b9e34903c2f975d95cfa516324bbee04))

## [3.17.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.16.1...@rudderstack/analytics-js@3.17.0) (2025-04-25)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.24`
* `@rudderstack/analytics-js-common` updated to version `3.18.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.8.1`

### Features

* pre-consent event buffering works now for anonymousId pre-consent storage strategy ([#2100](https://github.com/rudderlabs/rudder-sdk-js/issues/2100)) ([6336925](https://github.com/rudderlabs/rudder-sdk-js/commit/6336925ccfdf66623ca98c4a44b7bf0a13ef54dc))
* remove page loaded event ([#2088](https://github.com/rudderlabs/rudder-sdk-js/issues/2088)) ([ec1d604](https://github.com/rudderlabs/rudder-sdk-js/commit/ec1d604f70d4e476a751f5207df09eef69220be2))


### Bug Fixes

* consent api race condition to load integrations ([#2178](https://github.com/rudderlabs/rudder-sdk-js/issues/2178)) ([30149ad](https://github.com/rudderlabs/rudder-sdk-js/commit/30149adff3eddd628022f511374e9072d087db89))
* consider local page urls as dev release stage in error reporting ([#2174](https://github.com/rudderlabs/rudder-sdk-js/issues/2174)) ([ae53449](https://github.com/rudderlabs/rudder-sdk-js/commit/ae53449af2289113182c602971340afdad39d13d))
* recursively migrate persisted entries ([#2187](https://github.com/rudderlabs/rudder-sdk-js/issues/2187)) ([3dd07ea](https://github.com/rudderlabs/rudder-sdk-js/commit/3dd07ea1bde4655124fc02850a022bcb550b8c07))
* rename view id to visit id ([#2086](https://github.com/rudderlabs/rudder-sdk-js/issues/2086)) ([51c8dd9](https://github.com/rudderlabs/rudder-sdk-js/commit/51c8dd94b2e25f42a116cb72d209d41729c165c0))
* rename visit duration to time on page ([#2087](https://github.com/rudderlabs/rudder-sdk-js/issues/2087)) ([569846d](https://github.com/rudderlabs/rudder-sdk-js/commit/569846d992fd01105e880e67ca004d1e9f52688a))

## [3.16.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.16.0...@rudderstack/analytics-js@3.16.1) (2025-03-03)


### Bug Fixes

* prevent cookie storage engine options init by default ([#2077](https://github.com/rudderlabs/rudder-sdk-js/issues/2077)) ([cf0e4d9](https://github.com/rudderlabs/rudder-sdk-js/commit/cf0e4d91a7174023f0027b042013de6ecaf6fcaf))

## [3.16.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.15.2...@rudderstack/analytics-js@3.16.0) (2025-03-03)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.23`
* `@rudderstack/analytics-js-common` updated to version `3.17.2`
* `@rudderstack/analytics-js-plugins` updated to version `3.8.0`

### Features

* add events processing and retry headers ([#2066](https://github.com/rudderlabs/rudder-sdk-js/issues/2066)) ([3113030](https://github.com/rudderlabs/rudder-sdk-js/commit/311303098d053fd0ce87d9bf8393a800112958ae))


### Bug Fixes

* avoid premature execution before sdk initialization ([#2056](https://github.com/rudderlabs/rudder-sdk-js/issues/2056)) ([9c7e2a6](https://github.com/rudderlabs/rudder-sdk-js/commit/9c7e2a6011e34ec2cd20925c1f8d79427297263a))
* handle edge cases in retry queue ([#2074](https://github.com/rudderlabs/rudder-sdk-js/issues/2074)) ([f9263b2](https://github.com/rudderlabs/rudder-sdk-js/commit/f9263b24170680023dfa1687c778b97557ef5e1b))

## [3.15.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.15.1...@rudderstack/analytics-js@3.15.2) (2025-02-24)


### Bug Fixes

* allow handled errors to be processed ([#2059](https://github.com/rudderlabs/rudder-sdk-js/issues/2059)) ([578f8cd](https://github.com/rudderlabs/rudder-sdk-js/commit/578f8cdce0dc6ff6d5a9bd76ea73ad67ba99aacb))

## [3.15.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.15.0...@rudderstack/analytics-js@3.15.1) (2025-02-20)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.22`
* `@rudderstack/analytics-js-common` updated to version `3.17.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.7.2`

### Bug Fixes

* consent api to refresh consent data every time ([#2052](https://github.com/rudderlabs/rudder-sdk-js/issues/2052)) ([0cf0f9a](https://github.com/rudderlabs/rudder-sdk-js/commit/0cf0f9adb7d6fab71a32bf6fd331faeea6278bba))
* retry status code logic and error messages ([#2050](https://github.com/rudderlabs/rudder-sdk-js/issues/2050)) ([28fd410](https://github.com/rudderlabs/rudder-sdk-js/commit/28fd410f90fe2c0e5c9071d7151ac2e297340573))
* sdk loading snippet to reduce ambiguity in updating url ([#2048](https://github.com/rudderlabs/rudder-sdk-js/issues/2048)) ([843d944](https://github.com/rudderlabs/rudder-sdk-js/commit/843d944f2d63ee414cbcf9d7c991ba97567cdac3))

## [3.15.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.14.0...@rudderstack/analytics-js@3.15.0) (2025-02-17)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.21`
* `@rudderstack/analytics-js-common` updated to version `3.17.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.7.1`

### Features

* avoid reporting non-actionable errors ([#2041](https://github.com/rudderlabs/rudder-sdk-js/issues/2041)) ([60345fb](https://github.com/rudderlabs/rudder-sdk-js/commit/60345fb604109e509f9cd4eb45f76ebd3c756fc2))

## [3.14.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.13.0...@rudderstack/analytics-js@3.14.0) (2025-01-31)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.20`
* `@rudderstack/analytics-js-common` updated to version `3.16.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.7.0`

### Features

* move error reporting functionality to the core module ([#2011](https://github.com/rudderlabs/rudder-sdk-js/issues/2011)) ([78c50c7](https://github.com/rudderlabs/rudder-sdk-js/commit/78c50c7a6e4169560f3182be93148f4512d313ca)), closes [#2001](https://github.com/rudderlabs/rudder-sdk-js/issues/2001) [#2002](https://github.com/rudderlabs/rudder-sdk-js/issues/2002) [#2005](https://github.com/rudderlabs/rudder-sdk-js/issues/2005) [#2006](https://github.com/rudderlabs/rudder-sdk-js/issues/2006) [#2007](https://github.com/rudderlabs/rudder-sdk-js/issues/2007)

## [3.13.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.12.0...@rudderstack/analytics-js@3.13.0) (2025-01-28)


### Features

* lock plugins and integrations versions for npm package ([#2020](https://github.com/rudderlabs/rudder-sdk-js/issues/2020)) ([447e524](https://github.com/rudderlabs/rudder-sdk-js/commit/447e524dd1ee5782e6d7a8e834c03c57ebf3c196))
  * Plugins and integration SDKs will now be loaded from a versioned directory (`/3.x.y/`) on the CDN instead of the common `/v3/` path.
  * This ensures that SDK updates do not unexpectedly break existing integrations due to changes in dependencies.
  * If you are using a CDN proxy, ensure that it does not block requests to versioned paths like `/3.13.0/`, otherwise the SDK will not load correctly.

## [3.12.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.17...@rudderstack/analytics-js@3.12.0) (2025-01-24)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.19`
* `@rudderstack/analytics-js-common` updated to version `3.15.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.22`

### Features

* lock plugins and integrations version by default ([#1956](https://github.com/rudderlabs/rudder-sdk-js/issues/1956)) ([45e716e](https://github.com/rudderlabs/rudder-sdk-js/commit/45e716e6df3d6e665c25aa907531adb746961d50))
  * Plugins and integration SDKs will now be loaded from a versioned directory (`/3.x.y/`) on the CDN instead of the common `/v3/` path.
  * This ensures that SDK updates do not unexpectedly break existing integrations due to changes in dependencies.
  * If you are using a CDN proxy, ensure that it does not block requests to versioned paths like `/3.13.0/`, otherwise the SDK will not load correctly.

## [3.11.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.16...@rudderstack/analytics-js@3.11.17) (2025-01-03)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.18`
* `@rudderstack/analytics-js-common` updated to version `3.14.15`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.21`
## [3.11.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.15...@rudderstack/analytics-js@3.11.16) (2024-12-17)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.17`
* `@rudderstack/analytics-js-common` updated to version `3.14.14`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.20`

### Bug Fixes

* remove circular dependency in packages ([#1973](https://github.com/rudderlabs/rudder-sdk-js/issues/1973)) ([e525496](https://github.com/rudderlabs/rudder-sdk-js/commit/e5254964310c2c73baaf4d0655c3e4025c5e7d2b))
* separator and make changes in bugsnag plugin ([b69347c](https://github.com/rudderlabs/rudder-sdk-js/commit/b69347cd9bbf3a395b2f557f8219287900ceca5a))

## [3.11.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.14...@rudderstack/analytics-js@3.11.15) (2024-12-06)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.16`
* `@rudderstack/analytics-js-common` updated to version `3.14.13`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.18`
## [3.11.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.13...@rudderstack/analytics-js@3.11.14) (2024-11-30)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.6.17`

### Bug Fixes

* preload events not processed with detached load call ([#1953](https://github.com/rudderlabs/rudder-sdk-js/issues/1953)) ([6b0f66f](https://github.com/rudderlabs/rudder-sdk-js/commit/6b0f66f61745542f2b01c02c99b7514fd468db80))

## [3.11.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.12...@rudderstack/analytics-js@3.11.13) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.15`
* `@rudderstack/analytics-js-common` updated to version `3.14.12`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.16`
## [3.11.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.11...@rudderstack/analytics-js@3.11.12) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.14`
* `@rudderstack/analytics-js-common` updated to version `3.14.11`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.15`
## [3.11.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.10...@rudderstack/analytics-js@3.11.11) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.13`
* `@rudderstack/analytics-js-common` updated to version `3.14.10`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.14`

### Bug Fixes

* restore data sanitization changes but avoid using api overloads ([d0913ae](https://github.com/rudderlabs/rudder-sdk-js/commit/d0913ae32a8c63def26c081c7570a9960dcd1ebf))

## [3.11.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.9...@rudderstack/analytics-js@3.11.10) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.12`
* `@rudderstack/analytics-js-common` updated to version `3.14.9`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.13`
## [3.11.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.8...@rudderstack/analytics-js@3.11.9) (2024-11-22)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.11`
* `@rudderstack/analytics-js-common` updated to version `3.14.8`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.12`
## [3.11.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.7...@rudderstack/analytics-js@3.11.8) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.10`
* `@rudderstack/analytics-js-common` updated to version `3.14.7`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.11`
## [3.11.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.6...@rudderstack/analytics-js@3.11.7) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.9`
* `@rudderstack/analytics-js-common` updated to version `3.14.6`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.10`

### Bug Fixes

* utility import ([d86f545](https://github.com/rudderlabs/rudder-sdk-js/commit/d86f54536c5f4cb206c2fd676d3b49dd29e62bcf))

## [3.11.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.5...@rudderstack/analytics-js@3.11.6) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.8`
* `@rudderstack/analytics-js-common` updated to version `3.14.5`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.9`
## [3.11.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.4...@rudderstack/analytics-js@3.11.5) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.7`
* `@rudderstack/analytics-js-common` updated to version `3.14.4`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.8`
## [3.11.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.3...@rudderstack/analytics-js@3.11.4) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.6`
* `@rudderstack/analytics-js-common` updated to version `3.14.3`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.7`
## [3.11.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.2...@rudderstack/analytics-js@3.11.3) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.5`
* `@rudderstack/analytics-js-common` updated to version `3.14.2`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.6`
## [3.11.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.1...@rudderstack/analytics-js@3.11.2) (2024-11-21)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.6.4`
## [3.11.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.11.0...@rudderstack/analytics-js@3.11.1) (2024-11-19)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.4`
* `@rudderstack/analytics-js-common` updated to version `3.14.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.4`
## [3.11.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.10.2...@rudderstack/analytics-js@3.11.0) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.3`
* `@rudderstack/analytics-js-common` updated to version `3.14.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.3`

### Features

* error handle public apis ([295793a](https://github.com/rudderlabs/rudder-sdk-js/commit/295793a2cc60172b001c3fb1bc2624bb19fa8546))

## [3.10.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.10.1...@rudderstack/analytics-js@3.10.2) (2024-11-18)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.2`
* `@rudderstack/analytics-js-common` updated to version `3.13.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.2`
## [3.10.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.10.0...@rudderstack/analytics-js@3.10.1) (2024-11-12)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.1`
* `@rudderstack/analytics-js-common` updated to version `3.12.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.1`

### Bug Fixes

* batch entry retry ([#1918](https://github.com/rudderlabs/rudder-sdk-js/issues/1918)) ([ff346b8](https://github.com/rudderlabs/rudder-sdk-js/commit/ff346b867335750d7b428cab9c650a4d9dbfde57))
* revert sanitization changes ([#1916](https://github.com/rudderlabs/rudder-sdk-js/issues/1916)) ([890fb7b](https://github.com/rudderlabs/rudder-sdk-js/commit/890fb7b615535992290f5008b93d77b540c03955)), closes [#1907](https://github.com/rudderlabs/rudder-sdk-js/issues/1907) [#1902](https://github.com/rudderlabs/rudder-sdk-js/issues/1902)

## [3.10.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.9.1...@rudderstack/analytics-js@3.10.0) (2024-11-08)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.4.0`
* `@rudderstack/analytics-js-common` updated to version `3.12.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.0`

### Features

* add error handling to all public apis ([#1907](https://github.com/rudderlabs/rudder-sdk-js/issues/1907)) ([9fbaf81](https://github.com/rudderlabs/rudder-sdk-js/commit/9fbaf819bb02320d2f8ae82a869ad2b85090ea34))
* sanitize input data ([#1902](https://github.com/rudderlabs/rudder-sdk-js/issues/1902)) ([b71c44a](https://github.com/rudderlabs/rudder-sdk-js/commit/b71c44ae61f6c35cadc6523b918e1a574e32bc23))

## [3.9.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.9.0...@rudderstack/analytics-js@3.9.1) (2024-11-07)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.14`
* `@rudderstack/analytics-js-common` updated to version `3.11.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.5.2`

### Bug Fixes

* move page visit id to context ([#1904](https://github.com/rudderlabs/rudder-sdk-js/issues/1904)) ([76bbd16](https://github.com/rudderlabs/rudder-sdk-js/commit/76bbd16bd764baa00df2995fa9fb287800fd68d3))

## [3.9.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.8.0...@rudderstack/analytics-js@3.9.0) (2024-10-25)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.13`
* `@rudderstack/analytics-js-common` updated to version `3.11.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.5.1`

### Features

* gainsight PX destination ([#1852](https://github.com/rudderlabs/rudder-sdk-js/issues/1852)) ([#1889](https://github.com/rudderlabs/rudder-sdk-js/issues/1889)) ([3a705f0](https://github.com/rudderlabs/rudder-sdk-js/commit/3a705f063bcae99c7964495ff83ad9ce8d4eb5a3))
* track time spent on a page ([#1876](https://github.com/rudderlabs/rudder-sdk-js/issues/1876)) ([5590af7](https://github.com/rudderlabs/rudder-sdk-js/commit/5590af712dd951ce9182c06d8042794c4fe6df2f))

## [3.8.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.19...@rudderstack/analytics-js@3.8.0) (2024-10-21)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.12`
* `@rudderstack/analytics-js-common` updated to version `3.10.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.5.0`

### Features

* iubenda consent manager plugin ([#1809](https://github.com/rudderlabs/rudder-sdk-js/issues/1809)) ([7ea300c](https://github.com/rudderlabs/rudder-sdk-js/commit/7ea300c61ead9cc094c3f1985e0ef3165b0fcb59))

## [3.7.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.18...@rudderstack/analytics-js@3.7.19) (2024-10-18)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.11`
* `@rudderstack/analytics-js-common` updated to version `3.9.5`
* `@rudderstack/analytics-js-plugins` updated to version `3.4.19`

### Bug Fixes

* harmless change in the core sdk package ([5c34b04](https://github.com/rudderlabs/rudder-sdk-js/commit/5c34b0453041a0dc1b8a7dc55eb0c884d16d0598))

## [3.7.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.17...@rudderstack/analytics-js@3.7.18) (2024-10-17)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.10`
* `@rudderstack/analytics-js-common` updated to version `3.9.4`
* `@rudderstack/analytics-js-plugins` updated to version `3.4.18`
## [3.7.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.16...@rudderstack/analytics-js@3.7.17) (2024-10-11)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.4.16`

### Bug Fixes

* global integrations object is not used for preloaded events ([#1881](https://github.com/rudderlabs/rudder-sdk-js/issues/1881)) ([2776f07](https://github.com/rudderlabs/rudder-sdk-js/commit/2776f07e0b0142e05bd4bd3dc053c484c8ecf8a0))

## [3.7.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.15...@rudderstack/analytics-js@3.7.16) (2024-10-10)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.4.15`

### Bug Fixes

* source config fixture and local storage issue ([cbf0833](https://github.com/rudderlabs/rudder-sdk-js/commit/cbf08337f2a4dfdb0566ef60ef0dcf4c53ce8e00))

## [3.7.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.14...@rudderstack/analytics-js@3.7.15) (2024-10-03)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.4.15`

### Bug Fixes

* storage access error when cookies are blocked ([#1872](https://github.com/rudderlabs/rudder-sdk-js/issues/1872)) ([966dbc2](https://github.com/rudderlabs/rudder-sdk-js/commit/966dbc2996b0ee292a553058ef435154de73f42e))

## [3.7.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.13...@rudderstack/analytics-js@3.7.14) (2024-09-27)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.9`
* `@rudderstack/analytics-js-common` updated to version `3.9.3`
* `@rudderstack/analytics-js-plugins` updated to version `3.4.14`

### Bug Fixes

* add explicit checking for setting user and group traits ([68b4293](https://github.com/rudderlabs/rudder-sdk-js/commit/68b4293a9817b3a113af5f4ad8bfcfcf1cc81e88))
* auto session tracking ([#1856](https://github.com/rudderlabs/rudder-sdk-js/issues/1856)) ([fa7cce4](https://github.com/rudderlabs/rudder-sdk-js/commit/fa7cce494a4dce00f843ce5790ceccb0fa9ce95f))
* upgrade all packages to latest to fix vulnerabilities ([#1867](https://github.com/rudderlabs/rudder-sdk-js/issues/1867)) ([389348c](https://github.com/rudderlabs/rudder-sdk-js/commit/389348cfa61f2111c5ac4f9e2bad5851a466484d))

## [3.7.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.12...@rudderstack/analytics-js@3.7.13) (2024-09-17)


### Bug Fixes

* sdk loading snippet and sanity suite ([#1853](https://github.com/rudderlabs/rudder-sdk-js/issues/1853)) ([d531f14](https://github.com/rudderlabs/rudder-sdk-js/commit/d531f142f9f9f17f3f675962835531d02b687844))

## [3.7.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.11...@rudderstack/analytics-js@3.7.12) (2024-09-12)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.8`
* `@rudderstack/analytics-js-common` updated to version `3.9.2`
* `@rudderstack/analytics-js-plugins` updated to version `3.4.12`

### Bug Fixes

* gracefully handle cross sdk version cookies and warn ([#1847](https://github.com/rudderlabs/rudder-sdk-js/issues/1847)) ([408a838](https://github.com/rudderlabs/rudder-sdk-js/commit/408a8389be845883c35045fdb61695db5414ad21))

## [3.7.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.10...@rudderstack/analytics-js@3.7.11) (2024-08-30)


### Bug Fixes

* explicitly set XHR to async mode ([#1834](https://github.com/rudderlabs/rudder-sdk-js/issues/1834)) ([06f6a71](https://github.com/rudderlabs/rudder-sdk-js/commit/06f6a715c4aec9fb60a9cd04b3c8cf1b7b0c7216))

## [3.7.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.9...@rudderstack/analytics-js@3.7.10) (2024-08-28)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.7`
* `@rudderstack/analytics-js-common` updated to version `3.9.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.4.10`

### Bug Fixes

* handle blur and focus events to detect page leave ([#1837](https://github.com/rudderlabs/rudder-sdk-js/issues/1837)) ([57e735c](https://github.com/rudderlabs/rudder-sdk-js/commit/57e735ced4fb51ec895fbb196b1b879996cc10dd))

## [3.7.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.8...@rudderstack/analytics-js@3.7.9) (2024-08-16)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.6`
* `@rudderstack/analytics-js-common` updated to version `3.9.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.4.9`
## [3.7.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.7...@rudderstack/analytics-js@3.7.8) (2024-08-16)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.4.8`
## [3.7.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.6...@rudderstack/analytics-js@3.7.7) (2024-08-02)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `0.3.5`
* `@rudderstack/analytics-js-common` updated to version `3.8.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.4.7`

### Bug Fixes

* error filtering of non-errors ([#1811](https://github.com/rudderlabs/rudder-sdk-js/issues/1811)) ([7b83e16](https://github.com/rudderlabs/rudder-sdk-js/commit/7b83e1661b1e0ce0b6b5ae45d3a2e08db97ddcb3))

## [3.7.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.5...@rudderstack/analytics-js@3.7.6) (2024-07-24)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `3.7.5`
* `@rudderstack/analytics-js-common` updated to version `3.7.5`
* `@rudderstack/analytics-js-plugins` updated to version `3.7.5`
## [3.7.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.4...@rudderstack/analytics-js@3.7.5) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.7.4`

### Bug Fixes

* disable error reporting ([#1804](https://github.com/rudderlabs/rudder-sdk-js/issues/1804)) ([e61a298](https://github.com/rudderlabs/rudder-sdk-js/commit/e61a298675fcb852b9e5e64082f88d9ded4c248b))

## [3.7.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.3...@rudderstack/analytics-js@3.7.4) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.7.3`

### Bug Fixes

* filter non errors ([#1800](https://github.com/rudderlabs/rudder-sdk-js/issues/1800)) ([73de1cc](https://github.com/rudderlabs/rudder-sdk-js/commit/73de1ccc264e9afcbae3e6d7f66e120aca81e49a))

## [3.7.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.2...@rudderstack/analytics-js@3.7.3) (2024-07-23)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.7.2`

### Bug Fixes

* filter unhandled errors by message ([#1797](https://github.com/rudderlabs/rudder-sdk-js/issues/1797)) ([2f45d00](https://github.com/rudderlabs/rudder-sdk-js/commit/2f45d001e7d9dbb70b12b017fc73bc740135e870))

## [3.7.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.1...@rudderstack/analytics-js@3.7.2) (2024-07-22)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.7.1`

### Bug Fixes

* exclude capturing promise rejection error ([#1794](https://github.com/rudderlabs/rudder-sdk-js/issues/1794)) ([c006035](https://github.com/rudderlabs/rudder-sdk-js/commit/c00603528a03974b1b24775a3cda68d18e5c8f49))

## [3.7.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.7.0...@rudderstack/analytics-js@3.7.1) (2024-07-22)


### Bug Fixes

* prevent default rejection handling ([#1791](https://github.com/rudderlabs/rudder-sdk-js/issues/1791)) ([025d4f6](https://github.com/rudderlabs/rudder-sdk-js/commit/025d4f606b3b04d88c8bb2710fd2e567bf639d22))

## [3.7.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.6.1...@rudderstack/analytics-js@3.7.0) (2024-07-19)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `3.6.1`
* `@rudderstack/analytics-js-common` updated to version `3.6.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.1`

### Features

* error reporting plugin ([#1601](https://github.com/rudderlabs/rudder-sdk-js/issues/1601)) ([1f2629e](https://github.com/rudderlabs/rudder-sdk-js/commit/1f2629e594740763ce9bd54a21213b92d80ae085))


### Bug Fixes

* event API overloads ([#1782](https://github.com/rudderlabs/rudder-sdk-js/issues/1782)) ([02c5b47](https://github.com/rudderlabs/rudder-sdk-js/commit/02c5b47d0a83250fb5180e9ed467a92361663dab))

## [3.6.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.6.0...@rudderstack/analytics-js@3.6.1) (2024-07-05)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `3.6.0`
* `@rudderstack/analytics-js-common` updated to version `3.6.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.6.0`

### Bug Fixes

* package lint issues ([#1773](https://github.com/rudderlabs/rudder-sdk-js/issues/1773)) ([8e45d05](https://github.com/rudderlabs/rudder-sdk-js/commit/8e45d052bd6366d647d06226aa89b1fa2e512f9d))

## [3.6.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.5.2...@rudderstack/analytics-js@3.6.0) (2024-07-04)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `3.5.2`
* `@rudderstack/analytics-js-common` updated to version `3.5.2`
* `@rudderstack/analytics-js-plugins` updated to version `3.5.2`

### Features

* update itp implementation based on load options ([#1777](https://github.com/rudderlabs/rudder-sdk-js/issues/1777)) ([75aa117](https://github.com/rudderlabs/rudder-sdk-js/commit/75aa117911b1811b21576c95d2692d7f8580176c))

## [3.5.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.5.1...@rudderstack/analytics-js@3.5.2) (2024-07-01)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `3.5.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.5.1`
## [3.5.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.5.0...@rudderstack/analytics-js@3.5.1) (2024-06-25)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `3.5.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.5.0`
## [3.5.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.4.0...@rudderstack/analytics-js@3.5.0) (2024-06-25)

### Dependency Updates

* `@rudderstack/analytics-js-cookies` updated to version `3.4.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.4.0`

### Features

* create new package for cookie utilities ([#1759](https://github.com/rudderlabs/rudder-sdk-js/issues/1759)) ([92826a4](https://github.com/rudderlabs/rudder-sdk-js/commit/92826a4f2e87dd2d0d755016592e36e4708f34d4))

## [3.4.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.3.0...@rudderstack/analytics-js@3.4.0) (2024-06-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.3.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.3.0`

### Features

* add the ability to lock plugins version ([#1749](https://github.com/rudderlabs/rudder-sdk-js/issues/1749)) ([e2e1620](https://github.com/rudderlabs/rudder-sdk-js/commit/e2e1620677c90169fca35ed3e9057ced3b88a299))


### Bug Fixes

* add state metadata even for unhandled errors ([#1755](https://github.com/rudderlabs/rudder-sdk-js/issues/1755)) ([66fc415](https://github.com/rudderlabs/rudder-sdk-js/commit/66fc415474ffccd684972b47f9926ab87c0a514c))
* debounce cookie requests to server ([#1752](https://github.com/rudderlabs/rudder-sdk-js/issues/1752)) ([8b25cbe](https://github.com/rudderlabs/rudder-sdk-js/commit/8b25cbea43274f71825986c0ce78919358ce5b15))
* improve flushing events on page leave ([#1754](https://github.com/rudderlabs/rudder-sdk-js/issues/1754)) ([1be420f](https://github.com/rudderlabs/rudder-sdk-js/commit/1be420fae16b68629789d2ba37e16e6a6e00017c))
* remove data residency feature ([#1748](https://github.com/rudderlabs/rudder-sdk-js/issues/1748)) ([870a7ec](https://github.com/rudderlabs/rudder-sdk-js/commit/870a7ecf3cd251d88c207d9815c2f16c6e9a6883))

## [3.3.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.2.2...@rudderstack/analytics-js@3.3.0) (2024-06-07)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.2.2`
* `@rudderstack/analytics-js-plugins` updated to version `3.2.2`

### Features

* add install type to context ([#1740](https://github.com/rudderlabs/rudder-sdk-js/issues/1740)) ([3d25b65](https://github.com/rudderlabs/rudder-sdk-js/commit/3d25b654a70b0f39c412e80465e29e2bdb578aa7))
* handle remove cookie from client side ([#1739](https://github.com/rudderlabs/rudder-sdk-js/issues/1739)) ([8b19932](https://github.com/rudderlabs/rudder-sdk-js/commit/8b19932a702e1319cce6fa09961d109f0a83f71c))


### Bug Fixes

* abort initialization if source is disabled ([#1737](https://github.com/rudderlabs/rudder-sdk-js/issues/1737)) ([682b748](https://github.com/rudderlabs/rudder-sdk-js/commit/682b74873fa9d57ca5033e73a1d69f50b846523e))
* cookie domain determination ([#1735](https://github.com/rudderlabs/rudder-sdk-js/issues/1735)) ([dbeb064](https://github.com/rudderlabs/rudder-sdk-js/commit/dbeb0649f5632409b6a8e8c8bb19260c85d09224))
* cookie value comparison ([#1736](https://github.com/rudderlabs/rudder-sdk-js/issues/1736)) ([1ffb763](https://github.com/rudderlabs/rudder-sdk-js/commit/1ffb7638af1e4b69ae87121ac7f29f9ea489558f))
* handle cross domain server-side cookie requests ([#1741](https://github.com/rudderlabs/rudder-sdk-js/issues/1741)) ([68a2d3b](https://github.com/rudderlabs/rudder-sdk-js/commit/68a2d3b025a45311cc3639b140d33a9659e93e8f))
* improve sdk loading snippet ([#1745](https://github.com/rudderlabs/rudder-sdk-js/issues/1745)) ([d4e0f66](https://github.com/rudderlabs/rudder-sdk-js/commit/d4e0f663a4d0cdb55563ed380166d55e99cf3fc8))
* url validation ([#1730](https://github.com/rudderlabs/rudder-sdk-js/issues/1730)) ([3a3e105](https://github.com/rudderlabs/rudder-sdk-js/commit/3a3e1057f2db91ef5cbf652a664db9443fee9843))

## [3.2.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.2.1...@rudderstack/analytics-js@3.2.2) (2024-06-04)


### Bug Fixes

* sync issues between state and storage for session info ([#1742](https://github.com/rudderlabs/rudder-sdk-js/issues/1742)) ([3eddf30](https://github.com/rudderlabs/rudder-sdk-js/commit/3eddf3003277a69532cc336ce3cf74240097f52a))

## [3.2.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.2.0...@rudderstack/analytics-js@3.2.1) (2024-05-29)


### Bug Fixes

* sdk base url deduction ([#1732](https://github.com/rudderlabs/rudder-sdk-js/issues/1732)) ([546c552](https://github.com/rudderlabs/rudder-sdk-js/commit/546c5529928aa0900c998e33877c2434da6e727d))

## [3.2.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.1.1...@rudderstack/analytics-js@3.2.0) (2024-05-24)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.1.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.1.1`

### Features

* set server side cookies ([#1649](https://github.com/rudderlabs/rudder-sdk-js/issues/1649)) ([8b8ac8f](https://github.com/rudderlabs/rudder-sdk-js/commit/8b8ac8fb2b7fe0903fa383cfcd0388fe3022330c))


### Bug Fixes

* user sessions behavior ([#1708](https://github.com/rudderlabs/rudder-sdk-js/issues/1708)) ([84e7174](https://github.com/rudderlabs/rudder-sdk-js/commit/84e71744612c8345dc22b8cb0c9362d104eb35e9))

## [3.1.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.1.0...@rudderstack/analytics-js@3.1.1) (2024-05-10)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.1.0`
## [3.1.0](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.4...@rudderstack/analytics-js@3.1.0) (2024-04-26)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.4`
* `@rudderstack/analytics-js-plugins` updated to version `3.0.4`

### Features

* add a patch for storejs to expose length of the store ([#1694](https://github.com/rudderlabs/rudder-sdk-js/issues/1694)) ([36a13b0](https://github.com/rudderlabs/rudder-sdk-js/commit/36a13b017b3f95fab0aa9dfb73ec1e446ac7bb96))
* warn users on missing plugins ([#1691](https://github.com/rudderlabs/rudder-sdk-js/issues/1691)) ([c57cf82](https://github.com/rudderlabs/rudder-sdk-js/commit/c57cf820346a7fede352f2f346db37ad51413cf8))

## [3.0.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.3...@rudderstack/analytics-js@3.0.4) (2024-04-12)


### Bug Fixes

* remove MutationObserver and make polyfill checks consistent ([#1688](https://github.com/rudderlabs/rudder-sdk-js/issues/1688)) ([51b42fe](https://github.com/rudderlabs/rudder-sdk-js/commit/51b42fe51a5fbfa79df23b9b36095cc494dbcaa1))
* remove unnecessary window globals declarations ([#1687](https://github.com/rudderlabs/rudder-sdk-js/issues/1687)) ([09e5ab8](https://github.com/rudderlabs/rudder-sdk-js/commit/09e5ab89965a0b0dc5070891288a08358c103c0e))

## [3.0.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.2...@rudderstack/analytics-js@3.0.3) (2024-03-22)

### Dependency Updates

* `@rudderstack/analytics-js-plugins` updated to version `3.0.2`
## [3.0.2](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.1...@rudderstack/analytics-js@3.0.2) (2024-03-22)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.1`
* `@rudderstack/analytics-js-plugins` updated to version `3.0.1`

### Bug Fixes

* optimize localstorage transactions ([#1651](https://github.com/rudderlabs/rudder-sdk-js/issues/1651)) ([1289217](https://github.com/rudderlabs/rudder-sdk-js/commit/12892176578dd3628fded2311ea2548e3ff5802c))
* replace polyfillio with fastly ([#1664](https://github.com/rudderlabs/rudder-sdk-js/issues/1664)) ([24d3a0b](https://github.com/rudderlabs/rudder-sdk-js/commit/24d3a0b383f58b79fc1970d0c74761de30bb3f4a))
* type issues ([#1663](https://github.com/rudderlabs/rudder-sdk-js/issues/1663)) ([1f114a1](https://github.com/rudderlabs/rudder-sdk-js/commit/1f114a19ac14ffd9af6ae876a54d4d19afd80d65))

## [3.0.1](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.24...@rudderstack/analytics-js@3.0.1) (2024-03-21)

### Dependency Updates

* `@rudderstack/analytics-js-common` updated to version `3.0.0`
* `@rudderstack/analytics-js-plugins` updated to version `3.0.0`
## [3.0.0-beta.24](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.23...@rudderstack/analytics-js@3.0.0-beta.24) (2024-03-18)

## [3.0.0-beta.23](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.22...@rudderstack/analytics-js@3.0.0-beta.23) (2024-03-01)


### Features

* fetch anonymous id by cookie name provided in load option ([#1625](https://github.com/rudderlabs/rudder-sdk-js/issues/1625)) ([d8ccb10](https://github.com/rudderlabs/rudder-sdk-js/commit/d8ccb109f82398db8f53c51c0ac8f24cd1fd872e))
* reserved elements list updated ([#1632](https://github.com/rudderlabs/rudder-sdk-js/issues/1632)) ([20417fa](https://github.com/rudderlabs/rudder-sdk-js/commit/20417fa49e8e38c0d0092a470d98e9e72e2269f5))


### Bug Fixes

* **analytics-js-loading-scripts:** add version in polyfill io url ([#1630](https://github.com/rudderlabs/rudder-sdk-js/issues/1630)) ([3e315a6](https://github.com/rudderlabs/rudder-sdk-js/commit/3e315a6555871ef3cadb93236191a38bc21a2973))
* **rudder-sdk-js:** updated component-emitter to latest, supporting TS ([#1626](https://github.com/rudderlabs/rudder-sdk-js/issues/1626)) ([ffc0f24](https://github.com/rudderlabs/rudder-sdk-js/commit/ffc0f24e15bdaa8209860b287219e0a4dab4a0c4))

## [3.0.0-beta.22](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.21...@rudderstack/analytics-js@3.0.0-beta.22) (2024-02-16)

## [3.0.0-beta.21](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.20...@rudderstack/analytics-js@3.0.0-beta.21) (2024-02-02)


### Features

* **analytics-js:** auto expose new instance to window.rudderanalytics for npm usage ([#1599](https://github.com/rudderlabs/rudder-sdk-js/issues/1599)) ([27000ec](https://github.com/rudderlabs/rudder-sdk-js/commit/27000ec81f2d221ddaf206ceb0ed87ae2a8fc4e5))
* **analytics-js:** change default value of storage migrate flag to true ([#1602](https://github.com/rudderlabs/rudder-sdk-js/issues/1602)) ([6349436](https://github.com/rudderlabs/rudder-sdk-js/commit/6349436b8f6311e9f2fc74021dae864b2fdd4419))

## [3.0.0-beta.20](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.19...@rudderstack/analytics-js@3.0.0-beta.20) (2024-01-19)


### Features

* attach global error listeners ([#1586](https://github.com/rudderlabs/rudder-sdk-js/issues/1586)) ([b416897](https://github.com/rudderlabs/rudder-sdk-js/commit/b416897445c0e27b13853af44ed203daefd3f720))

## [3.0.0-beta.19](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.18...@rudderstack/analytics-js@3.0.0-beta.19) (2024-01-08)


### Bug Fixes

* enhance error handling ([#1568](https://github.com/rudderlabs/rudder-sdk-js/issues/1568)) ([0f08ba4](https://github.com/rudderlabs/rudder-sdk-js/commit/0f08ba4009de27647e2220aa2e56e30d5a99e902))
* localstorage retry patch and upgrade packages ([#1573](https://github.com/rudderlabs/rudder-sdk-js/issues/1573)) ([b14d027](https://github.com/rudderlabs/rudder-sdk-js/commit/b14d0276cc7dedf87062530eab404f7a924fecf7))

## [3.0.0-beta.18](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.17...@rudderstack/analytics-js@3.0.0-beta.18) (2023-12-13)


### Bug Fixes

* **analytics-js:** new export that excludes all third party host scripts ([#1533](https://github.com/rudderlabs/rudder-sdk-js/issues/1533)) ([999d8fa](https://github.com/rudderlabs/rudder-sdk-js/commit/999d8fa0262e7cd7b021404ec28a99fc64dd323d))

## [3.0.0-beta.17](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.16...@rudderstack/analytics-js@3.0.0-beta.17) (2023-12-06)


### Bug Fixes

* **analytics-js:** post ready callback invocations ([#1535](https://github.com/rudderlabs/rudder-sdk-js/issues/1535)) ([996fb9a](https://github.com/rudderlabs/rudder-sdk-js/commit/996fb9ad47bd9a28da533c5807873ea71f2f1f24))

## [3.0.0-beta.16](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.15...@rudderstack/analytics-js@3.0.0-beta.16) (2023-12-01)


### Features

* **analytics-js-loading-scripts:** add loading snippet version in event context ([#1483](https://github.com/rudderlabs/rudder-sdk-js/issues/1483)) ([4873cbc](https://github.com/rudderlabs/rudder-sdk-js/commit/4873cbc183879c0c1825cf939a53b6cf570cdf4e))


### Bug Fixes

* initialisation of bugsnag in chrome extension ([#1516](https://github.com/rudderlabs/rudder-sdk-js/issues/1516)) ([af970c9](https://github.com/rudderlabs/rudder-sdk-js/commit/af970c94ad45c50fcbbca0d0e7597fdefa08b154))
* multiple onReady invocation ([#1522](https://github.com/rudderlabs/rudder-sdk-js/issues/1522)) ([bf3b09b](https://github.com/rudderlabs/rudder-sdk-js/commit/bf3b09bef82eaf13f34bd538a080fd9f5e557e78))
* update polyfill list and add version ([#1518](https://github.com/rudderlabs/rudder-sdk-js/issues/1518)) ([653d58e](https://github.com/rudderlabs/rudder-sdk-js/commit/653d58e782c7449c7f7afe851665a59912287750))

## [3.0.0-beta.15](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.14...@rudderstack/analytics-js@3.0.0-beta.15) (2023-11-13)


### Features

* add beacon polyfill ([#1487](https://github.com/rudderlabs/rudder-sdk-js/issues/1487)) ([8053ae2](https://github.com/rudderlabs/rudder-sdk-js/commit/8053ae2ec7c5757bb65a1881ca1b79fe08c30fc7))
* buffer consent method invocations ([#1501](https://github.com/rudderlabs/rudder-sdk-js/issues/1501)) ([70f6f64](https://github.com/rudderlabs/rudder-sdk-js/commit/70f6f64500b6b08bbb657498ec0e1fdecf72b82c))
* consent api wrap up ([#1477](https://github.com/rudderlabs/rudder-sdk-js/issues/1477)) ([edc78ac](https://github.com/rudderlabs/rudder-sdk-js/commit/edc78ac54235aabfc8d1d78f961fbd650a3b7c73))
* reinitialize persistent data from consent options ([#1465](https://github.com/rudderlabs/rudder-sdk-js/issues/1465)) ([43f30b7](https://github.com/rudderlabs/rudder-sdk-js/commit/43f30b7296ae9a0862810fd0b3c520e8bddf614c))


### Bug Fixes

* gcm post consent issues ([#1507](https://github.com/rudderlabs/rudder-sdk-js/issues/1507)) ([6241d3d](https://github.com/rudderlabs/rudder-sdk-js/commit/6241d3da28edc9b5f478774a763226d9d72f7dcc))
* gcm qa fixes ([#1499](https://github.com/rudderlabs/rudder-sdk-js/issues/1499)) ([84ca784](https://github.com/rudderlabs/rudder-sdk-js/commit/84ca784e6848e7fd8988c4a93db46d4636f2f89c))
* storage migration to allow empty string values ([#1488](https://github.com/rudderlabs/rudder-sdk-js/issues/1488)) ([b639fa1](https://github.com/rudderlabs/rudder-sdk-js/commit/b639fa17f22d1bbad951b293c9edba36f164163e))

## [3.0.0-beta.14](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.13...@rudderstack/analytics-js@3.0.0-beta.14) (2023-10-27)


### Features

* alter life cycle for pre consent ([#1411](https://github.com/rudderlabs/rudder-sdk-js/issues/1411)) ([60ec092](https://github.com/rudderlabs/rudder-sdk-js/commit/60ec0924a1229678fb16d76a34a494c40a622a11))
* auto capture timezone as a part of context ([#1464](https://github.com/rudderlabs/rudder-sdk-js/issues/1464)) ([8e66069](https://github.com/rudderlabs/rudder-sdk-js/commit/8e660693d75727d2131a57ca57859e6d0b920e84))
* configure cookie to be fetched from exact domain ([#1468](https://github.com/rudderlabs/rudder-sdk-js/issues/1468)) ([4db1b10](https://github.com/rudderlabs/rudder-sdk-js/commit/4db1b10b45b4ffcd652aec6bd684943ca35c6c08))
* consent api ([#1458](https://github.com/rudderlabs/rudder-sdk-js/issues/1458)) ([216b405](https://github.com/rudderlabs/rudder-sdk-js/commit/216b405f7c319d5ff2d799d2e3a5efe2ee4a03af))


### Bug Fixes

* **analytics-js:** correct declared global extended type ([#1460](https://github.com/rudderlabs/rudder-sdk-js/issues/1460)) ([3f15290](https://github.com/rudderlabs/rudder-sdk-js/commit/3f1529037ba0541391b5f8033e37f867fdd7931c))
* **analytics-js:** fix remote import error when npm package is bundled with webpack ([#1466](https://github.com/rudderlabs/rudder-sdk-js/issues/1466)) ([3a818ac](https://github.com/rudderlabs/rudder-sdk-js/commit/3a818accd24e6b3667c75a6b60fb12aba36bdf7e))
* **monorepo:** update vulnerable dependencies ([#1457](https://github.com/rudderlabs/rudder-sdk-js/issues/1457)) ([7a4bc4c](https://github.com/rudderlabs/rudder-sdk-js/commit/7a4bc4cc641e4fff2a8f561ce6fd67d16c0cd5a0))
* upgrade vulnerable cryptoJS dependency, rolup to v4 & NX to v17 ([#1471](https://github.com/rudderlabs/rudder-sdk-js/issues/1471)) ([b2bb21c](https://github.com/rudderlabs/rudder-sdk-js/commit/b2bb21cb3f618f6c86f593d1706abe9e6349066d))

## [3.0.0-beta.13](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.12...@rudderstack/analytics-js@3.0.0-beta.13) (2023-10-16)


### Features

* add support for session storage ([#1440](https://github.com/rudderlabs/rudder-sdk-js/issues/1440)) ([7e3106b](https://github.com/rudderlabs/rudder-sdk-js/commit/7e3106b5317af05ad28a9c0c22a50638dbaebdc2))
* **analytics-js-service-worker:** deprecate service worker export of rudder-sdk-js package  in favor of the new standalone package([#1437](https://github.com/rudderlabs/rudder-sdk-js/issues/1437)) ([1797d3e](https://github.com/rudderlabs/rudder-sdk-js/commit/****************************************))
* dmt plugin for v3 ([#1412](https://github.com/rudderlabs/rudder-sdk-js/issues/1412)) ([97ee68a](https://github.com/rudderlabs/rudder-sdk-js/commit/97ee68a27daa5ce8c3a098cdc84c4ee7981f1149))


### Bug Fixes

* **analytics-js-loading-scripts:** add globalThis polyfill for safari ([#1446](https://github.com/rudderlabs/rudder-sdk-js/issues/1446)) ([bf111f8](https://github.com/rudderlabs/rudder-sdk-js/commit/bf111f8fc24fe75d183ea4924423e3c560ce457d))
* **analytics-js:** add global definitions extended window type ([#1445](https://github.com/rudderlabs/rudder-sdk-js/issues/1445)) ([b995635](https://github.com/rudderlabs/rudder-sdk-js/commit/b995635a7a3979173d35b34fa32b41b4429b166f))
* empty anonymous id after reset call ([#1433](https://github.com/rudderlabs/rudder-sdk-js/issues/1433)) ([b078347](https://github.com/rudderlabs/rudder-sdk-js/commit/b0783478917a75833f6e7133f10ad5f8999866de))
* identify traits type ([#1427](https://github.com/rudderlabs/rudder-sdk-js/issues/1427)) ([a58c919](https://github.com/rudderlabs/rudder-sdk-js/commit/a58c919ca36fc4e14d134455a08fe0e35f3e66ce))
* pass anonymous id load option to getanonymousid method ([#1443](https://github.com/rudderlabs/rudder-sdk-js/issues/1443)) ([e234a8f](https://github.com/rudderlabs/rudder-sdk-js/commit/e234a8fa7538ac84fbf451aa4b104c1b26b5a04e))

## [3.0.0-beta.12](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.11...@rudderstack/analytics-js@3.0.0-beta.12) (2023-10-02)


### Bug Fixes

* **analytics-js,rudder-sdk-js:** dead object issue ([#1410](https://github.com/rudderlabs/rudder-sdk-js/issues/1410)) ([94f4b2d](https://github.com/rudderlabs/rudder-sdk-js/commit/94f4b2db11b32a6b4ddcb2b1eacf7cdbd1867ef3))

## [3.0.0-beta.11](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.10...@rudderstack/analytics-js@3.0.0-beta.11) (2023-09-26)


### Bug Fixes

* **analytics-js-plugins:** add IE11 compatibility for crypto-es ([#1407](https://github.com/rudderlabs/rudder-sdk-js/issues/1407)) ([483ae44](https://github.com/rudderlabs/rudder-sdk-js/commit/483ae44ec59a5ddd72c0187e1c55128967e4289a))

## [3.0.0-beta.10](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.9...@rudderstack/analytics-js@3.0.0-beta.10) (2023-09-26)


### Features

* optimize plugin chunks ([#1379](https://github.com/rudderlabs/rudder-sdk-js/issues/1379)) ([5acfa4d](https://github.com/rudderlabs/rudder-sdk-js/commit/5acfa4d61d85e01c44252749074021d0a782b59e))


### Bug Fixes

* **analytics-js:** enforce default cloud mode events delivery plugin ([#1402](https://github.com/rudderlabs/rudder-sdk-js/issues/1402)) ([e06d9b8](https://github.com/rudderlabs/rudder-sdk-js/commit/e06d9b80cd029e11a394f5a4e2069313f69b9349))
* plugins registering twice ([#1380](https://github.com/rudderlabs/rudder-sdk-js/issues/1380)) ([9de8f28](https://github.com/rudderlabs/rudder-sdk-js/commit/9de8f280f27245bcc0284d5033ce8ccbf05abe1b))

## [3.0.0-beta.9](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.8...@rudderstack/analytics-js@3.0.0-beta.9) (2023-09-20)

## [3.0.0-beta.8](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.7...@rudderstack/analytics-js@3.0.0-beta.8) (2023-09-18)


### Features

* deprecate support of common names for integrations ([#1374](https://github.com/rudderlabs/rudder-sdk-js/issues/1374)) ([f1d097d](https://github.com/rudderlabs/rudder-sdk-js/commit/f1d097d9976f6c9d2ad0f1d81d469148f8c7c197))

## [3.0.0-beta.7](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.6...@rudderstack/analytics-js@3.0.0-beta.7) (2023-09-14)


### Features

* granular control of persisted data storing with auto migration ([#1329](https://github.com/rudderlabs/rudder-sdk-js/issues/1329)) ([b709edc](https://github.com/rudderlabs/rudder-sdk-js/commit/b709edcbf9314d26fb9cd0af5fa8790330853d9c))
* new load options for pre-consent configuration ([#1363](https://github.com/rudderlabs/rudder-sdk-js/issues/1363)) ([363a524](https://github.com/rudderlabs/rudder-sdk-js/commit/363a5242b607ed7bcb21f2847d15c6b399d0b6a9))


### Bug Fixes

* getsessionid updating the sessionstart status ([#1370](https://github.com/rudderlabs/rudder-sdk-js/issues/1370)) ([510bb06](https://github.com/rudderlabs/rudder-sdk-js/commit/510bb0604c559f7f5d98f721e79c106243fe542f))

# [3.0.0-beta.6](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.5...@rudderstack/analytics-js@3.0.0-beta.6) (2023-08-30)


### Bug Fixes

* forward only supported calls to the log provider ([#1333](https://github.com/rudderlabs/rudder-sdk-js/issues/1333)) ([a4c0743](https://github.com/rudderlabs/rudder-sdk-js/commit/a4c07434a35a778c81088475726f100f06abb7b0))
* handle errors for cookies data URI encode and decode ([#1350](https://github.com/rudderlabs/rudder-sdk-js/issues/1350)) ([b25ff6c](https://github.com/rudderlabs/rudder-sdk-js/commit/b25ff6c7f392d85ef9eb77293bfdf39b96207c51))


### Features

* add batching support to xhr plugin ([#1301](https://github.com/rudderlabs/rudder-sdk-js/issues/1301)) ([0421663](https://github.com/rudderlabs/rudder-sdk-js/commit/04216637a00dc5339cf466a586137415b46b6b49))
* add callback for polyfill load ([#1335](https://github.com/rudderlabs/rudder-sdk-js/issues/1335)) ([6ba9329](https://github.com/rudderlabs/rudder-sdk-js/commit/6ba932918dd03c110c92cd5837a2f8ca0f9cf192))
* add resize event handler to update screen info ([#1336](https://github.com/rudderlabs/rudder-sdk-js/issues/1336)) ([be05226](https://github.com/rudderlabs/rudder-sdk-js/commit/be0522668f44667d7d3a8082db89a7e4cad316c8))





# [3.0.0-beta.5](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.4...@rudderstack/analytics-js@3.0.0-beta.5) (2023-08-21)


### Bug Fixes

* **analytics-js:** update context page details in every event creation ([#1317](https://github.com/rudderlabs/rudder-sdk-js/issues/1317)) ([45c2300](https://github.com/rudderlabs/rudder-sdk-js/commit/45c230094aceb8176d92e7958fcb6910ebc61248))





# [3.0.0-beta.4](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.3...@rudderstack/analytics-js@3.0.0-beta.4) (2023-08-17)


### Bug Fixes

* **analytics-js:** fix SPA url change not reflecting in context page ([22c13bf](https://github.com/rudderlabs/rudder-sdk-js/commit/22c13bf7a3bdd632b7616995404548d7ea36d5a3))





# [3.0.0-beta.3](https://github.com/rudderlabs/rudder-sdk-js/compare/@rudderstack/analytics-js@3.0.0-beta.2...@rudderstack/analytics-js@3.0.0-beta.3) (2023-08-10)


### Bug Fixes

* **analytics-js:** add polyfill and feature detection for CustomEvent ([92c565f](https://github.com/rudderlabs/rudder-sdk-js/commit/92c565fca6ee2c90e0d7c34426e9a502d06ca745))
* **analytics-js:** change default cdn paths to the beta ones ([8d4bbe4](https://github.com/rudderlabs/rudder-sdk-js/commit/8d4bbe4bbf8be60dbdf0d07244a11da8f1948e5f))





# 3.0.0-beta.2 (2023-08-09)


### Bug Fixes

* add missing load option to buffer data plane events ([#1265](https://github.com/rudderlabs/rudder-sdk-js/issues/1265)) ([8bca1b2](https://github.com/rudderlabs/rudder-sdk-js/commit/8bca1b253dd3be90a2e7e6c258847c417c578850))
* **analytics-js:** add new flag in errorHandler, add state reset method ([#998](https://github.com/rudderlabs/rudder-sdk-js/issues/998)) ([4c76315](https://github.com/rudderlabs/rudder-sdk-js/commit/4c76315481793cd29ae4dc9b249a0684df2540d4))
* **analytics-js:** data residency url detection issue ([#1163](https://github.com/rudderlabs/rudder-sdk-js/issues/1163)) ([4e80937](https://github.com/rudderlabs/rudder-sdk-js/commit/4e8093742935366918de2526eeabdf2b51851f55))
* **analytics-js:** fix edge cases & add unit tests for error handler ([068f305](https://github.com/rudderlabs/rudder-sdk-js/commit/068f305a6f401b7b521b2f0b0bdd8124d31c67c0))
* **analytics-js:** fix issues with tracking methods overloads ([#1164](https://github.com/rudderlabs/rudder-sdk-js/issues/1164)) ([718f9a9](https://github.com/rudderlabs/rudder-sdk-js/commit/718f9a9bf9e24fa203cfe9cec835528c91ed955f))
* **analytics-js:** fix type issues & broken unit tests ([dd198bc](https://github.com/rudderlabs/rudder-sdk-js/commit/dd198bc737f23d666dff15501a530b65c5b674f3))
* **analytics-js:** support imports in SSR & reduce shared bundles code ([#1135](https://github.com/rudderlabs/rudder-sdk-js/issues/1135)) ([29d1d75](https://github.com/rudderlabs/rudder-sdk-js/commit/29d1d75325c732d62b9926d3848c0b1b2e566c85))
* auto capture anonymous id ([#1160](https://github.com/rudderlabs/rudder-sdk-js/issues/1160)) ([2947ead](https://github.com/rudderlabs/rudder-sdk-js/commit/2947ead0b431ea608f510939f1d68ca1bae58095))
* auto detection of destSDKBaseUrl ([#1144](https://github.com/rudderlabs/rudder-sdk-js/issues/1144)) ([1d6a1d7](https://github.com/rudderlabs/rudder-sdk-js/commit/1d6a1d7788f03fcd3d0eb101c277737a5f758368))
* avoid persisting user id in alias api ([#1057](https://github.com/rudderlabs/rudder-sdk-js/issues/1057)) ([273eb9e](https://github.com/rudderlabs/rudder-sdk-js/commit/273eb9e27fda917f443cd1eff63d74580b612662))
* config url deduction ([#1282](https://github.com/rudderlabs/rudder-sdk-js/issues/1282)) ([658dc24](https://github.com/rudderlabs/rudder-sdk-js/commit/658dc24e077035898871888bfd4c72e88f16deb2))
* cookie storage options ([#1232](https://github.com/rudderlabs/rudder-sdk-js/issues/1232)) ([23970bc](https://github.com/rudderlabs/rudder-sdk-js/commit/23970bc88965b8a8631f406cd0c47b6bb949e0ea))
* error reporting ([#1285](https://github.com/rudderlabs/rudder-sdk-js/issues/1285)) ([1b9324e](https://github.com/rudderlabs/rudder-sdk-js/commit/1b9324e0be38eecbc25cb08be7650d8c1e474d35))
* ie11 incompatibility issues ([#1279](https://github.com/rudderlabs/rudder-sdk-js/issues/1279)) ([80c59ae](https://github.com/rudderlabs/rudder-sdk-js/commit/80c59ae6b1b5908087e36d39956becab5523027e))
* issues in rudder event structure ([#1111](https://github.com/rudderlabs/rudder-sdk-js/issues/1111)) ([bed6210](https://github.com/rudderlabs/rudder-sdk-js/commit/bed6210cdc1097f1e3a75e9151cace1a7425401d))
* issues post sanity checks, tidy up code structure, add uaCH, npm packaging ([#1132](https://github.com/rudderlabs/rudder-sdk-js/issues/1132)) ([0fa64c1](https://github.com/rudderlabs/rudder-sdk-js/commit/0fa64c1bb277cbd20b0d7c984347e5fe52e4d4fe))
* native destinations queue options ([#1209](https://github.com/rudderlabs/rudder-sdk-js/issues/1209)) ([0341fc8](https://github.com/rudderlabs/rudder-sdk-js/commit/0341fc8a35433209a402f497cd92865bcec9f20f))
* normalize all error messages ([#1191](https://github.com/rudderlabs/rudder-sdk-js/issues/1191)) ([b45f3f3](https://github.com/rudderlabs/rudder-sdk-js/commit/b45f3f324afd2df6e806a586fe7d281392b03d79))
* storage option configuration ([#1217](https://github.com/rudderlabs/rudder-sdk-js/issues/1217)) ([7dc0488](https://github.com/rudderlabs/rudder-sdk-js/commit/7dc048895f7fae0783284dc5351b9a86df8981db))
* use destination display name throughout the app ([#1269](https://github.com/rudderlabs/rudder-sdk-js/issues/1269)) ([6e6a18c](https://github.com/rudderlabs/rudder-sdk-js/commit/6e6a18c5248654963130e24d94191350292a5f58))
* xhr queue plugin retry mechanism ([#1171](https://github.com/rudderlabs/rudder-sdk-js/issues/1171)) ([6d8d2b9](https://github.com/rudderlabs/rudder-sdk-js/commit/6d8d2b9db554459061995494de0b42c1f35b3bb6))


### Features

* add application state to bugsnag metadata ([#1168](https://github.com/rudderlabs/rudder-sdk-js/issues/1168)) ([7273e3a](https://github.com/rudderlabs/rudder-sdk-js/commit/7273e3af6683165c3c33265c64db6fb28a3ff5e5))
* add validations for load options ([#1277](https://github.com/rudderlabs/rudder-sdk-js/issues/1277)) ([1a276bf](https://github.com/rudderlabs/rudder-sdk-js/commit/1a276bf99471790080bc74f3e126e208cb416eaf))
* **analytics-js-plugins:** new beacon queue plugin ([#1173](https://github.com/rudderlabs/rudder-sdk-js/issues/1173)) ([9e4602b](https://github.com/rudderlabs/rudder-sdk-js/commit/9e4602b67c7ce1345023388e09c3701820f71091))
* **analytics-js:** add application lifecycle and analytics class ([71ceed5](https://github.com/rudderlabs/rudder-sdk-js/commit/71ceed5276a9a4da83df4654b76b1d012e72f766))
* **analytics-js:** add external source loader, fix async tests, cleanup ([8ba7bdf](https://github.com/rudderlabs/rudder-sdk-js/commit/8ba7bdf260a6771bf4cfc154b9f84ab61846a622))
* **analytics-js:** add global state initial structure ([f636227](https://github.com/rudderlabs/rudder-sdk-js/commit/f636227e0094a4a3f0bfdc17d52c4731ab17e20c))
* **analytics-js:** add globally exposed analytics instances ([03931a6](https://github.com/rudderlabs/rudder-sdk-js/commit/03931a67c51a62b41db1398a28f1e82d48b9a8a5))
* **analytics-js:** add HttpClient, Logger & ErrorHandler services ([236f951](https://github.com/rudderlabs/rudder-sdk-js/commit/236f95198d8f2ae4a029339074fa063679fbaa38))
* **analytics-js:** add more state slices ([#973](https://github.com/rudderlabs/rudder-sdk-js/issues/973)) ([7c1e627](https://github.com/rudderlabs/rudder-sdk-js/commit/7c1e6275ad9eeec2ccdd4a100b085437f78a2603))
* **analytics-js:** add online status detection in capabilities mngr ([a4702da](https://github.com/rudderlabs/rudder-sdk-js/commit/a4702dab9718f66cacc8aa58add41840f1853a23))
* **analytics-js:** add RSA_Initialiser & RSA_Ready event dispatching ([#1283](https://github.com/rudderlabs/rudder-sdk-js/issues/1283)) ([612b388](https://github.com/rudderlabs/rudder-sdk-js/commit/612b3886b13c4ce9c6b616218b62c7c1041e671e))
* **analytics-js:** add storage manager,fix issue with localhost cookies ([9a5bff7](https://github.com/rudderlabs/rudder-sdk-js/commit/9a5bff7ebbf76da9cbb768c401362b57da67d37b))
* **analytics-js:** expose global analytics instances  & preload buffer ([203919f](https://github.com/rudderlabs/rudder-sdk-js/commit/203919fd8efd263afb41732cb912898d50ca5781))
* **analytics-js:** migrated js-plugin dependency to source code ([4be78ab](https://github.com/rudderlabs/rudder-sdk-js/commit/4be78abcddbc11bae85c5d5f2718a46a4b0119db))
* bugsnag plugin ([#1159](https://github.com/rudderlabs/rudder-sdk-js/issues/1159)) ([c59cfd9](https://github.com/rudderlabs/rudder-sdk-js/commit/c59cfd9e6e4160e4759695dddf527bfc512f119e))
* config manager ([#990](https://github.com/rudderlabs/rudder-sdk-js/issues/990)) ([cc48a29](https://github.com/rudderlabs/rudder-sdk-js/commit/cc48a29b414ffbbbba10980c73a3fa78c6fd5e7c))
* configurable storage type ([#1258](https://github.com/rudderlabs/rudder-sdk-js/issues/1258)) ([08e3616](https://github.com/rudderlabs/rudder-sdk-js/commit/08e3616bece2ad3df1c533833b344a9c811e70fe))
* consent manager plugin ([#1096](https://github.com/rudderlabs/rudder-sdk-js/issues/1096)) ([7af1cce](https://github.com/rudderlabs/rudder-sdk-js/commit/7af1ccec03997cd55ce70aa1e4afba05b22da264))
* create bundling and packaging for v3 ([#1098](https://github.com/rudderlabs/rudder-sdk-js/issues/1098)) ([3f14bbe](https://github.com/rudderlabs/rudder-sdk-js/commit/3f14bbe8d9d6af62d4366873c59c9c21df704675))
* dataplane events queue ([#1088](https://github.com/rudderlabs/rudder-sdk-js/issues/1088)) ([17f45bc](https://github.com/rudderlabs/rudder-sdk-js/commit/17f45bc1a57f37edee56808aa1f337deef208528))
* events repository ([#1063](https://github.com/rudderlabs/rudder-sdk-js/issues/1063)) ([8a92dcb](https://github.com/rudderlabs/rudder-sdk-js/commit/8a92dcb14311b3537d391375fc0ed34433b5afe7))
* events service ([#1000](https://github.com/rudderlabs/rudder-sdk-js/issues/1000)) ([7bb3025](https://github.com/rudderlabs/rudder-sdk-js/commit/7bb30251f4e5bfb169e69aca377e7e57df8ac58a))
* hybrid mode ([#1147](https://github.com/rudderlabs/rudder-sdk-js/issues/1147)) ([e623214](https://github.com/rudderlabs/rudder-sdk-js/commit/e6232145818032aa6e33130511b1e1d41d4a293b))
* improve adblocker detection ([#1176](https://github.com/rudderlabs/rudder-sdk-js/issues/1176)) ([6fb57ef](https://github.com/rudderlabs/rudder-sdk-js/commit/6fb57ef40c4ea73cb9d1c01844458702e2819ebc))
* improve destination loader logic ([#1263](https://github.com/rudderlabs/rudder-sdk-js/issues/1263)) ([c154155](https://github.com/rudderlabs/rudder-sdk-js/commit/c154155102f22ac17c6f82b8869b85000a5cc69d))
* ketch consent manager plugin ([#1210](https://github.com/rudderlabs/rudder-sdk-js/issues/1210)) ([75d4588](https://github.com/rudderlabs/rudder-sdk-js/commit/75d4588481e3fe86bad804162663f332ce2f895d))
* log messages language dictionary ([#1206](https://github.com/rudderlabs/rudder-sdk-js/issues/1206)) ([77a867e](https://github.com/rudderlabs/rudder-sdk-js/commit/77a867e9c109122a9223293cb5af25f1ccb48ecc))
* native destinations events queue ([#1127](https://github.com/rudderlabs/rudder-sdk-js/issues/1127)) ([ead338c](https://github.com/rudderlabs/rudder-sdk-js/commit/ead338cb5a45c7d109428259459892ff896a0ccb))
* plugins manager & capabilities manager ([#1062](https://github.com/rudderlabs/rudder-sdk-js/issues/1062)) ([9d03bbd](https://github.com/rudderlabs/rudder-sdk-js/commit/9d03bbdea3bf2658f56580aa9bb8df2af9baf9a0))
* refactor apis ([#1240](https://github.com/rudderlabs/rudder-sdk-js/issues/1240)) ([4f25a03](https://github.com/rudderlabs/rudder-sdk-js/commit/4f25a0377ef438a4e4b5dcad6f2504ec5da5f7a3))
* remove crypto based encryption for persistent data ([#1197](https://github.com/rudderlabs/rudder-sdk-js/issues/1197)) ([187b701](https://github.com/rudderlabs/rudder-sdk-js/commit/187b7016e75f092c54698fe7fe3652656943e35f))
* rename sdk file name ([#1190](https://github.com/rudderlabs/rudder-sdk-js/issues/1190)) ([0167e38](https://github.com/rudderlabs/rudder-sdk-js/commit/0167e384a05e1fa33b3da3b940f3952ee06ef21e))
* session tracking ([#1061](https://github.com/rudderlabs/rudder-sdk-js/issues/1061)) ([e46e98c](https://github.com/rudderlabs/rudder-sdk-js/commit/e46e98c5211aaccb325e4c1109d8c26e4c41394d))
* storage service improvements ([#1233](https://github.com/rudderlabs/rudder-sdk-js/issues/1233)) ([441fd60](https://github.com/rudderlabs/rudder-sdk-js/commit/441fd600c2e72e990518e45c972e43ce33567e7f))
* user session manager ([#1013](https://github.com/rudderlabs/rudder-sdk-js/issues/1013)) ([450cce0](https://github.com/rudderlabs/rudder-sdk-js/commit/450cce03bf09a5c3f3d93b6a6083173ddb6309d7))





# Change Log
