/* eslint-disable unicorn/filename-case */
// map b/w the names of integrations comming from config plane to integration module names
const configToIntNames = {
  HS: 'HubSpot',
  GA: 'GA',
  HOTJAR: 'Hotjar',
  GOOGLEADS: 'GoogleAds',
  VWO: 'VWO',
  GTM: 'GoogleTagManager',
  BRAZE: 'Braze',
  INTERCOM: 'INTERCOM',
  KEEN: 'Keen',
  KISSMETRICS: 'Kissmetrics',
  CUSTOMERIO: 'CustomerIO',
  CHARTBEAT: 'Chartbeat',
  FACEBOOK_PIXEL: 'FacebookPixel',
  LOTAME: 'Lotame',
  OPTIMIZELY: 'Optimizely',
  BUGSNAG: 'Bugsnag',
  FULLSTORY: 'Fullstory',
  TVSQUARED: 'TVSquared',
  GA4: 'GA4',
  GA4_V2: 'GA4_V2',
  MOENGAGE: 'MoEngage',
  AM: 'Amplitude',
  PENDO: 'Pendo',
  LYTICS: 'Lytics',
  APPCUES: 'Appcues',
  POSTHOG: 'Posthog',
  KLAVIYO: 'Klaviyo',
  CLEVERTAP: 'Clevertap',
  BINGADS: 'BingAds',
  PINTEREST_TAG: 'PinterestTag',
  ADOBE_ANALYTICS: 'AdobeAnalytics',
  LINKEDIN_INSIGHT_TAG: 'LinkedInInsightTag',
  REDDIT_PIXEL: 'RedditPixel',
  DRIP: 'Drip',
  HEAP: 'Heap',
  CRITEO: 'Criteo',
  MP: 'Mixpanel',
  QUALTRICS: 'Qualtrics',
  PROFITWELL: 'ProfitWell',
  SENTRY: 'Sentry',
  QUANTUMMETRIC: 'QuantumMetric',
  SNAP_PIXEL: 'SnapPixel',
  POST_AFFILIATE_PRO: 'PostAffiliatePro',
  GOOGLE_OPTIMIZE: 'GoogleOptimize',
  LAUNCHDARKLY: 'LaunchDarkly',
  GA360: 'GA360',
  ADROLL: 'Adroll',
  DCM_FLOODLIGHT: 'DCMFloodlight',
  MATOMO: 'Matomo',
  VERO: 'Vero',
  MOUSEFLOW: 'Mouseflow',
  ROCKERBOX: 'Rockerbox',
  CONVERTFLOW: 'ConvertFlow',
  SNAPENGAGE: 'SnapEngage',
  LIVECHAT: 'LiveChat',
  SHYNET: 'Shynet',
  WOOPRA: 'Woopra',
  ROLLBAR: 'RollBar',
  QUORA_PIXEL: 'QuoraPixel',
  JUNE: 'June',
  ENGAGE: 'Engage',
  ITERABLE: 'Iterable',
  YANDEX_METRICA: 'YandexMetrica',
  REFINER: 'Refiner',
  QUALAROO: 'Qualaroo',
  PODSIGHTS: 'Podsights',
  AXEPTIO: 'Axeptio',
  SATISMETER: 'Satismeter',
  MICROSOFT_CLARITY: 'MicrosoftClarity',
  SENDINBLUE: 'Sendinblue',
  OLARK: 'Olark',
  LEMNISK: 'Lemnisk',
  TIKTOK_ADS: 'TiktokAds',
  ACTIVE_CAMPAIGN: 'ActiveCampaign',
  SPRIG: 'Sprig',
  SPOTIFYPIXEL: 'SpotifyPixel',
  COMMANDBAR: 'CommandBar',
  NINETAILED: 'Ninetailed',
  XPIXEL: 'XPixel',
  GAINSIGHT_PX: 'Gainsight_PX',
  USERPILOT: 'Userpilot',
};

export { configToIntNames };
