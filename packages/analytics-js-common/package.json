{"name": "@rudderstack/analytics-js-common", "version": "3.21.0", "private": true, "description": "RudderStack JavaScript SDK common code", "module": "dist/npm/index.js", "types": "./dist/npm/index.d.ts", "publishConfig": {}, "files": ["dist/npm", "LICENSE.md", "README.md", "CHANGELOG.md"], "keywords": ["analytics", "rudder"], "author": "RudderStack", "license": "Elastic-2.0", "repository": {"type": "git", "url": "git+https://github.com/rudderlabs/rudder-sdk-js.git", "directory": "packages/analytics-js-common"}, "bugs": {"url": "https://github.com/rudderlabs/rudder-sdk-js/issues"}, "homepage": "https://github.com/rudderlabs/rudder-sdk-js/blob/main/packages/analytics-js-common/README.md", "scripts": {"clean": "rimraf -rf ./dist && rimraf -rf ./node_modules/.cache && rimraf -rf ./reports", "start": "exit 0", "build": "npm run build:browser && npm run build:package", "build:modern": "npm run build:browser:modern && npm run build:package:modern", "build:browser": "exit 0", "build:browser:modern": "exit 0", "build:npm": "tsc", "build:npm:modern": "BROWSERSLIST_ENV=modern npm run build:npm", "build:package": "npm run build:npm", "build:package:modern": "npm run build:npm:modern", "test": "nx test --maxWorkers=50%", "test:ci": "nx test --configuration=ci --runInBand --maxWorkers=1 --forceExit", "check:lint": "nx lint", "check:lint:ci": "nx lint --configuration=ci", "check:size:build": "npm run build:browser && npm run build:browser:modern && npm run build:package && npm run build:package:modern", "check:size": "npm run check:size:build && size-limit", "check:size:json": "size-limit --json", "check:circular": "madge --circular --extensions js,ts src --warning || exit 0", "check:support": "NODE_ENV=production npx browserslist --mobile-to-desktop", "check:support:modern": "NODE_ENV=modern npx browserslist --mobile-to-desktop", "check:duplicates": "jscpd src", "check:security": "npm audit --recursive --audit-level=high", "start:modern": "exit 0", "package": "npm pack", "release": "npm publish"}, "dependencies": {"@preact/signals-core": "1.11.0", "rudder-component-cookie": "0.0.1", "@segment/top-domain": "3.0.1", "crypto-js": "4.2.0", "@lukeed/uuid": "2.0.1", "get-value": "4.0.1", "ramda": "0.31.3", "storejs": "2.1.0"}, "devDependencies": {"@bugsnag/js": "8.4.0"}, "overrides": {}, "browserslist": {"production": ["defaults", "Edge >= 80", "Firefox >= 47", "IE >= 11", "Chrome >= 54", "Safari >= 7", "Opera >= 43"], "modern": ["defaults", "supports es6-module-dynamic-import"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 edge version", "last 1 safari version"]}}