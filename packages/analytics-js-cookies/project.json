{"name": "@rudderstack/analytics-js-cookies", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/analytics-js-cookies/src", "projectType": "library", "tags": ["type:lib", "scope:analytics-v3-cookies"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/analytics-js-cookies/jest.config.mjs", "passWithNoTests": true, "codeCoverage": true, "watchAll": false, "forceExit": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"hasTypeAwareRules": true, "lintFilePatterns": ["packages/analytics-js-cookies/src/**/*.{ts,js}", "packages/analytics-js-cookies/{package,project}.json"]}, "configurations": {"ci": {"force": true, "outputFile": "packages/analytics-js-cookies/reports/eslint.json", "format": "json"}}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventionalcommits", "tagPrefix": "{projectName}@", "trackDeps": true}}, "github": {"executor": "@jscutlery/semver:github", "options": {"tag": "@rudderstack/analytics-js-cookies@0.4.27", "title": "@rudderstack/analytics-js-cookies@0.4.27", "discussion-category": "@rudderstack/analytics-js-cookies@0.4.27", "notesFile": "./packages/analytics-js-cookies/CHANGELOG_LATEST.md"}}}}