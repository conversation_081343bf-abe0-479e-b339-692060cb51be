{"name": "rudder-sdk-js", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/analytics-v1.1/src", "projectType": "library", "tags": ["type:sdk", "scope:analytics-v1.1"], "targets": {"prepare-test-mocks": {"executor": "nx:run-commands", "options": {"command": "sed 's/var rudderanalytics/rudderanalytics/g' packages/analytics-v1.1/dist/cdn/legacy/rudder-analytics.min.js > packages/analytics-v1.1/__mocks__/cdnSDK.js"}, "dependsOn": ["build:browser"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/analytics-v1.1/jest.config.mjs", "passWithNoTests": true, "codeCoverage": true, "watchAll": false, "forceExit": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}, "dependsOn": ["prepare-test-mocks"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"hasTypeAwareRules": true, "lintFilePatterns": ["packages/analytics-v1.1/src/**/*.{ts,js}", "packages/analytics-v1.1/{package,project}.json"]}, "configurations": {"ci": {"force": true, "outputFile": "packages/analytics-v1.1/reports/eslint.json", "format": "json"}}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventionalcommits", "tagPrefix": "{projectName}@", "trackDeps": true}}, "github": {"executor": "@jscutlery/semver:github", "options": {"tag": "rudder-sdk-js@2.51.4", "title": "rudder-sdk-js@2.51.4", "discussion-category": "rudder-sdk-js@2.51.4", "notesFile": "./packages/analytics-v1.1/CHANGELOG_LATEST.md"}}}}