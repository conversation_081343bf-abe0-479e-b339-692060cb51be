{"name": "rudder-sdk-js", "version": "2.51.4", "description": "RudderStack JavaScript SDK", "main": "dist/npm/index.js", "module": "dist/npm/index.es.js", "exports": {".": {"types": "./dist/npm/index.d.ts", "import": "./dist/npm/index.es.js", "require": "./dist/npm/index.js"}, "./content-script": {"types": "./dist/npm/index.d.ts", "require": "./dist/npm/content-script/index.js", "import": "./dist/npm/content-script/index.es.js"}}, "typesVersions": {"*": {".": ["dist/npm/index.d.ts"]}}, "publishConfig": {"access": "public"}, "files": ["dist/npm", "LICENSE.md", "README.md", "CHANGELOG.md"], "scripts": {"clean": "rimraf -rf ./dist && rimraf -rf ./node_modules/.cache && rimraf -rf ./reports", "start": "rollup --config rollup-configs/rollup.sdk.browser.mjs --watch --environment DEV_SERVER,PROD_DEBUG", "start:modern": "BROWSERSLIST_ENV=modern npm run start", "build": "npm run build:browser && npm run build:package", "build:modern": "npm run build:browser:modern && npm run build:package:modern", "build:browser": "rollup --config rollup-configs/rollup.sdk.browser.mjs --environment VERSION:$npm_package_version,UGLIFY,PROD_DEBUG,ENV:prod", "build:browser:modern": "BROWSERSLIST_ENV=modern rollup --config rollup-configs/rollup.sdk.browser.mjs --environment VERSION:$npm_package_version,UGLIFY,PROD_DEBUG,ENV:prod", "build:npm": "rollup --config rollup-configs/rollup.sdk.npm.mjs --environment VERSION:$npm_package_version,UGLIFY,ENV:prod,NPM", "build:npm:modern": "BROWSERSLIST_ENV=modern rollup --config rollup-configs/rollup.sdk.npm.mjs --environment VERSION:$npm_package_version,UGLIFY,ENV:prod,NPM", "build:npm:content-script": "NO_EXTERNAL_HOST=true npm run build:npm", "build:npm:content-script:modern": "BROWSERSLIST_ENV=modern  NO_EXTERNAL_HOST=true npm run build:npm", "build:package": "npm run build:npm && npm run build:npm:content-script", "build:package:modern": "npm run build:npm:modern && npm run build:npm:content-script:modern", "build:browser:test": "npm run build:browser && sed 's/var rudderanalytics/rudderanalytics/g' dist/cdn/legacy/rudder-analytics.min.js > __mocks__/cdnSDK.js", "test": "npm run build:browser:test && nx test --maxWorkers=50%", "test:ci": "npm run build:browser:test && nx test --configuration=ci --runInBand --maxWorkers=1 --forceExit", "check:lint": "nx lint", "check:lint:ci": "nx lint --configuration=ci", "check:size:build": "npm run build:browser && npm run build:browser:modern && npm run build:package && npm run build:package:modern", "check:size": "npm run check:size:build && size-limit", "check:size:json": "size-limit --json", "check:circular": "madge --circular --extensions js,ts src || exit 0", "check:support": "NODE_ENV=production npx browserslist --mobile-to-desktop", "check:support:modern": "NODE_ENV=modern npx browserslist --mobile-to-desktop", "check:duplicates": "jscpd src", "check:security": "npm audit --recursive --audit-level=high", "build:browser:debug": "rollup --config rollup-configs/rollup.sdk.browser.mjs --environment VERSION:$npm_package_version,UGLIFY,PROD_DEBUG:inline,ENV:prod", "build:browser:bundle-size": "VISUALIZER=true npm run build:browser", "build:browser:modern:bundle-size": "VISUALIZER=true npm run build:browser:modern", "build:npm:dev": "rollup --config rollup-configs/rollup.sdk.npm.mjs --environment VERSION:$npm_package_version,NPM", "package": "npm pack", "release": "npm publish"}, "keywords": ["analytics", "rudder"], "author": "RudderStack", "license": "Elastic-2.0", "bugs": {"url": "https://github.com/rudderlabs/rudder-sdk-js/issues"}, "homepage": "https://github.com/rudderlabs/rudder-sdk-js/blob/main/packages/analytics-v1.1/README.md", "repository": {"type": "git", "url": "https://github.com/rudderlabs/rudder-sdk-js.git", "directory": "packages/analytics-v1.1"}, "dependencies": {"@rudderstack/analytics-js-common": "*", "get-value": "4.0.1", "ramda": "0.31.3", "component-emitter": "2.0.0", "@segment/localstorage-retry": "1.3.0", "@lukeed/uuid": "2.0.1"}, "devDependencies": {}, "overrides": {}, "browserslist": {"production": ["defaults", "Edge >= 80", "Firefox >= 47", "IE >= 11", "Chrome >= 54", "Safari >= 7", "Opera >= 43"], "modern": ["defaults"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 edge version", "last 1 safari version"]}}