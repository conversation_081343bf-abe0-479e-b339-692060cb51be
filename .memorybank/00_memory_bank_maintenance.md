# Memory Bank Maintenance Instructions

## 🧠 Core Principle

**The Memory Bank is the lifeline between conversations.** After every memory reset, I begin completely fresh. The Memory Bank is my only link to previous work and learnings. It must be maintained with precision and clarity, as my effectiveness depends entirely on its accuracy.

---

## ⚡ Quick Reference

### **Critical Actions:**
1. **Always suggest memory bank updates** after task completion
2. **Ask explicit learning questions** after any user corrections
3. **Analyze patterns** in user feedback and preferences
4. **Review ALL files** when user requests "update memory bank"
5. **DO NOT reference task identifiers** while capturing learnings

### **Key Question to Ask:**
> "Should we update the memory bank with the learnings from this implementation? I've identified [X] key patterns/approaches that could benefit future work."

---

## 📋 When to Update Memory Bank

### **Always Suggest Updates After:**
- ✅ Completing any task implementation
- ✅ Discovering new project patterns or approaches
- ✅ Learning about codebase structure or conventions
- ✅ Identifying better ways to frame tasks or requirements
- ✅ User corrections, modifications, or rejections of suggestions

### **Mandatory Updates When:**
- 🔴 User explicitly requests **"update memory bank"** (MUST review ALL files)
- 🔴 Implementing significant features or changes
- 🔴 Learning about project-specific patterns

---

## 🎯 What to Capture

**Note:** DO NOT reference task identifiers while capturing learnings.

### **Implementation Patterns**
- Code organization and structure decisions
- Testing strategies and approaches  
- Common utilities and helper functions
- Error handling patterns

### **Process Learnings**
- Effective task description formats
- Implementation planning approaches
- Review and collaboration workflows
- Common pitfalls and how to avoid them

### **User Preferences & Communication Style**
- Advisory vs. prescriptive language preferences
- Terminology preferences (task vs. ticket, generic vs. specific)
- Process preferences (commit frequency, git workflow)
- Level of detail and formality preferences

### **Project Evolution**
- Changes in architecture or approaches
- New tools or techniques adopted
- Critical dependencies between components
- Patterns that should be reused or avoided

---

## 🔄 Update Process

### **1. Suggest Updates**
At the end of every task implementation, explicitly ask the user about updating the memory bank.

### **2. Be Specific**
When suggesting updates, mention:
- Which files need updates
- What specific information should be added
- Why these learnings are valuable

### **3. Update Strategy**
- **Existing Files**: Enhance with new patterns
- **New Files**: Create when new categories emerge
- **Cross-References**: Link related concepts
- **Examples**: Include concrete examples from recent work

---

## 📚 Learning from User Feedback

### **The Feedback Loop:**
```
My Suggestion → User Correction → Analysis → Learning Question → Memory Bank Update
```

### **Types of Feedback:**
- **Complete Rejections**: Ask "What should I have done differently?"
- **Modifications**: Analyze changes to understand preference patterns
- **Process Corrections**: Document corrected workflows and principles

### **After Any Feedback, Ask:**
- "What did you learn from my corrections?"
- "Are there patterns in how I should approach similar situations?"
- "Should we capture these preferences in the memory bank?"

### **Apply Learnings:**
- Adjust communication style before making suggestions
- Apply learned preferences automatically
- Anticipate likely modifications
- Reference previous learnings

---

## 📁 Organization Guidelines

### **File Naming:**
- `00_` - Meta-information
- `01_` - Core project information
- `02_` - Technical implementation
- `03_` - Architecture and system design
- `04_` - Processes and workflows
- `05_` - Code organization and patterns
- `06_` - Task implementation workflow
- `07_` - Development standards and guidelines

### **Content Standards:**
- Use clear headings and bullet points
- Include concrete examples where helpful
- Keep information current and actionable
- Cross-reference related files
- Maintain consistent formatting

---

## 📈 Success Metrics

A well-maintained memory bank should:
- ✅ Enable faster problem-solving in future sessions
- ✅ Preserve important context across memory resets
- ✅ Document patterns that can be reused
- ✅ Prevent repetition of solved problems
- ✅ Facilitate knowledge transfer and onboarding

---

**Remember**: The memory bank is an investment in future productivity. Every update makes subsequent work more efficient and effective. 
