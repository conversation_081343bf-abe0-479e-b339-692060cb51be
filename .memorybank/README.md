# RudderStack JavaScript SDK - Memory Bank

This memory bank contains structured knowledge about the RudderStack JavaScript SDK project for AI agent reference.

## 📚 Quick Navigation

### **Core Information**

- [01_project_overview.md](01_project_overview.md) - Project purpose, features, monorepo structure
- [01a_common_terminology.md](01a_common_terminology.md) - RudderStack and SDK terminology

### **Technical Details**

- [02_technical_stack.md](02_technical_stack.md) - Technologies, tools, and dependencies
- [03_system_architecture.md](03_system_architecture.md) - Architecture and data flow
- [05_code_structure.md](05_code_structure.md) - Monorepo layout, package details, and sanity suite patterns

### **Development Standards**

- [07_development_guidelines.md](07_development_guidelines.md) - Code standards, best practices, and constraints

### **Processes & Workflows**

- [04_git_workflow_cicd.md](04_git_workflow_cicd.md) - Git workflow, CI/CD, and sanity suite deployment
- [06_task_implementation_workflow.md](06_task_implementation_workflow.md) - Task implementation guide

### **Meta Information**

- [00_memory_bank_maintenance.md](00_memory_bank_maintenance.md) - How to maintain this memory bank

## 🎯 Purpose

Enable AI agents to quickly understand and work with the RudderStack JavaScript SDK codebase across memory resets. While primarily designed for AI agent context, this knowledge base can also provide some onboarding value for new engineers.

---

**Note**: Files are designed for quick scanning and reference. Update as the project evolves.
