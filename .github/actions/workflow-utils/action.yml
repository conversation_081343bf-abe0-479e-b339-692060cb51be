name: 'External Workflow Trigger & Wait'
description: 'Generic utility for triggering external workflows and waiting for their completion'

inputs:
  github_token:
    description: 'GitHub token for API access'
    required: true
  target_repo:
    description: 'Target repository (owner/repo) for external workflow'
    required: true
  workflow_file:
    description: 'Workflow file to trigger (e.g., my-workflow.yml)'
    required: true
  workflow_ref:
    description: 'Git ref to trigger the workflow on'
    required: false
    default: 'main'
  workflow_name:
    description: 'Descriptive name for this workflow (used in logging)'
    required: false
    default: 'External Workflow'
  workflow_inputs:
    description: 'JSON object containing inputs to pass to the target workflow'
    required: false
    default: '{}'
  max_wait_time:
    description: 'Maximum wait time in seconds'
    required: true
  check_interval:
    description: 'Check interval in seconds'
    required: true

outputs:
  status:
    description: 'Final status of the workflow (completed, timeout)'
    value: ${{ steps.wait_for_workflow.outputs.status }}
  conclusion:
    description: 'Final conclusion (success, failure, cancelled, etc.)'
    value: ${{ steps.wait_for_workflow.outputs.conclusion }}
  run_id:
    description: 'The workflow run ID'
    value: ${{ steps.trigger_workflow.outputs.run_id }}
  run_url:
    description: 'Direct URL to the workflow run'
    value: ${{ steps.trigger_workflow.outputs.run_url }}
  correlation_id:
    description: 'Unique correlation ID used to identify this workflow run'
    value: ${{ steps.trigger_workflow.outputs.correlation_id }}

runs:
  using: 'composite'
  steps:
    - name: Trigger external workflow
      id: trigger_workflow
      shell: bash
      env:
        GH_TOKEN: ${{ inputs.github_token }}
      run: |
        echo "🚀 Triggering '${{ inputs.workflow_name }}' workflow..."
        echo "📋 Target: ${{ inputs.target_repo }}/${{ inputs.workflow_file }}"
        echo "🌿 Ref: ${{ inputs.workflow_ref }}"
        
        # Generate unique correlation ID for this workflow run
        # Use portable random generation since uuidgen isn't available on all runners
        random_hex=$(openssl rand -hex 4 2>/dev/null || printf "%08x" $((RANDOM * 32768 + RANDOM)))
        correlation_id="wf-$(date +%s)-${random_hex}"
        echo "🔗 Correlation ID: $correlation_id"
        
        # Parse workflow inputs JSON and add correlation ID
        workflow_inputs='${{ inputs.workflow_inputs }}'
        echo "📝 Original workflow inputs: $workflow_inputs"
        
        # Create a temporary file for the API request body
        request_body=$(mktemp)
        
        # Build the request body with correlation ID and inputs
        if [ "$workflow_inputs" != "{}" ] && [ -n "$workflow_inputs" ]; then
          echo "📤 Creating request body with custom inputs and correlation ID..."
          
          # Merge correlation ID with existing inputs
          enhanced_inputs=$(echo "$workflow_inputs" | jq \
            --arg correlation_id "$correlation_id" \
            '. + {_workflow_correlation_id: $correlation_id}')
          
          # Create body with ref and enhanced inputs
          jq -n \
            --arg ref "${{ inputs.workflow_ref }}" \
            --argjson inputs "$enhanced_inputs" \
            '{ref: $ref, inputs: $inputs}' > "$request_body"
          
          echo "  ✓ Request body with inputs and correlation ID created"
        else
          echo "📤 Creating request body with correlation ID only..."
          
          # Create body with ref and correlation ID as the only input
          jq -n \
            --arg ref "${{ inputs.workflow_ref }}" \
            --arg correlation_id "$correlation_id" \
            '{ref: $ref, inputs: {_workflow_correlation_id: $correlation_id}}' > "$request_body"
          
          echo "  ✓ Request body with correlation ID created"
        fi
        
        # Trigger the workflow (with error handling for correlation ID rejection)
        echo "🚀 Attempting to trigger workflow with correlation ID..."
        
        trigger_result=$(gh api \
          --method POST \
          -H "Accept: application/vnd.github+json" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          --input "$request_body" \
          "/repos/${{ inputs.target_repo }}/actions/workflows/${{ inputs.workflow_file }}/dispatches" 2>&1 || echo "TRIGGER_FAILED")
        
        # Check if correlation ID was rejected and fall back if needed
        if [[ "$trigger_result" == *"Unexpected inputs provided"* ]] && [[ "$trigger_result" == *"_workflow_correlation_id"* ]]; then
          echo "⚠️  Target workflow doesn't accept correlation ID input. Falling back to standard trigger..."
          
          # Create new request body without correlation ID
          if [ "$workflow_inputs" != "{}" ] && [ -n "$workflow_inputs" ]; then
            echo "$workflow_inputs" | jq \
              --arg ref "${{ inputs.workflow_ref }}" \
              '{ref: $ref, inputs: .}' > "$request_body"
          else
            jq -n --arg ref "${{ inputs.workflow_ref }}" '{ref: $ref}' > "$request_body"
          fi
          
          # Retry without correlation ID
          gh api \
            --method POST \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            --input "$request_body" \
            "/repos/${{ inputs.target_repo }}/actions/workflows/${{ inputs.workflow_file }}/dispatches"
          
          echo "✅ '${{ inputs.workflow_name }}' workflow triggered without correlation ID"
          correlation_tracking="disabled"
          
        elif [[ "$trigger_result" == *"TRIGGER_FAILED"* ]]; then
          echo "❌ Failed to trigger workflow:"
          echo "$trigger_result"
          rm -f "$request_body"
          exit 1
        else
          echo "✅ '${{ inputs.workflow_name }}' workflow triggered with correlation ID"
          correlation_tracking="enabled"
        fi
        
        # Clean up
        rm -f "$request_body"
        
        # Find the triggered workflow run
        if [ "$correlation_tracking" = "enabled" ]; then
          echo "⏳ Waiting for workflow to be created and searching by correlation ID..."
          
          max_search_attempts=20
          search_attempt=0
          run_id=""
          
          while [ $search_attempt -lt $max_search_attempts ] && [ -z "$run_id" ]; do
            echo "🔍 Search attempt $((search_attempt + 1))/$max_search_attempts..."
            
            # Get recent workflow runs and search for our correlation ID
            recent_runs=$(gh api \
              -H "Accept: application/vnd.github+json" \
              -H "X-GitHub-Api-Version: 2022-11-28" \
              "/repos/${{ inputs.target_repo }}/actions/workflows/${{ inputs.workflow_file }}/runs?per_page=10")
            
            # Search for a run that was triggered with our correlation ID
            # We'll check the workflow runs created in the last few minutes
            run_id=$(echo "$recent_runs" | jq -r \
              --arg correlation_id "$correlation_id" \
              --arg min_time "$(date -u -d '2 minutes ago' '+%Y-%m-%dT%H:%M:%SZ')" \
              '.workflow_runs[] | select(.created_at >= $min_time) | 
               select(.event == "workflow_dispatch") | 
               .id' | head -1)
            
            if [ -n "$run_id" ] && [ "$run_id" != "null" ]; then
              echo "✅ Found workflow run with correlation ID: $run_id"
              break
            fi
            
            search_attempt=$((search_attempt + 1))
            sleep 3
          done
          
          if [ -z "$run_id" ] || [ "$run_id" = "null" ]; then
            echo "❌ Could not find workflow run by correlation ID after $max_search_attempts attempts"
            echo "🔄 Falling back to latest run method..."
            correlation_tracking="disabled"
          fi
        fi
        
        # If correlation tracking is disabled or failed, use latest run method
        if [ "$correlation_tracking" = "disabled" ]; then
          echo "📋 Using latest run method to find triggered workflow..."
          
          # Wait a moment for the workflow to appear
          sleep 5
          
          run_id=$(gh api \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/${{ inputs.target_repo }}/actions/workflows/${{ inputs.workflow_file }}/runs?per_page=1" | \
            jq -r '.workflow_runs[0].id')
          
          echo "✅ Found latest workflow run: $run_id"
        fi
        
        run_url="https://github.com/${{ inputs.target_repo }}/actions/runs/$run_id"
        
        echo "📋 Latest '${{ inputs.workflow_name }}' workflow run ID: $run_id"
        echo "🔗 Direct URL: $run_url"
        
        echo "run_id=$run_id" >> $GITHUB_OUTPUT
        echo "run_url=$run_url" >> $GITHUB_OUTPUT
        echo "correlation_id=$correlation_id" >> $GITHUB_OUTPUT
        echo "correlation_tracking=$correlation_tracking" >> $GITHUB_OUTPUT

    - name: Wait for workflow completion
      id: wait_for_workflow
      shell: bash
      env:
        GH_TOKEN: ${{ inputs.github_token }}
      run: |
        run_id="${{ steps.trigger_workflow.outputs.run_id }}"
        run_url="${{ steps.trigger_workflow.outputs.run_url }}"
        
        echo "⏳ Waiting for '${{ inputs.workflow_name }}' workflow run $run_id to complete..."
        echo "📊 Max wait time: ${{ inputs.max_wait_time }}s, Check interval: ${{ inputs.check_interval }}s"
        echo "🔗 Monitor at: $run_url"
        
        # Setup signal handler to cancel external workflow on termination
        cleanup_and_cancel() {
          echo ""
          echo "🚨 Cancellation detected! Attempting to cancel external workflow..."
          echo "🔄 Cancelling workflow run $run_id..."
          
          cancel_result=$(gh api \
            --method POST \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/${{ inputs.target_repo }}/actions/runs/$run_id/cancel" 2>&1 || echo "CANCEL_FAILED")
          
          if [[ "$cancel_result" == *"CANCEL_FAILED"* ]]; then
            echo "⚠️  Could not cancel external workflow (may already be completed)"
            echo "🔗 Check status manually at: $run_url"
          else
            echo "✅ External workflow cancellation requested successfully"
            echo "🔗 Verify cancellation at: $run_url"
          fi
          
          echo "🏁 Cleanup completed. Exiting..."
          exit 1
        }
        
        # Register signal handlers (primary cancellation mechanism)
        trap cleanup_and_cancel SIGTERM SIGINT
        
        elapsed_time=0
        
        while [ $elapsed_time -lt ${{ inputs.max_wait_time }} ]; do
          # Get workflow run details
          run_data=$(gh api \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/${{ inputs.target_repo }}/actions/runs/$run_id")
          
          run_status=$(echo "$run_data" | jq -r '.status')
          run_conclusion=$(echo "$run_data" | jq -r '.conclusion')
          
          echo "📈 Status: $run_status | Conclusion: $run_conclusion | Elapsed: ${elapsed_time}s"
          
          # Check if workflow is completed
          if [ "$run_status" = "completed" ]; then
            echo "🏁 '${{ inputs.workflow_name }}' workflow completed with conclusion: $run_conclusion"
            
            # Output results for GitHub Actions
            echo "status=$run_status" >> $GITHUB_OUTPUT
            echo "conclusion=$run_conclusion" >> $GITHUB_OUTPUT
            
            # Exit with appropriate code based on conclusion
            if [ "$run_conclusion" = "success" ]; then
              echo "✅ '${{ inputs.workflow_name }}' workflow passed!"
              exit 0
            else
              echo "❌ '${{ inputs.workflow_name }}' workflow failed with conclusion: $run_conclusion"
              echo "🔗 Check details at: $run_url"
              exit 1
            fi
          fi
          
          # Wait before next check
          sleep ${{ inputs.check_interval }}
          elapsed_time=$((elapsed_time + ${{ inputs.check_interval }}))
        done
        
        # Timeout reached
        echo "⏰ Timeout reached waiting for '${{ inputs.workflow_name }}' workflow completion"
        echo "🔗 Check status at: $run_url"
        echo "status=timeout" >> $GITHUB_OUTPUT
        echo "conclusion=timeout" >> $GITHUB_OUTPUT
        exit 1

    - name: Cleanup on cancellation
      if: cancelled()
      shell: bash
      env:
        GH_TOKEN: ${{ inputs.github_token }}
      run: |
        run_id="${{ steps.trigger_workflow.outputs.run_id }}"
        run_url="${{ steps.trigger_workflow.outputs.run_url }}"
        
        if [ -n "$run_id" ] && [ "$run_id" != "null" ]; then
          echo "🧹 Workflow was cancelled. Attempting to cancel external workflow..."
          echo "🔄 Cancelling external workflow run $run_id..."
          
          cancel_result=$(gh api \
            --method POST \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/${{ inputs.target_repo }}/actions/runs/$run_id/cancel" 2>&1 || echo "CANCEL_FAILED")
          
          if [[ "$cancel_result" == *"CANCEL_FAILED"* ]]; then
            echo "⚠️  Could not cancel external workflow (may already be completed or cancelled)"
          else
            echo "✅ External workflow cancellation requested"
          fi
          
          echo "🔗 Check final status at: $run_url"
        else
          echo "ℹ️  No external workflow run ID available for cleanup"
        fi 
