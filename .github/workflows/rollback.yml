name: Rollback Production Deployment

on:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  id-token: write # allows the JWT to be requested from GitHub's OIDC provider
  contents: write # Changed to write to allow creating/updating releases

env:
  NODE_OPTIONS: "--no-warnings"
  CACHE_CONTROL_NO_STORE: "\"no-store\""
  CACHE_CONTROL_MAX_AGE: "\"max-age=3600\""
  LEGACY_DIR_NAME: "legacy"
  MODERN_DIR_NAME: "modern"
  INTEGRATIONS_DIR_NAME: "js-integrations"
  PLUGINS_DIR_NAME: "plugins"
  LATEST_VERSION_DIR_NAME: "v3"
  LEGACY_SDK_LATEST_VERSION_DIR_NAME: "v1.1"

jobs:
  validate-actor:
    # Only allow to be deployed from tags and main branch
    if: startsWith(github.ref, 'refs/tags/v') || github.ref == 'refs/heads/main'
    uses: ./.github/workflows/validate-actor.yml
    with:
      team_names: 'js-sdk,integrations'
    secrets:
      PAT: ${{ secrets.PAT }}

  get-rollback-inputs:
    name: Get Rollback Inputs
    runs-on: [self-hosted, Linux, X64]
    outputs:
      trigger_source: ${{ steps.set-outputs.outputs.trigger_source }}
    steps:
      - name: Set outputs
        id: set-outputs
        run: echo "trigger_source=${{ format('Triggered from <{0}|{1}> by <{2}|{3}>', github.ref, github.ref_name, format('{0}/{1}', github.server_url, github.actor), github.actor) }}" >> $GITHUB_OUTPUT

  rollback:
    needs: [validate-actor, get-rollback-inputs]
    name: Rollback production deployment
    runs-on: [self-hosted, Linux, X64]
    steps:
      - name: Install AWS CLI
        uses: unfor19/install-aws-cli-action@2ff7a6968c81b173eaaef188869215870f44902b  # master

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df  # v4.2.1
        with:
          role-to-assume: arn:aws:iam::${{ secrets.AWS_PROD_ACCOUNT_ID }}:role/${{ secrets.AWS_PROD_S3_SYNC_ROLE }}
          aws-region: us-east-1

      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          ref: main # Production branch

      - name: Get current sdk versions
        run: |
          current_version=$(jq -r .version packages/analytics-js/package.json)
          echo "CURRENT_VERSION_VALUE=$current_version" >> $GITHUB_ENV
          echo "SDK current version: $current_version"

          legacy_sdk_current_version=$(jq -r .version packages/analytics-v1.1/package.json)
          echo "LEGACY_SDK_CURRENT_VERSION_VALUE=$legacy_sdk_current_version" >> $GITHUB_ENV
          echo "Legacy SDK current version: $legacy_sdk_current_version"

      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.sha }}

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Get rollback sdk versions
        run: |
          rollback_version=$(jq -r .version packages/analytics-js/package.json)
          echo "ROLLBACK_VERSION_VALUE=$rollback_version" >> $GITHUB_ENV
          echo "SDK rollback version: $rollback_version"

          legacy_sdk_rollback_version=$(jq -r .version packages/analytics-v1.1/package.json)
          echo "LEGACY_SDK_ROLLBACK_VERSION_VALUE=$legacy_sdk_rollback_version" >> $GITHUB_ENV
          echo "Legacy SDK rollback version: $legacy_sdk_rollback_version"

          echo "DATE=$(date)" >> $GITHUB_ENV

      - name: Copy the core SDK artifacts from the previous version to the latest version directory
        run: |
          s3_path_prefix="s3://${{ secrets.AWS_PROD_S3_BUCKET_NAME }}"

          # Copy from S3 bucket versioned directory to the same S3 bucket in the latest version directory
          # excluding the plugins and js-integrations directories
          # as the core SDK automatically refers to the plugins and js-integrations files from the versioned directory

          aws s3 cp $s3_path_prefix/${{ env.ROLLBACK_VERSION_VALUE }}/${{ env.LEGACY_DIR_NAME }}/ $s3_path_prefix/${{ env.LATEST_VERSION_DIR_NAME }}/${{ env.LEGACY_DIR_NAME }}/ --recursive --exclude "${{ env.INTEGRATIONS_DIR_NAME }}/*" --exclude "${{ env.INTEGRATIONS_DIR_NAME }}/**" --cache-control ${{ env.CACHE_CONTROL_NO_STORE }}

          aws s3 cp $s3_path_prefix/${{ env.ROLLBACK_VERSION_VALUE }}/${{ env.MODERN_DIR_NAME }}/ $s3_path_prefix/${{ env.LATEST_VERSION_DIR_NAME }}/${{ env.MODERN_DIR_NAME }}/ --recursive --exclude "${{ env.PLUGINS_DIR_NAME }}/*" --exclude "${{ env.PLUGINS_DIR_NAME }}/**" --exclude "${{ env.INTEGRATIONS_DIR_NAME }}/*" --exclude "${{ env.INTEGRATIONS_DIR_NAME }}/**" --cache-control ${{ env.CACHE_CONTROL_NO_STORE }}

      - name: Invalidate CloudFront cache for the latest version directory
        run: |
          invalidation_id=$(AWS_MAX_ATTEMPTS=10 aws cloudfront create-invalidation --distribution-id ${{ secrets.AWS_PROD_CF_DISTRIBUTION_ID }} --paths "/${{ env.LATEST_VERSION_DIR_NAME }}*" --query "Invalidation.Id" --output text)

          aws cloudfront wait invalidation-completed --distribution-id ${{ secrets.AWS_PROD_CF_DISTRIBUTION_ID }} --id "$invalidation_id"

      # Repeat the above steps for the legacy SDK artifacts
      - name: Copy the legacy SDK artifacts from the previous version to the latest version directory
        run: |
          s3_path_prefix="s3://${{ secrets.AWS_PROD_S3_BUCKET_NAME }}"

          # Copy from S3 bucket versioned directory to the same S3 bucket in the latest version directory
          aws s3 cp $s3_path_prefix/${{ env.LEGACY_SDK_ROLLBACK_VERSION_VALUE }}/ $s3_path_prefix/${{ env.LEGACY_SDK_LATEST_VERSION_DIR_NAME }}/ --recursive --cache-control ${{ env.CACHE_CONTROL_NO_STORE }}

      - name: Invalidate CloudFront cache for the latest version directory (legacy SDK artifacts)
        run: |
          invalidation_id=$(AWS_MAX_ATTEMPTS=10 aws cloudfront create-invalidation --distribution-id ${{ secrets.AWS_PROD_CF_DISTRIBUTION_ID }} --paths "/${{ env.LEGACY_SDK_LATEST_VERSION_DIR_NAME }}*" --query "Invalidation.Id" --output text)

          aws cloudfront wait invalidation-completed --distribution-id ${{ secrets.AWS_PROD_CF_DISTRIBUTION_ID }} --id "$invalidation_id"

      - name: Mark the rollback version as the latest GitHub release
        uses: actions/github-script@v7
        continue-on-error: true
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const rollbackVersion = process.env.LEGACY_SDK_ROLLBACK_VERSION_VALUE;
            const releaseTag = `@rudderstack/analytics-js@${rollbackVersion}`;
            
            console.log(`Marking ${releaseTag} as the latest release on GitHub`);
            
            // Find the release for the rollback version
            const releases = await github.rest.repos.listReleases({
              owner: context.repo.owner,
              repo: context.repo.repo,
            });
            
            const rollbackRelease = releases.data.find(release => 
              release.tag_name === releaseTag || 
              release.name === releaseTag
            );
            
            if (!rollbackRelease) {
              console.log(`Release with tag ${releaseTag} not found`);
              return;
            }
            
            // Update the release to set it as the latest
            await github.rest.repos.updateRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: rollbackRelease.id,
              make_latest: true
            });
            
            console.log(`Successfully marked ${releaseTag} as the latest release`);

      - name: Deprecate the rolled back NPM version
        continue-on-error: true
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          NPM_CONFIG_PROVENANCE: true
        run: |
          VERSION_TO_DEPRECATE="${{ env.CURRENT_VERSION_VALUE }}"
          echo "Deprecating NPM version: $VERSION_TO_DEPRECATE"
          
          # Deprecate the specified version with a message
          npm set //registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}
          npm deprecate "@rudderstack/analytics-js@$VERSION_TO_DEPRECATE" "We've identified issues with this version of the package. Please switch to v${{ env.ROLLBACK_VERSION_VALUE }} or the latest version if available."

          # Note: The legacy SDK is not deprecated as it is deprecated by default with every release.
          # Refer to deploy-npm.yml for more details.

      - name: Send message to Slack channel
        id: slack
        continue-on-error: true
        uses: slackapi/slack-github-action@b0fa283ad8fea605de13dc3f449259339835fc52  # v2.1.0
        env:
          PROJECT_NAME: 'JS SDK Browser Package - Rollback - Production'
          CDN_URL: ${{ format('https://cdn.rudderlabs.com/{0}/modern/rsa.min.js', env.LATEST_VERSION_DIR_NAME) }}
          RELEASES_URL: 'https://github.com/rudderlabs/rudder-sdk-js/releases/tag/@rudderstack/analytics-js@'
          LINK_TEXT: ${{ format('v{0}', env.ROLLBACK_VERSION_VALUE) }}
          GITHUB_RUN_URL: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
          ACTOR_URL: ${{ format('{0}/{1}', github.server_url, github.actor) }}
          ACTOR: ${{ github.actor }}
        with:
          method: 'chat.postMessage'
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          retries: rapid
          payload-templated: true
          payload: |
            {
              "channel": "${{ secrets.SLACK_RELEASE_CHANNEL_ID }}",
              "text": "*:rocket: Deployment - ${{ env.PROJECT_NAME }} - <${{ env.CDN_URL }}|${{ env.LINK_TEXT }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SW7DM8P3> <!subteam^S03SHJ20350>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rocket: Deployment - ${{ env.PROJECT_NAME }}"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{ env.CDN_URL }}|${{ env.LINK_TEXT }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SW7DM8P3> <!subteam^S03SHJ20350>"
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/javascript/javascript.png",
                    "alt_text": "JavaScript Icon"
                  }
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": ":boom: ${{ needs.get-rollback-inputs.outputs.trigger_source }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": ":gear: <${{ env.GITHUB_RUN_URL }}|View workflow run details>"
                    }${{ format(',{{"type": "mrkdwn", "text": ":book: <{0}{1}|View release notes>"}}', env.RELEASES_URL, env.ROLLBACK_VERSION_VALUE) || ''  }}
                  ]
                }
              ]
            }

  deploy-sanity-suite:
    name: Deploy sanity suite
    needs: [get-rollback-inputs]
    uses: ./.github/workflows/deploy-sanity-suite.yml
    with:
      environment: 'production'
      trigger_source: ${{ needs.get-rollback-inputs.outputs.trigger_source }}
    secrets:
      AWS_ACCOUNT_ID: ${{ secrets.AWS_PROD_ACCOUNT_ID }}
      AWS_S3_BUCKET_NAME: ${{ secrets.AWS_PROD_S3_BUCKET_NAME }}
      AWS_S3_SYNC_ROLE: ${{ secrets.AWS_PROD_S3_SYNC_ROLE }}
      AWS_CF_DISTRIBUTION_ID: ${{ secrets.AWS_PROD_CF_DISTRIBUTION_ID }}
      SANITY_SUITE_WRITE_KEY: ${{ secrets.SANITY_SUITE_PROD_WRITE_KEY }}
      SANITY_SUITE_DATAPLANE_URL: ${{ secrets.SANITY_SUITE_PROD_DATAPLANE_URL }}
      SANITY_SUITE_CONFIG_SERVER_HOST: ${{ secrets.SANITY_SUITE_PROD_CONFIG_SERVER_HOST }}
      BUGSNAG_API_KEY: ${{ secrets.RS_PROD_BUGSNAG_API_KEY }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      SLACK_RELEASE_CHANNEL_ID: ${{ secrets.SLACK_RELEASE_CHANNEL_ID_NON_PROD }}

  run-e2e-regression-test-suites:
    uses: ./.github/workflows/run-e2e-regression-test-suites.yml
    name: Run E2E Regression Test Suites
    needs: [get-rollback-inputs, rollback, deploy-sanity-suite]
    with:
      environment: production
      trigger_source: ${{ needs.get-rollback-inputs.outputs.trigger_source }}
    secrets:
      PAT: ${{ secrets.PAT }}
