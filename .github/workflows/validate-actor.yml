name: Validate Actor

on:
  workflow_call:
    secrets:
      PAT:
        required: true
    inputs:
      team_names:
        description: 'Comma-separated list of team names'
        type: string
        default: 'js-sdk'

jobs:
  validate-actor:
    runs-on: [self-hosted, Linux, X64]
    steps:
      - name: Validate if actor is allowed to trigger the workflow
        env:
          ORG_NAME: rudderlabs
          TEAM_NAMES: ${{ inputs.team_names }}
          GH_TOKEN: ${{ secrets.PAT }}
        run: |
          actor=${{ github.actor || github.triggering_actor }}
          allowed=false

          # Split TEAM_NAMES into an array and loop through each team
          IFS=',' read -ra TEAMS <<< "$TEAM_NAMES"

          for TEAM_NAME in "${TEAMS[@]}"; do
            echo "Checking membership for actor '$actor' in team '@${ORG_NAME}/${TEAM_NAME}'..."

            # Use gh api to fetch the membership details
            response=$(gh api "/orgs/$ORG_NAME/teams/$TEAM_NAME/memberships/$actor" -H "X-GitHub-Api-Version: 2022-11-28" 2>/dev/null || echo "error")

            # Check if API request failed
            if [ "$response" = "error" ]; then
              echo "Error: Failed to fetch membership details for '$actor' in team '$TEAM_NAME'. Please check if:"
              echo "1. The team name '$TEAM_NAME' exists in the organization"
              echo "2. The GitHub token has sufficient permissions"
              echo "3. The GitHub API is accessible"
              continue
            fi

            # Check if the actor is an active member
            if echo "$response" | grep -q '"state":"active"'; then
              echo "'$actor' is an active member of '@${ORG_NAME}/${TEAM_NAME}' team."
              allowed=true
              break
            fi
          done

          # Final validation result
          if [ "$allowed" = true ]; then
            echo "'$actor' is allowed to trigger the workflow."
          else
            echo "Error: '$actor' is NOT a member of any of the specified teams '${TEAM_NAMES}'. Access denied."
            exit 1
          fi
