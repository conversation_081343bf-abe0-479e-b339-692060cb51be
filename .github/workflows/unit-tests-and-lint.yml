name: Unit Tests and Lint

on:
  push:
    branches: ['main', 'develop']
  pull_request:
    branches: ['main', 'develop', 'hotfix/*']
    types: ['opened', 'reopened', 'synchronize']

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_OPTIONS: '--no-warnings'
  BASE_REF: ${{ github.event.pull_request.base.sha || 'HEAD' }}

jobs:
  test:
    name: Unit Tests and Lint
    runs-on: [self-hosted, Linux, X64]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha || github.ref }}

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Setup workspace
        env:
          HUSKY: 0
        run: |
          npm run setup:deps

      - name: Check for affected projects
        id: check_affected
        run: |
          AFFECTED_PROJECTS=$(npx nx show projects --affected --base=$BASE_REF | tr '\n' ',')
          echo "Affected projects: $AFFECTED_PROJECTS"
          echo "affected_projects=$AFFECTED_PROJECTS" >> $GITHUB_OUTPUT

      - name: Execute unit tests
        run: |
          npm run test:ci

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24  # v5.4.3
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        with:
          files: ./packages/analytics-js/reports/coverage/clover.xml,./packages/analytics-js-common/reports/coverage/clover.xml,./packages/analytics-js-integrations/reports/coverage/clover.xml,./packages/analytics-js-plugins/reports/coverage/clover.xml,./packages/analytics-js-service-worker/reports/coverage/clover.xml,./packages/analytics-v1.1/reports/coverage/clover.xml,./packages/analytics-js-cookies/reports/coverage/clover.xml

      - name: Execute linting check
        run: |
          npm run check:lint:ci

      - name: Fix filesystem paths in generated reports
        run: |
          ./scripts/fix-reports-path-in-github-runner.sh

      - name: SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@2500896589ef8f7247069a56136f8dc177c27ccf  # v5.2.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
