name: Deploy to NPM

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      is_called:
        type: string
        required: true
      base_version:
        type: string
        required: false
      head_version:
        type: string
        required: false
      version_suffix:
        type: string
        required: false
      bugsnag_release_stage:
        description: 'Bugsnag release stage'
        type: string
        required: false
      environment:
        type: string
        required: false
        default: 'production'
      trigger_source:
        description: 'Description of what/who triggered the workflow chain'
        type: string
        required: false
      monorepo_release_version:
        description: 'Monorepo release version for artifact lookup'
        type: string
        required: false
      release_ticket_id:
        description: 'Release ticket for artifact lookup'
        type: string
        required: false
    secrets:
      RS_PROD_BUGSNAG_API_KEY:
        required: true
      NPM_TOKEN:
        required: true
      SLACK_BOT_TOKEN:
        required: true
      SLACK_RELEASE_CHANNEL_ID:
        required: true

permissions:
  id-token: write # Allows the JWT to be requested from GitHub's OIDC provider
  contents: read # Required for actions/checkout

env:
  NODE_OPTIONS: '--no-warnings'

jobs:
  deploy:
    name: Deploy to NPM
    # As we publish the NPM package with provenance, we must use GitHub-hosted runners
    runs-on: ubuntu-latest
    if: ${{ inputs.is_called == 'true' || (github.event_name == 'workflow_dispatch' && startsWith(github.ref, 'refs/tags/v')) }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.sha }}

      - name: Modify package versions
        if: ${{ inputs.version_suffix != '' }}
        run: |
          CURRENT_VERSION_JS=$(jq -r .version packages/analytics-js/package.json)
          CURRENT_VERSION_V1=$(jq -r .version packages/analytics-v1.1/package.json)
          
          # Append the suffix to create new versions
          NEW_VERSION_JS="${CURRENT_VERSION_JS}-${{ inputs.version_suffix }}"
          NEW_VERSION_V1="${CURRENT_VERSION_V1}-${{ inputs.version_suffix }}"
          
          echo "Updating versions:"
          echo "analytics-js: $CURRENT_VERSION_JS -> $NEW_VERSION_JS"
          echo "analytics-v1.1: $CURRENT_VERSION_V1 -> $NEW_VERSION_V1"
          
          # Update all package.json files using jq
          TMP_FILE_JS=$(mktemp)
          jq ".version = \"$NEW_VERSION_JS\"" packages/analytics-js/package.json > "$TMP_FILE_JS" && mv "$TMP_FILE_JS" packages/analytics-js/package.json
          TMP_FILE_V1=$(mktemp)
          jq ".version = \"$NEW_VERSION_V1\"" packages/analytics-v1.1/package.json > "$TMP_FILE_V1" && mv "$TMP_FILE_V1" packages/analytics-v1.1/package.json

      - name: Get new version number
        run: |
          current_version_v1=$(jq -r .version packages/analytics-v1.1/package.json)
          current_version_sw=$(jq -r .version packages/analytics-js-service-worker/package.json)
          current_version_cookie_utils=$(jq -r .version packages/analytics-js-cookies/package.json)
          current_version=$(jq -r .version packages/analytics-js/package.json)
          echo "CURRENT_VERSION_V1_VALUE=$current_version_v1" >> $GITHUB_ENV
          echo "CURRENT_VERSION_SW_VALUE=$current_version_sw" >> $GITHUB_ENV
          echo "CURRENT_VERSION_COOKIE_UTILS_VALUE=$current_version_cookie_utils" >> $GITHUB_ENV
          echo "CURRENT_VERSION_VALUE=$current_version" >> $GITHUB_ENV

      - name: Get versions in NPM
        run: |
          # Get latest version across all tags (most recently published)
          current_npm_version=$(npm view @rudderstack/analytics-js time --json 2>/dev/null | jq -r 'to_entries | map(select(.key != "modified" and .key != "created")) | sort_by(.value) | last.key' || echo "not found")
          echo "CURRENT_NPM_VERSION=$current_npm_version" >> $GITHUB_ENV

          current_npm_version_sw=$(npm view @rudderstack/analytics-js-service-worker time --json 2>/dev/null | jq -r 'to_entries | map(select(.key != "modified" and .key != "created")) | sort_by(.value) | last.key' || echo "not found")
          echo "CURRENT_NPM_VERSION_SW=$current_npm_version_sw" >> $GITHUB_ENV
          
          current_npm_version_cookie_utils=$(npm view @rudderstack/analytics-js-cookies time --json 2>/dev/null | jq -r 'to_entries | map(select(.key != "modified" and .key != "created")) | sort_by(.value) | last.key' || echo "not found")
          echo "CURRENT_NPM_VERSION_COOKIE_UTILS=$current_npm_version_cookie_utils" >> $GITHUB_ENV

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Setup workspace
        env:
          HUSKY: 0
          REMOTE_MODULES_BASE_PATH: 'https://cdn.rudderlabs.com/v3/modern/plugins'
          BUGSNAG_API_KEY: ${{ secrets.RS_PROD_BUGSNAG_API_KEY }}
          LOCK_DEPS_VERSION: ${{ inputs.environment == 'production' && 'true' || 'false' }}
          BUGSNAG_RELEASE_STAGE: ${{ inputs.bugsnag_release_stage || 'production' }}
        run: |
          npm run setup:ci

      - name: Build release artifacts
        env:
          BUGSNAG_API_KEY: ${{ secrets.RS_PROD_BUGSNAG_API_KEY }}
          BUGSNAG_RELEASE_STAGE: ${{ inputs.bugsnag_release_stage || 'production' }}
          LOCK_DEPS_VERSION: ${{ inputs.environment == 'production' && 'true' || 'false' }}
        run: |
          npm run build:package
          npm run build:package:modern

      - name: Get the monorepo versions for the base and head versions
        run: |
          # If both base and head versions are provided, use them
          if [ -n "${{ inputs.base_version }}" ] && [ -n "${{ inputs.head_version }}" ]; then
            LAST_VERSION=${{ inputs.base_version }}
            CURRENT_VERSION=${{ inputs.head_version }}
          else
            # Otherwise, get the two latest monorepo versions
            CURRENT_VERSION=$(git tag -l "v3*" --sort=-version:refname | head -n 1)
            LAST_VERSION=$(git tag -l "v3*" --sort=-version:refname | head -n 2 | awk 'NR == 2 { print $1 }')
          fi

          echo "Current version: $CURRENT_VERSION"
          echo "Previous version: $LAST_VERSION"

          echo "current_monorepo_version=$(echo $CURRENT_VERSION)" >> $GITHUB_ENV
          echo "last_monorepo_version=$(echo $LAST_VERSION)" >> $GITHUB_ENV
          echo "DATE=$(date)" >> $GITHUB_ENV

      - name: Publish package to NPM
        env:
          HUSKY: 0
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          NPM_CONFIG_PROVENANCE: true
        run: |
          # Remove unnecessary fields from package.json before publishing
          ./scripts/make-package-json-publish-ready.sh

          npm set //registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}
          npx nx release publish --base=${{ env.last_monorepo_version }} --head=${{ env.current_monorepo_version }} ${{ inputs.environment == 'beta' && '--tag=beta' || '' }}

          # Reset the changes made to package.json
          git reset --hard
          git clean -fd

      - name: Wait for NPM propagation
        run: |
          # It has been observed that the NPM package is not immediately available after publish.
          echo "Waiting for NPM propagation..."
          sleep 15

      - name: Get versions in NPM after publish
        run: |
          npm cache clean --force
          
          # Get latest version across all tags (most recently published)
          new_npm_version=$(npm view @rudderstack/analytics-js time --json 2>/dev/null | jq -r 'to_entries | map(select(.key != "modified" and .key != "created")) | sort_by(.value) | last.key' || echo "not found")
          echo "NEW_NPM_VERSION=$new_npm_version" >> $GITHUB_ENV

          new_npm_version_sw=$(npm view @rudderstack/analytics-js-service-worker time --json 2>/dev/null | jq -r 'to_entries | map(select(.key != "modified" and .key != "created")) | sort_by(.value) | last.key' || echo "not found")
          echo "NEW_NPM_VERSION_SW=$new_npm_version_sw" >> $GITHUB_ENV

          new_npm_version_cookie_utils=$(npm view @rudderstack/analytics-js-cookies time --json 2>/dev/null | jq -r 'to_entries | map(select(.key != "modified" and .key != "created")) | sort_by(.value) | last.key' || echo "not found")
          echo "NEW_NPM_VERSION_COOKIE_UTILS=$new_npm_version_cookie_utils" >> $GITHUB_ENV

      - name: Debug environment variables
        continue-on-error: true
        run: |
          echo "CURRENT_NPM_VERSION=${{ env.CURRENT_NPM_VERSION }}"
          echo "NEW_NPM_VERSION=${{ env.NEW_NPM_VERSION }}"

          echo "CURRENT_NPM_VERSION_SW=${{ env.CURRENT_NPM_VERSION_SW }}"
          echo "NEW_NPM_VERSION_SW=${{ env.NEW_NPM_VERSION_SW }}"

          echo "CURRENT_NPM_VERSION_COOKIE_UTILS=${{ env.CURRENT_NPM_VERSION_COOKIE_UTILS }}"
          echo "NEW_NPM_VERSION_COOKIE_UTILS=${{ env.NEW_NPM_VERSION_COOKIE_UTILS }}"

      - name: Deprecate the legacy SDK NPM package
        continue-on-error: true
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          NPM_CONFIG_PROVENANCE: true
        run: |
          npm set //registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}

          npm deprecate rudder-sdk-js "This package is deprecated and no longer maintained. While your events are still being tracked and delivered, we strongly recommend you to migrate to the latest package, @rudderstack/analytics-js (https://www.npmjs.com/package/@rudderstack/analytics-js), for the latest features, security updates, and improved performance. For more details, visit the migration guide: https://www.rudderstack.com/docs/sources/event-streams/sdks/rudderstack-javascript-sdk/migration-guide/."


      - name: Send message to Slack channel
        if: env.CURRENT_NPM_VERSION != env.NEW_NPM_VERSION && env.NEW_NPM_VERSION != 'not found'
        id: slack
        continue-on-error: true
        uses: slackapi/slack-github-action@b0fa283ad8fea605de13dc3f449259339835fc52  # v2.1.0
        env:
          PROJECT_NAME: ${{ format('JS SDK NPM Package{0}', (inputs.environment == 'staging' && ' - Staging') || (inputs.environment == 'development' && ' - Development') || (inputs.environment == 'beta' && ' - Beta') || '') }}
          NPM_PACKAGE_URL: 'https://www.npmjs.com/package/@rudderstack/analytics-js'
          RELEASES_URL: 'https://github.com/rudderlabs/rudder-sdk-js/releases/tag/@rudderstack/analytics-js@'
          GITHUB_RUN_URL: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
          ACTOR_URL: ${{ format('{0}/{1}', github.server_url, github.actor) }}
          ACTOR: ${{ github.actor }}
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload-templated: true
          retries: rapid
          payload: |
            {
              "channel": "${{ secrets.SLACK_RELEASE_CHANNEL_ID }}",
              "text": "*:rocket: Published - ${{ env.PROJECT_NAME }} - <${{ env.NPM_PACKAGE_URL }}|${{ env.CURRENT_VERSION_VALUE }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SHJ20350>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rocket: Published - ${{ env.PROJECT_NAME }}"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{ env.NPM_PACKAGE_URL }}|v${{ env.CURRENT_VERSION_VALUE }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SW7DM8P3> <!subteam^S03SHJ20350>"
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://img.icons8.com/color/452/npm.png",
                    "alt_text": "NPM Icon"
                  }
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": ":package: ${{ (github.event_name == 'workflow_dispatch' && format('Published by <{0}|{1}>', env.ACTOR_URL, env.ACTOR)) || (inputs.trigger_source && inputs.trigger_source) }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": ":gear: <${{ env.GITHUB_RUN_URL }}|View workflow run details>"
                    }${{ inputs.environment == 'production' && format(',{{"type": "mrkdwn", "text": ":book: <{0}{1}|View release notes>"}}', env.RELEASES_URL, env.CURRENT_VERSION_VALUE) || ''  }}
                  ]
                }
              ]
            }

      - name: Post NPM packages publish notification to release thread
        continue-on-error: true
        if: ${{ steps.slack.outcome == 'success' && inputs.monorepo_release_version != '' && inputs.release_ticket_id != '' }}
        uses: ./.github/actions/release-thread-integration
        with:
          monorepo_release_version: ${{ inputs.monorepo_release_version }}
          release_ticket_id: ${{ inputs.release_ticket_id }}
          slack_bot_token: ${{ secrets.SLACK_BOT_TOKEN }}
          deployment_name: 'JS SDK NPM Package'
          slack_api_response: ${{ steps.slack.outputs.response }}

      - name: Send message to Slack channel for Service Worker
        if: env.CURRENT_NPM_VERSION_SW != env.NEW_NPM_VERSION_SW && env.NEW_NPM_VERSION_SW != 'not found'
        id: slackSw
        continue-on-error: true
        uses: slackapi/slack-github-action@b0fa283ad8fea605de13dc3f449259339835fc52  # v2.1.0
        env:
          PROJECT_NAME: ${{ format('JS SDK Service Worker NPM Package{0}', (inputs.environment == 'staging' && ' - Staging') || (inputs.environment == 'development' && ' - Development') || (inputs.environment == 'beta' && ' - Beta') || '') }}
          NPM_PACKAGE_URL: 'https://www.npmjs.com/package/@rudderstack/analytics-js-service-worker'
          RELEASES_URL: 'https://github.com/rudderlabs/rudder-sdk-js/releases/tag/@rudderstack/analytics-js-service-worker@'
          GITHUB_RUN_URL: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
          ACTOR_URL: ${{ format('{0}/{1}', github.server_url, github.actor) }}
          ACTOR: ${{ github.actor }}
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload-templated: true
          retries: rapid
          payload: |
            {
              "channel": "${{ secrets.SLACK_RELEASE_CHANNEL_ID }}",
              "text": "*:rocket: Published - ${{ env.PROJECT_NAME }} - <${{ env.NPM_PACKAGE_URL }}|${{ env.CURRENT_VERSION_SW_VALUE }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SHJ20350>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rocket: Published - ${{ env.PROJECT_NAME }}"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{ env.NPM_PACKAGE_URL }}|v${{ env.CURRENT_VERSION_SW_VALUE }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SW7DM8P3> <!subteam^S03SHJ20350>"
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://img.icons8.com/color/452/npm.png",
                    "alt_text": "NPM Icon"
                  }
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": ":package: ${{ (github.event_name == 'workflow_dispatch' && format('Published by <{0}|{1}>', env.ACTOR_URL, env.ACTOR)) || (inputs.trigger_source && inputs.trigger_source) }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": ":gear: <${{ env.GITHUB_RUN_URL }}|View workflow run details>"
                    }${{ inputs.environment == 'production' && format(',{{"type": "mrkdwn", "text": ":book: <{0}{1}|View release notes>"}}', env.RELEASES_URL, env.CURRENT_VERSION_SW_VALUE) || ''  }}
                  ]
                }
              ]
            }

      - name: Send message to Slack channel for cookie utilities
        if: env.CURRENT_NPM_VERSION_COOKIE_UTILS != env.NEW_NPM_VERSION_COOKIE_UTILS && env.NEW_NPM_VERSION_COOKIE_UTILS != 'not found'
        id: slack-cookie-utils
        continue-on-error: true
        uses: slackapi/slack-github-action@b0fa283ad8fea605de13dc3f449259339835fc52  # v2.1.0
        env:
          PROJECT_NAME: ${{ format('JS SDK Cookies Utilities{0}', (inputs.environment == 'staging' && ' - Staging') || (inputs.environment == 'development' && ' - Development') || (inputs.environment == 'beta' && ' - Beta') || '') }}
          NPM_PACKAGE_URL: 'https://www.npmjs.com/package/@rudderstack/analytics-js-cookies'
          RELEASES_URL: 'https://github.com/rudderlabs/rudder-sdk-js/releases/tag/@rudderstack/analytics-js-cookies@'
          GITHUB_RUN_URL: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
          ACTOR_URL: ${{ format('{0}/{1}', github.server_url, github.actor) }}
          ACTOR: ${{ github.actor }}
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload-templated: true
          retries: rapid
          payload: |
            {
              "channel": "${{ secrets.SLACK_RELEASE_CHANNEL_ID }}",
              "text": "*:rocket: Published - ${{ env.PROJECT_NAME }} - <${{ env.NPM_PACKAGE_URL }}|${{ env.CURRENT_VERSION_COOKIE_UTILS_VALUE }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SHJ20350>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rocket: Published - ${{ env.PROJECT_NAME }}"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{ env.NPM_PACKAGE_URL }}|v${{ env.CURRENT_VERSION_COOKIE_UTILS_VALUE }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SW7DM8P3> <!subteam^S03SHJ20350>"
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://img.icons8.com/color/452/npm.png",
                    "alt_text": "NPM Icon"
                  }
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": ":package: ${{ (github.event_name == 'workflow_dispatch' && format('Published by <{0}|{1}>', env.ACTOR_URL, env.ACTOR)) || (inputs.trigger_source && inputs.trigger_source) }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": ":gear: <${{ env.GITHUB_RUN_URL }}|View workflow run details>"
                    }${{ inputs.environment == 'production' && format(',{{"type": "mrkdwn", "text": ":book: <{0}{1}|View release notes>"}}', env.RELEASES_URL, env.CURRENT_VERSION_COOKIE_UTILS_VALUE) || ''  }}
                  ]
                }
              ]
            }
