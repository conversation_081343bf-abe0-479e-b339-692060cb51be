name: Publish New Release to GitHub

on:
  pull_request:
    branches:
      - main
    types:
      - closed

env:
  NODE_OPTIONS: '--no-warnings'

jobs:
  get-release-inputs:
    name: Get Release Inputs
    runs-on: [self-hosted, Linux, X64]
    outputs:
      trigger_source: ${{ steps.set-outputs.outputs.trigger_source }}
      release_version: ${{ steps.extract-release-info.outputs.release_version }}
      release_ticket_id: ${{ steps.extract-release-info.outputs.release_ticket_id }}
    steps:
      - name: Set outputs
        id: set-outputs
        run: echo "trigger_source=${{ format('PR <{0}|#{1}> merged by <{2}|{3}>', github.event.pull_request.html_url, github.event.pull_request.number, format('{0}/{1}', github.server_url, github.actor), github.actor) }}" >> $GITHUB_OUTPUT

      - name: Extract release info from branch
        id: extract-release-info
        if: startsWith(github.event.pull_request.head.ref, 'release/') || startsWith(github.event.pull_request.head.ref, 'hotfix-release/')
        run: |
          branch_name="${{ github.event.pull_request.head.ref }}"
          echo "Branch name: $branch_name"
          
          # Extract version and ticket from branch name (format: release/3.2.1-SDK-1234 or hotfix-release/3.2.2-SDK-5678)
          version=$(echo "$branch_name" | sed -n 's|.*\/\([0-9]\+\.[0-9]\+\.[0-9]\+\)-.*|\1|p')
          ticket=$(echo "$branch_name" | sed -n 's|.*-\([^-]*\)$|\1|p')
          
          echo "Extracted version: $version"
          echo "Extracted ticket: $ticket"
          
          echo "release_version=$version" >> $GITHUB_OUTPUT
          echo "release_ticket_id=$ticket" >> $GITHUB_OUTPUT

  release:
    name: Publish new release
    needs: [get-release-inputs]
    # For publishing GitHub release, we need to only use the GitHub hosted runners
    runs-on: ubuntu-latest
    # only merged pull requests must trigger this job
    if: >
      (startsWith(github.event.pull_request.head.ref, 'release/') ||
      startsWith(github.event.pull_request.head.ref, 'hotfix-release/')) &&
      github.event.pull_request.merged == true

    steps:
      - name: Extract version from branch name (for release branches)
        id: extract-version
        env:
          BRANCH_NAME_REF: ${{ github.event.pull_request.head.ref }}
        run: |
          BRANCH_NAME="${BRANCH_NAME_REF}"
          VERSION=${BRANCH_NAME#hotfix-}
          VERSION=${VERSION#release/}

          # Extract the ticket ID (case-insensitive match for -SDK-<ticket_number>)
          RELEASE_TICKET_ID=$(echo "$BRANCH_NAME" | grep -oE '[sS][dD][kK]-[0-9]+')

          echo "release_ticket_id=$RELEASE_TICKET_ID" >> $GITHUB_OUTPUT

          # Remove the -SDK-<ticket_number> suffix case insensitively
          VERSION=$(echo "$VERSION" | awk '{sub(/-[sS][dD][kK]-[0-9]+$/, ""); print}')

          echo "release_version=$VERSION" >> $GITHUB_OUTPUT

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.sha }}

      # In order to make a commit, we need to initialize a user.
      # You may choose to write something less generic here if you want, it doesn't matter functionality wise.
      - name: Initialize mandatory git config
        run: |
          git config user.name "GitHub actions"
          git config user.email <EMAIL>

      - name: Create monorepo release tag
        id: create_monorepo_release
        run: |
          git tag -a v${{ steps.extract-version.outputs.release_version }} -m "chore(monorepo): release v${{ steps.extract-version.outputs.release_version }}"
          git push origin refs/tags/v${{ steps.extract-version.outputs.release_version }}

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Setup workspace
        env:
          HUSKY: 0
        run: |
          npm run setup:deps

      - name: Get the two latest versions
        run: |
          CURRENT_VERSION=$(git tag -l "v3*" --sort=-version:refname | head -n 1)
          LAST_VERSION=$(git tag -l "v3*" --sort=-version:refname | head -n 2 | awk 'NR == 2 { print $1 }')
          echo "Current version: $CURRENT_VERSION"
          echo "Previous version: $LAST_VERSION"
          echo "current_monorepo_version=$(echo $CURRENT_VERSION)" >> $GITHUB_ENV
          echo "last_monorepo_version=$(echo $LAST_VERSION)" >> $GITHUB_ENV
          echo "DATE=$(date)" >> $GITHUB_ENV

      - name: Create GitHub releases
        id: create_release
        continue-on-error: true
        env:
          HUSKY: 0
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # The default behavior of Nx is to consider changes to package-lock.json
          # as impacting all the packages.
          # Here is a work around to ignore package-lock.json and package.json
          # files before publishing the releases.
          # Ref: https://github.com/nrwl/nx/issues/15116#issuecomment-1535260119
          echo package-lock.json >> .nxignore
          echo package.json >> .nxignore
          npm run release:github -- --base=${{ env.last_monorepo_version }} --head=${{ env.current_monorepo_version }}

          # Restore working copy
          git checkout -- .nxignore

      - name: Create pull request into develop
        if: always()
        continue-on-error: true
        env:
          GH_TOKEN: ${{ secrets.PAT }}
        run: |
          pr_title="chore(release): merge main into develop after release v${{ steps.extract-version.outputs.release_version }} [${{ steps.extract-version.outputs.release_ticket_id }}]"
          
          # Create PR body with proper formatting
          cat > pr_body.md << EOF
          :crown: **Automated Post-Release PR**

          This pull request was created automatically by the GitHub Actions workflow. It merges changes from the \`main\` branch into the \`develop\` branch after a release has been completed.

          This ensures that the \`develop\` branch stays up to date with all release-related changes from the \`main\` branch.

          ### Details
          - **Monorepo Release Version**: v${{ steps.extract-version.outputs.release_version }}
          - **Related Ticket**: [${{ steps.extract-version.outputs.release_ticket_id }}](https://linear.app/rudderstack/issue/${{ steps.extract-version.outputs.release_ticket_id }})

          ---
          Please review and merge it before closing the release ticket. :rocket:
          EOF
          
          # Create the PR using GitHub CLI
          gh pr create \
            --base develop \
            --head main \
            --title "$pr_title" \
            --body-file pr_body.md

      - name: Send message to Slack channel
        id: slack
        if: always()
        continue-on-error: true
        uses: slackapi/slack-github-action@b0fa283ad8fea605de13dc3f449259339835fc52  # v2.1.0
        env:
          PROJECT_NAME: 'JS SDK Monorepo'
          TAG_COMPARE_URL: 'https://github.com/rudderlabs/rudder-sdk-js/compare/'
          RELEASES_URL: 'https://github.com/rudderlabs/rudder-sdk-js/releases/tag/'
          GITHUB_RUN_URL: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
          ACTOR_URL: ${{ format('{0}/{1}', github.server_url, github.actor) }}
          ACTOR: ${{ github.actor }}
        with:
          method: chat.postMessage
          payload-templated: true
          retries: rapid
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload: |
            {
              "channel": "${{ secrets.SLACK_RELEASE_CHANNEL_ID }}",
              "text": "*:rocket: New Release - ${{ env.PROJECT_NAME }} - <${{ env.TAG_COMPARE_URL }}${{ env.last_monorepo_version }}...v${{ steps.extract-version.outputs.release_version }}|v${{ steps.extract-version.outputs.release_version }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SHJ20350>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rocket: New Release - ${{ env.PROJECT_NAME }}"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{ env.TAG_COMPARE_URL }}${{ env.last_monorepo_version }}...v${{ steps.extract-version.outputs.release_version }}|v${{ steps.extract-version.outputs.release_version }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SW7DM8P3> <!subteam^S03SHJ20350>"
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
                    "alt_text": "GitHub Icon"
                  }
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": ":boom: ${{ needs.get-release-inputs.outputs.trigger_source }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": ":gear: <${{ env.GITHUB_RUN_URL }}|View workflow run details>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "For more details, check the full release notes <${{ env.RELEASES_URL }}v${{ steps.extract-version.outputs.release_version }}|here>."
                    }
                  ]
                }
              ]
            }

  publish-npm-packages:
    needs: [get-release-inputs, release]
    name: Publish packages to NPM
    uses: ./.github/workflows/deploy-npm.yml
    with:
      is_called: 'true'
      trigger_source: ${{ needs.get-release-inputs.outputs.trigger_source }}
      monorepo_release_version: ${{ needs.get-release-inputs.outputs.release_version }}
      release_ticket_id: ${{ needs.get-release-inputs.outputs.release_ticket_id }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      RS_PROD_BUGSNAG_API_KEY: ${{ secrets.RS_PROD_BUGSNAG_API_KEY }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      SLACK_RELEASE_CHANNEL_ID: ${{ secrets.SLACK_RELEASE_CHANNEL_ID }}
