name: Deploy Sanity Suite

on:
  workflow_call:
    inputs:
      environment:
        type: string
        required: true
      use_pr_head_sha:
        type: boolean
        default: false
      beta_identifier:
        type: string
        required: false
      trigger_source:
        description: 'Description of what triggered this workflow'
        type: string
        required: true
      monorepo_release_version:
        description: 'Monorepo release version for artifact lookup'
        type: string
        required: false
      release_ticket_id:
        description: 'Release ticket for artifact lookup'
        type: string
        required: false
    secrets:
      AWS_ACCOUNT_ID:
        required: true
      AWS_S3_BUCKET_NAME:
        required: true
      AWS_S3_SYNC_ROLE:
        required: true
      AWS_CF_DISTRIBUTION_ID:
        required: true
      SANITY_SUITE_WRITE_KEY:
        required: true
      SANITY_SUITE_DATAPLANE_URL:
        required: true
      SANITY_SUITE_CONFIG_SERVER_HOST:
        required: true
      BUGSNAG_API_KEY:
        required: true
      SLACK_BOT_TOKEN:
        required: true
      SLACK_RELEASE_CHANNEL_ID:
        required: true

permissions:
  id-token: write # allows the JWT to be requested from GitHub's OIDC provider
  contents: read # This is required for actions/checkout

env:
  NODE_OPTIONS: '--no-warnings'
  CACHE_CONTROL: '"max-age=3600"'

jobs:
  deploy:
    name: Deploy Sanity Suite
    runs-on: [self-hosted, Linux, X64]

    steps:
      - name: Determine checkout SHA
        id: getSHA
        run: |
          if ${{ inputs.use_pr_head_sha }}; then
            sha=$(echo ${{ github.event.pull_request.head.sha }})
          else
            sha=$(echo ${{ github.sha }})
          fi
          echo "Checkout SHA: $sha"
          echo "SHA=$sha" >> $GITHUB_OUTPUT

      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          ref: ${{ steps.getSHA.outputs.SHA }}

      - name: Configure deployment options
        id: deployment-options
        run: |
          current_version=$(jq -r .version packages/sanity-suite/package.json)
          echo "CURRENT_VERSION_VALUE=$current_version" >> $GITHUB_ENV
          echo "DATE=$(date)" >> $GITHUB_ENV
          echo "BUGSNAG_RELEASE_STAGE=${{ inputs.environment }}" >> $GITHUB_ENV

          if [ "${{ inputs.environment }}" == "staging" ]; then
            echo "SDK_CDN_VERSION_PATH_PREFIX=staging/latest/" >> $GITHUB_ENV
            echo "SUITE_CDN_PATH=/staging" >> $GITHUB_ENV
          elif [ "${{ inputs.environment }}" == "production" ]; then
            echo "SDK_CDN_VERSION_PATH_PREFIX=" >> $GITHUB_ENV
            echo "SUITE_CDN_PATH=" >> $GITHUB_ENV
          elif [ "${{ inputs.environment }}" == "beta" ]; then
            echo "SDK_CDN_VERSION_PATH_PREFIX=beta/${{ inputs.beta_identifier }}/" >> $GITHUB_ENV
            echo "SUITE_CDN_PATH=/beta/${{ inputs.beta_identifier }}" >> $GITHUB_ENV
          else
            echo "SDK_CDN_VERSION_PATH_PREFIX=dev/latest/" >> $GITHUB_ENV
            echo "SUITE_CDN_PATH=/dev" >> $GITHUB_ENV
          fi

      - name: Install AWS CLI
        uses: unfor19/install-aws-cli-action@2ff7a6968c81b173eaaef188869215870f44902b  # master

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df  # v4.2.1
        with:
          role-to-assume: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID }}:role/${{ secrets.AWS_S3_SYNC_ROLE }}
          aws-region: us-east-1

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Setup workspace
        env:
          HUSKY: 0
          REMOTE_MODULES_BASE_PATH: 'https://cdn.rudderlabs.com/${{ env.SDK_CDN_VERSION_PATH_PREFIX }}v3/modern/plugins'
          BUGSNAG_RELEASE_STAGE: ${{ env.BUGSNAG_RELEASE_STAGE }}
          BUGSNAG_API_KEY: ${{ secrets.BUGSNAG_API_KEY }}
          LOCK_DEPS_VERSION: ${{ inputs.environment == 'production' && 'true' || 'false' }}
        run: |
          npm run setup:ci

      - name: Build artifacts
        env:
          WRITE_KEY: ${{ secrets.SANITY_SUITE_WRITE_KEY }}
          DATAPLANE_URL: ${{ secrets.SANITY_SUITE_DATAPLANE_URL }}
          CONFIG_SERVER_HOST: ${{ secrets.SANITY_SUITE_CONFIG_SERVER_HOST }}
          SDK_CDN_VERSION_PATH_PREFIX: ${{ env.SDK_CDN_VERSION_PATH_PREFIX }}
          HUSKY: 0
        run: |
          npm run build:sanity

      - name: Update aggregator file with environment path
        run: |
          if [ -f "packages/sanity-suite/dist/all/index.html" ]; then
            echo "Updating all/index.html with SUITE_CDN_PATH: ${{ env.SUITE_CDN_PATH }}"
            sed -i 's|__ENV_PATH__|${{ env.SUITE_CDN_PATH }}|g' packages/sanity-suite/dist/all/index.html
            echo "Updated aggregator file successfully"
          else
            echo "Warning: all/index.html not found in dist directory"
          fi

      - name: Sync files to S3
        run: |
          aws s3 cp packages/sanity-suite/dist/ s3://${{ secrets.AWS_S3_BUCKET_NAME }}/sanity-suite${{ env.SUITE_CDN_PATH }}/ --recursive --cache-control ${{ env.CACHE_CONTROL }}

      - name: Create Cloudfront invalidation
        run: |
          AWS_MAX_ATTEMPTS=10 aws cloudfront create-invalidation --distribution-id ${{ secrets.AWS_CF_DISTRIBUTION_ID }} --paths "/sanity-suite${{ env.SUITE_CDN_PATH }}*"

      - name: Send message to Slack channel
        id: slack
        uses: slackapi/slack-github-action@b0fa283ad8fea605de13dc3f449259339835fc52  # v2.1.0
        continue-on-error: true
        env:
          PROJECT_NAME: ${{ format('Sanity Suite - {0}', (inputs.environment == 'production' && 'Production') || (inputs.environment == 'staging' && 'Staging') || (inputs.environment == 'development' && 'Development') || 'Beta') }}
          CDN_URL: 'https://cdn.rudderlabs.com/sanity-suite${{ env.SUITE_CDN_PATH }}/v3/cdn/index.html'
          LINK_TEXT: ${{ ((inputs.environment == 'development' && format('v{0} - Development', env.CURRENT_VERSION_VALUE)) || (inputs.environment == 'staging' && format('v{0} - Staging', env.CURRENT_VERSION_VALUE)) || (inputs.environment == 'beta' && format('v{0} - Beta', env.CURRENT_VERSION_VALUE)) || format('v{0}', env.CURRENT_VERSION_VALUE)) }}
          GITHUB_RUN_URL: ${{ format('{0}/{1}/actions/runs/{2}', github.server_url, github.repository, github.run_id) }}
          ACTOR_URL: ${{ format('{0}/{1}', github.server_url, github.actor) }}
          ACTOR: ${{ github.actor }}
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          retries: rapid
          payload-templated: true
          payload: |
            {
              "channel": "${{ secrets.SLACK_RELEASE_CHANNEL_ID }}",
              "text": "*:test_tube: Deployment - ${{ env.PROJECT_NAME }} - <${{ env.CDN_URL }}|${{ env.LINK_TEXT }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SW7DM8P3> <!subteam^S03SHJ20350>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":test_tube: Deployment - ${{ env.PROJECT_NAME }}"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{ env.CDN_URL }}|${{ env.LINK_TEXT }}>*\n${{ env.DATE }}\nCC: <!subteam^S0555JBV36D> <!subteam^S03SW7DM8P3> <!subteam^S03SHJ20350>"
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://cdn.jsdelivr.net/npm/programming-languages-logos/src/javascript/javascript.png",
                    "alt_text": "JavaScript Icon"
                  }
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": ":boom: ${{ inputs.trigger_source }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": ":gear: <${{ env.GITHUB_RUN_URL }}|View workflow run details>"
                    }
                  ]
                }
              ]
            }

      - name: Post deployment link to release thread
        continue-on-error: true
        uses: ./.github/actions/release-thread-integration
        if: ${{ steps.slack.outcome == 'success' && inputs.monorepo_release_version != '' && inputs.release_ticket_id != '' }}
        with:
          monorepo_release_version: ${{ inputs.monorepo_release_version }}
          release_ticket_id: ${{ inputs.release_ticket_id }}
          slack_bot_token: ${{ secrets.SLACK_BOT_TOKEN }}
          deployment_name: 'Sanity Suite'
          slack_api_response: ${{ steps.slack.outputs.response }}
