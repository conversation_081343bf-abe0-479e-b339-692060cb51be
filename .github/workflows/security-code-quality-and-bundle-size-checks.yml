name: Security, Code Quality and Bundle Size Checks

on:
  pull_request:
    branches: ['develop', 'main', 'hotfix/*']
    types: ['opened', 'reopened', 'synchronize']

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_OPTIONS: '--no-warnings'
  BASE_REF: ${{ github.event.pull_request.base.sha || 'HEAD' }}

jobs:
  bundle-size-checks:
    name: Bundle size checks
    runs-on: [self-hosted, Linux, X64]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Execute bundle size checks
        uses: rudderlabs/github-action-check-size-limit@v2.13.0
        env:
          HUSKY: 0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          install_script: setup:ci
          build_script: check:size:build
          script: npm run check:size:json:ci --silent -- --output-style=static --silent=true --exclude=@rudderstack/analytics-js-sanity-suite
          is_monorepo: true

  security-code-quality-checks:
    name: Security and code quality checks
    runs-on: [self-hosted, Linux, X64]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Setup workspace
        env:
          HUSKY: 0
        run: |
          npm run setup:ci

      - name: Execute code quality checks
        run: |
          npm run check:circular
          npm run check:duplicates

      - name: Build the project
        run: |
          npm run build:ci
          npm run build:modern:ci

      - name: Execute package linting checks
        run: |
          npm run check:pub:ci

      - name: Execute security checks
        run: |
          npm run check:security
