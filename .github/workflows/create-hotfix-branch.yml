name: Create a new hotfix branch

on:
  workflow_dispatch:
    inputs:
      hotfix_branch_name:
        description: Hotfix branch name ('hotfix/' is automatically prepended)
        required: true

jobs:
  validate-actor:
    # Only allow creating hotfix branches from the main branch
    if: github.ref == 'refs/heads/main'
    uses: ./.github/workflows/validate-actor.yml
    with:
      team_names: 'js-sdk,integrations'
    secrets:
      PAT: ${{ secrets.PAT }}

  create-branch:
    name: Create a new hotfix branch
    runs-on: [self-hosted, Linux, X64]
    needs: validate-actor
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # In order to make a commit, we need to initialize a user.
      # You may choose to write something less generic here if you want, it doesn't matter functionality wise.
      - name: Initialize mandatory git config
        run: |
          git config user.name "GitHub actions"
          git config user.email <EMAIL>

      - name: Create hotfix branch
        run: |
          chmod +x scripts/create-hotfix-branch.sh
          ./scripts/create-hotfix-branch.sh "${{ github.event.inputs.hotfix_branch_name }}"
