name: Check PR Title

on:
  pull_request:
    branches: ['main', 'develop', 'hotfix/*']
    types: ['opened', 'reopened', 'edited', 'synchronize']

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  check_pr_title:
    name: Check PR title
    runs-on: [self-hosted, Linux, X64]
    steps:
      - name: Check PR title
        uses: rudderlabs/github-action-check-pr-title@v1.0.11
