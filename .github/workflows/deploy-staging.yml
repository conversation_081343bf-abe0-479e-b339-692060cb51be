name: Deploy to Staging Environment

on:
  pull_request:
    branches: ['main']
    types:
      - opened
      - synchronize
      - reopened

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  get-deploy-inputs:
    name: Get Deploy Inputs
    runs-on: [self-hosted, Linux, X64]
    outputs:
      trigger_source: ${{ steps.set-outputs.outputs.trigger_source }}
      release_version: ${{ steps.extract-release-info.outputs.release_version }}
      release_ticket_id: ${{ steps.extract-release-info.outputs.release_ticket_id }}
    steps:
      - name: Set outputs
        id: set-outputs
        run: echo "trigger_source=${{ format('Triggered via PR <{0}|#{1}> by <{2}|{3}>', github.event.pull_request.html_url, github.event.pull_request.number, format('{0}/{1}', github.server_url, github.actor), github.actor) }}" >> $GITHUB_OUTPUT

      - name: Extract release info from branch
        id: extract-release-info
        if: startsWith(github.head_ref, 'release/') || startsWith(github.head_ref, 'hotfix-release/')
        env:
          HEAD_REF: ${{ github.head_ref }}
        run: |
          echo "Branch name: $HEAD_REF"
          
          # Extract version and ticket from branch name (format: release/3.2.1-SDK-1234 or hotfix-release/3.2.2-SDK-5678)
          version=$(echo "$HEAD_REF" | sed -n 's|.*\/\([0-9]\+\.[0-9]\+\.[0-9]\+\)-.*|\1|p')
          ticket=$(echo "$HEAD_REF" | sed -n 's|.*-\([^-]*\)$|\1|p')
          
          echo "Extracted version: $version"
          echo "Extracted ticket: $ticket"
          
          echo "release_version=$version" >> $GITHUB_OUTPUT
          echo "release_ticket_id=$ticket" >> $GITHUB_OUTPUT

  deploy:
    name: Deploy to staging environment
    needs: [get-deploy-inputs]
    # Only allow running this workflow for PRs raised from release branches to main
    if: startsWith(github.head_ref, 'hotfix-release/') || startsWith(github.head_ref, 'release/')
    uses: ./.github/workflows/deploy.yml
    with:
      environment: 'staging'
      bugsnag_release_stage: 'staging'
      s3_dir_path: 'staging/latest/v3'
      s3_dir_path_legacy: 'staging/latest/v1.1'
      use_pr_head_sha: true
      trigger_source: ${{ needs.get-deploy-inputs.outputs.trigger_source }}
      monorepo_release_version: ${{ needs.get-deploy-inputs.outputs.release_version }}
      release_ticket_id: ${{ needs.get-deploy-inputs.outputs.release_ticket_id }}
    secrets:
      AWS_ACCOUNT_ID: ${{ secrets.AWS_STAGING_ACCOUNT_ID }}
      AWS_S3_BUCKET_NAME: ${{ secrets.AWS_STAGING_S3_BUCKET_NAME }}
      AWS_S3_SYNC_ROLE: ${{ secrets.AWS_STAGING_S3_SYNC_ROLE }}
      AWS_CF_DISTRIBUTION_ID: ${{ secrets.AWS_PROD_CF_DISTRIBUTION_ID }}
      BUGSNAG_API_KEY: ${{ secrets.RS_PROD_BUGSNAG_API_KEY }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      SLACK_RELEASE_CHANNEL_ID: ${{ secrets.SLACK_RELEASE_CHANNEL_ID_NON_PROD }}

  deploy-sanity-suite:
    name: Deploy sanity suite
    needs: [get-deploy-inputs]
    uses: ./.github/workflows/deploy-sanity-suite.yml
    with:
      environment: 'staging'
      use_pr_head_sha: true
      trigger_source: ${{ needs.get-deploy-inputs.outputs.trigger_source }}
      monorepo_release_version: ${{ needs.get-deploy-inputs.outputs.release_version }}
      release_ticket_id: ${{ needs.get-deploy-inputs.outputs.release_ticket_id }}
    secrets:
      AWS_ACCOUNT_ID: ${{ secrets.AWS_STAGING_ACCOUNT_ID }}
      AWS_S3_BUCKET_NAME: ${{ secrets.AWS_STAGING_S3_BUCKET_NAME }}
      AWS_S3_SYNC_ROLE: ${{ secrets.AWS_STAGING_S3_SYNC_ROLE }}
      AWS_CF_DISTRIBUTION_ID: ${{ secrets.AWS_STAGING_CF_DISTRIBUTION_ID }}
      SANITY_SUITE_WRITE_KEY: ${{ secrets.SANITY_SUITE_STAGING_WRITE_KEY }}
      SANITY_SUITE_DATAPLANE_URL: ${{ secrets.SANITY_SUITE_STAGING_DATAPLANE_URL }}
      SANITY_SUITE_CONFIG_SERVER_HOST: ${{ secrets.SANITY_SUITE_STAGING_CONFIG_SERVER_HOST }}
      BUGSNAG_API_KEY: ${{ secrets.RS_STAGING_BUGSNAG_API_KEY }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      SLACK_RELEASE_CHANNEL_ID: ${{ secrets.SLACK_RELEASE_CHANNEL_ID_NON_PROD }}

  run-e2e-regression-test-suites:
    uses: ./.github/workflows/run-e2e-regression-test-suites.yml
    name: Run E2E Regression Test Suites
    needs: [get-deploy-inputs, deploy-sanity-suite, deploy]
    with:
      environment: staging
      trigger_source: ${{ needs.get-deploy-inputs.outputs.trigger_source }}
      use_pr_head_sha: true
      monorepo_release_version: ${{ needs.get-deploy-inputs.outputs.release_version }}
      release_ticket_id: ${{ needs.get-deploy-inputs.outputs.release_ticket_id }}
    secrets:
      PAT: ${{ secrets.PAT }}
