---
name: Bug report
about: Create a report to help us improve
title: 'BUG : <Title>'
labels: bug, open source
assignees: rudderlabs/js-sdk
---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:

1. Go to '...'
2. Click on '....'
3. <PERSON>roll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Additional Information (please complete the following information):**

- SDK installation type: NPM/CDN
- SDK CDN URL: (if applicable)
- SDK version: (for NPM installation)
- Node version: (if applicable)
- NPM version: (if applicable)
- TypeScript version: (if applicable)
- Webpage URL: (where the SDK is installed, if applicable)
- Share the event payload: (if applicable)
- Integration that has the issue: (if applicable)
- Framework and version(e.g: Next.js, React, Vue): (if applicable)
- Bundling toolset (e.g: Webpack, Rollup): (if applicable)

**Desktop (please complete the following information):**

- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**Smartphone (please complete the following information):**

- Device: [e.g. iPhone6]
- OS: [e.g. iOS8.1]
- Browser [e.g. stock browser, safari]
- Version [e.g. 22]

**Additional context**
Add any other context about the problem here.
