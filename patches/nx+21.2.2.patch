diff --git a/node_modules/nx/src/command-line/yargs-utils/shared-options.js b/node_modules/nx/src/command-line/yargs-utils/shared-options.js
index 6de380b..fd310c3 100644
--- a/node_modules/nx/src/command-line/yargs-utils/shared-options.js
+++ b/node_modules/nx/src/command-line/yargs-utils/shared-options.js
@@ -128,6 +128,10 @@ function withRunOptions(yargs) {
         type: 'boolean',
         hidden: true,
         alias: 'agents',
+    })
+        .options('silent', {
+        type: 'boolean',
+        default: false,
     });
 }
 function withTargetAndConfigurationOption(yargs, demandOption = true) {
diff --git a/node_modules/nx/src/tasks-runner/life-cycles/static-run-many-terminal-output-life-cycle.js b/node_modules/nx/src/tasks-runner/life-cycles/static-run-many-terminal-output-life-cycle.js
index f58f821..5660e84 100644
--- a/node_modules/nx/src/tasks-runner/life-cycles/static-run-many-terminal-output-life-cycle.js
+++ b/node_modules/nx/src/tasks-runner/life-cycles/static-run-many-terminal-output-life-cycle.js
@@ -23,6 +23,10 @@ class StaticRunManyTerminalOutputLifeCycle {
         this.allCompletedTasks = new Map();
     }
     startCommand() {
+        if (this.args.silent) {
+            return;
+        }
+
         if (this.tasks.length === 0) {
             return;
         }
@@ -47,6 +51,10 @@ class StaticRunManyTerminalOutputLifeCycle {
         output_1.output.addVerticalSeparatorWithoutNewLines('cyan');
     }
     endCommand() {
+        if (this.args.silent) {
+            return;
+        }
+
         output_1.output.addNewline();
         if (this.tasks.length === 0) {
             output_1.output.logSingleLine(`No tasks were run`);
@@ -99,8 +107,9 @@ class StaticRunManyTerminalOutputLifeCycle {
         }
     }
     printTaskTerminalOutput(task, cacheStatus, terminalOutput) {
+        const isSilent = this.args.silent === true;
         const args = (0, utils_1.getPrintableCommandArgsForTask)(task);
-        output_1.output.logCommandOutput(args.join(' '), cacheStatus, terminalOutput);
+        output_1.output.logCommandOutput(args.join(' '), cacheStatus, terminalOutput, isSilent);
     }
 }
 exports.StaticRunManyTerminalOutputLifeCycle = StaticRunManyTerminalOutputLifeCycle;
diff --git a/node_modules/nx/src/tasks-runner/run-command.js b/node_modules/nx/src/tasks-runner/run-command.js
index 0a3a2ea..860408a 100644
--- a/node_modules/nx/src/tasks-runner/run-command.js
+++ b/node_modules/nx/src/tasks-runner/run-command.js
@@ -689,7 +689,9 @@ async function invokeTasksRunner({ tasks, projectGraph, taskGraph, lifeCycle, nx
 function constructLifeCycles(lifeCycle) {
     const lifeCycles = [];
     lifeCycles.push(new store_run_information_life_cycle_1.StoreRunInformationLifeCycle());
-    lifeCycles.push(new nx_cloud_ci_message_life_cycle_1.NxCloudCIMessageLifeCycle());
+    if (lifeCycle.args?.silent !== true) {
+        lifeCycles.push(new nx_cloud_ci_message_life_cycle_1.NxCloudCIMessageLifeCycle());
+    }
     lifeCycles.push(lifeCycle);
     if (process.env.NX_PERF_LOGGING === 'true') {
         lifeCycles.push(new task_timings_life_cycle_1.TaskTimingsLifeCycle());
diff --git a/node_modules/nx/src/utils/output.js b/node_modules/nx/src/utils/output.js
index 7d8eb43..8e9c0fc 100644
--- a/node_modules/nx/src/utils/output.js
+++ b/node_modules/nx/src/utils/output.js
@@ -157,20 +157,22 @@ class CLIOutput {
         this.addNewline();
         this.addNewline();
     }
-    logCommandOutput(message, taskStatus, output) {
+    logCommandOutput(message, taskStatus, output, isSilent) {
         let commandOutputWithStatus = this.getCommandWithStatus(message, taskStatus);
         if (process.env.NX_SKIP_LOG_GROUPING !== 'true' &&
-            process.env.GITHUB_ACTIONS) {
+            process.env.GITHUB_ACTIONS && !isSilent) {
             const icon = this.getStatusIcon(taskStatus);
             commandOutputWithStatus = `${GH_GROUP_PREFIX}${icon} ${commandOutputWithStatus}`;
         }
-        this.addNewline();
-        this.writeToStdOut(commandOutputWithStatus);
-        this.addNewline();
-        this.addNewline();
+        if (!isSilent) {
+            this.addNewline();
+            this.writeToStdOut(commandOutputWithStatus);
+            this.addNewline();
+            this.addNewline();
+        }
         this.writeToStdOut(output);
         if (process.env.NX_SKIP_LOG_GROUPING !== 'true' &&
-            process.env.GITHUB_ACTIONS) {
+            process.env.GITHUB_ACTIONS && !isSilent) {
             this.writeToStdOut(GH_GROUP_SUFFIX);
         }
     }
