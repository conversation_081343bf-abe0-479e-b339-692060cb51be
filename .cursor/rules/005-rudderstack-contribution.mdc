---
description: RudderStack contribution standards and workflow patterns for consistent development across projects
globs: 
alwaysApply: false
---
# RudderStack Contribution Standards

**Critical**: Follow RudderStack company-wide contribution patterns

## Contribution Workflow
- **Issues First**: Create or link to issue before starting work
- **Branch Naming**: Follow RudderStack conventions (feature/, fix/, docs/)
- **PR Templates**: Use provided PR templates consistently
- **Code Review**: All changes require review from RudderStack team members

## Standards Across Projects
- **Consistent Documentation**: Follow RudderStack documentation patterns
- **Security**: Follow security guidelines for customer data handling
- **Testing**: Comprehensive testing for customer-facing changes
- **Backwards Compatibility**: Maintain compatibility unless explicitly breaking

**Git Workflow**: [.memorybank/04_git_workflow_cicd.md](mdc:.memorybank/04_git_workflow_cicd.md)
**Task Workflow**: [.memorybank/06_task_implementation_workflow.md](mdc:.memorybank/06_task_implementation_workflow.md)
