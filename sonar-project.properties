sonar.log.level=INFO
sonar.verbose=false
sonar.qualitygate.wait=false

# Project details
sonar.projectKey=rudderlabs_repo_rudder-sdk-js
sonar.organization=rudderlabs
sonar.projectName=rudder-sdk-js
sonar.projectVersion=3.90.0

# Meta-data for the project
sonar.links.scm=https://github.com/rudderlabs/rudder-sdk-js
sonar.links.issue=https://github.com/rudderlabs/rudder-sdk-js/issues

# Path to reports
sonar.javascript.lcov.reportPaths=packages/analytics-js/reports/coverage/lcov.info,packages/analytics-js-plugins/reports/coverage/lcov.info,packages/analytics-js-common/reports/coverage/lcov.info,packages/analytics-js-integrations/reports/coverage/lcov.info,packages/analytics-js-service-worker/reports/coverage/lcov.info,packages/analytics-v1.1/reports/coverage/lcov.info,packages/analytics-js-cookies/reports/coverage/lcov.info
sonar.testExecutionReportPaths=packages/analytics-js/reports/sonar/results-report.xml,packages/analytics-js-plugins/reports/sonar/results-report.xml,packages/analytics-js-common/reports/sonar/results-report.xml,packages/analytics-js-integrations/reports/sonar/results-report.xml,packages/analytics-js-service-worker/reports/sonar/results-report.xml,packages/analytics-v1.1/reports/sonar/results-report.xml,packages/analytics-js-cookies/reports/sonar/results-report.xml
sonar.eslint.reportPaths=packages/analytics-js/reports/eslint.json,packages/analytics-js-plugins/reports/eslint.json,packages/analytics-js-common/reports/eslint.json,packages/analytics-js-integrations/reports/eslint.json,packages/analytics-js-service-worker/reports/eslint.json,packages/analytics-v1.1/reports/eslint.json,packages/analytics-js-cookies/reports/eslint.json

# Path to sources
sonar.sources=packages
sonar.inclusions=**/src/**/*.js, **/src/**/*.ts
sonar.exclusions=**/*.json,**/*.html,**/*.png,**/*.jpg,**/*.gif,**/*.svg,**/nativeSdkLoader.js

# Path to tests
sonar.tests=packages
sonar.test.inclusions=**/__tests__/**/*
sonar.test.exclusions=**/*.json,**/*.html,**/*.png,**/*.jpg,**/*.gif,**/*.svg

# Coverage config
sonar.coverage.exclusions=**/__tests__/**/*,__mocks__/**/*,**/__fixtures__/**/*,examples/**/*,**/*.json,**/*.html,**/*.png,**/*.jpg,**/*.gif,**/*.svg,**/nativeSdkLoader.js,packages/analytics-js-common/src/constants/integrations/**/*

# Source encoding
sonar.sourceEncoding=UTF-8

# Exclusions for copy-paste detection
sonar.cpd.exclusions=**/__tests__/**/*,__mocks__/**/*,**/__mocks__/**/*,**/__fixtures__/**/*,examples/**/*,**/nativeSdkLoader.js,assets/**/*,packages/analytics-js-common/src/constants/integrations/**/*,packages/loading-scripts/**/*,packages/sanity-suite/**/*
